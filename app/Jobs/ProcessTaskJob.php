<?php

namespace App\Jobs;

use App\Models\Task;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class ProcessTaskJob implements ShouldQueue
{
    use Queueable;

    public int $tries = 3;

    public int $maxExceptions = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Task $task
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // 检查任务状态
        if ($this->task->status !== Task::STATUS_PENDING) {
            Log::warning('任务状态不是 pending，跳过处理', [
                'task_id' => $this->task->id,
                'status' => $this->task->status,
            ]);

            return;
        }

        try {
            // 获取拥有任务的模型
            $taskable = $this->task->taskable;

            if (! $taskable) {
                throw new \Exception('任务关联的模型不存在');
            }

            // 调用模型的 handleTask 方法
            $taskable->handleTask($this->task);

            Log::info('任务处理成功', [
                'task_id' => $this->task->id,
                'task_type' => $this->task->type,
                'taskable_type' => $this->task->taskable_type,
                'taskable_id' => $this->task->taskable_id,
            ]);

        } catch (\Exception $e) {
            Log::error('队列任务处理失败', [
                'task_id' => $this->task->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 如果任务还未标记为失败，则标记为失败
            if ($this->task->fresh()->status !== Task::STATUS_FAILED) {
                $this->task->markAsFailed($e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('队列任务最终失败', [
            'task_id' => $this->task->id,
            'error' => $exception->getMessage(),
        ]);

        // 确保任务标记为失败
        if ($this->task->fresh()->status !== Task::STATUS_FAILED) {
            $this->task->markAsFailed($exception->getMessage());
        }
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return 'task-'.$this->task->id;
    }
}
