<?php

namespace App\Service;

use App\DTOs\StatefulSetDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class StatefulSetService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 StatefulSet 列表
     */
    public function getStatefulSets(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return StatefulSetDTO::fromK8sResource($item);
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 StatefulSet 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 StatefulSet
     */
    public function getStatefulSet(string $name): StatefulSetDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}");

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 StatefulSet
     */
    public function createStatefulSet(array $data): StatefulSetDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        // 如果没有指定服务名称，创建 Headless Service
        if (empty($data['service_name'])) {
            $this->createHeadlessService($data['name']);
            $data['service_name'] = $data['name'].'-headless';
        } else {
            // 验证服务是否存在
            $this->validateService($data['service_name']);
        }

        $payload = $this->buildStatefulSetPayload($data);

        try {
            $response = $this->workspace->cluster->http()
                ->post("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets", $payload);

            Log::info('StatefulSet 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $data['name'],
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('创建 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $data['name'],
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 StatefulSet
     */
    public function updateStatefulSet(string $name, array $data): StatefulSetDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        try {
            // 先获取现有的 StatefulSet
            $existing = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}")
                ->json();

            // 构建更新的 payload
            $payload = $this->buildStatefulSetPayload($data, $existing);

            $response = $this->workspace->cluster->http()
                ->put("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}", $payload);

            Log::info('StatefulSet 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('更新 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 StatefulSet
     */
    public function deleteStatefulSet(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}");

            Log::info('StatefulSet 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('删除 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('删除 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 扩容/缩容 StatefulSet
     */
    public function scaleStatefulSet(string $name, int $replicas): StatefulSetDTO
    {
        if ($replicas < 0) {
            throw new \Exception('副本数不能小于 0');
        }

        try {
            // 使用 PATCH 方式更新副本数
            $patchData = [
                'spec' => [
                    'replicas' => $replicas,
                ],
            ];

            $response = $this->workspace->cluster->http()
                ->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])
                ->patch("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}", $patchData);

            Log::info('StatefulSet 扩容成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'replicas' => $replicas,
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('扩容 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'replicas' => $replicas,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('扩容 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 构建 StatefulSet 载荷
     */
    protected function buildStatefulSetPayload(array $data, ?array $existing = null): array
    {
        $containers = $this->buildContainers($data['containers'] ?? []);

        // 从容器的 volume_mounts 中收集所有卷信息
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        $volumes = $this->buildVolumes($allVolumeMounts, $data['containers'] ?? []);
        $imagePullSecrets = $this->buildImagePullSecrets($data['image_pull_secrets'] ?? []);

        $payload = [
            'apiVersion' => 'apps/v1',
            'kind' => 'StatefulSet',
            'metadata' => [
                'name' => $data['name'],
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $data['name']),
            ],
            'spec' => [
                'replicas' => $data['replicas'] ?? 1,
                'serviceName' => $data['service_name'] ?? $data['name'].'-headless',
                'updateStrategy' => [
                    'type' => $data['update_strategy'] ?? 'RollingUpdate',
                    'rollingUpdate' => [
                        'partition' => 0,
                    ],
                ],
                'selector' => [
                    'matchLabels' => [
                        'app' => $data['name'],
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => array_merge(
                            $this->workspace->buildDefaultLabels(null, $data['name']),
                            ['app' => $data['name']]
                        ),
                    ],
                    'spec' => [
                        'containers' => $containers,
                        'restartPolicy' => 'Always', // 强制设置为 Always，StatefulSet 不允许其他重启策略
                    ],
                ],
            ],
        ];

        // 添加卷
        if (! empty($volumes)) {
            $payload['spec']['template']['spec']['volumes'] = $volumes;
        }

        // 添加 image pull secrets
        if (! empty($imagePullSecrets)) {
            $payload['spec']['template']['spec']['imagePullSecrets'] = $imagePullSecrets;
        }

        // 添加卷声明模板
        if (! empty($volumeClaimTemplates)) {
            $payload['spec']['volumeClaimTemplates'] = $volumeClaimTemplates;
        }

        // 如果是更新，保留元数据和现有卷声明模板
        if ($existing) {
            $payload['metadata'] = array_merge($existing['metadata'], $payload['metadata']);
            // StatefulSet 的卷声明模板不能修改，保留原有的
            if (isset($existing['spec']['volumeClaimTemplates'])) {
                $payload['spec']['volumeClaimTemplates'] = $existing['spec']['volumeClaimTemplates'];
            }
        }

        return $payload;
    }

    /**
     * 构建容器配置（复用 DeploymentService 的逻辑）
     */
    protected function buildContainers(array $containers): array
    {
        return array_map(function ($container) {
            $containerSpec = [
                'name' => $container['name'],
                'image' => $container['image'],
                'stdin' => true,
                'tty' => true,
            ];

            // 添加工作目录
            if (! empty($container['working_dir'])) {
                $containerSpec['workingDir'] = $container['working_dir'];
            }

            // 添加 command 和 args
            if (! empty($container['command'])) {
                $containerSpec['command'] = $container['command'];
            }
            if (! empty($container['args'])) {
                $containerSpec['args'] = $container['args'];
            }

            // 添加端口
            if (! empty($container['ports'])) {
                $containerSpec['ports'] = array_map(function ($port) {
                    return [
                        'name' => $port['name'] ?? 'default',
                        'containerPort' => (int) $port['container_port'],
                        'protocol' => $port['protocol'] ?? 'TCP',
                    ];
                }, $container['ports']);
            }

            // 构建环境变量
            $envVars = [];
            $envFrom = [];

            // 添加直接环境变量
            if (! empty($container['env'])) {
                foreach ($container['env'] as $env) {
                    $envVars[] = [
                        'name' => $env['name'],
                        'value' => (string) $env['value'],
                    ];
                }
            }

            // 添加从 ConfigMap 引用的环境变量
            if (! empty($container['env_from_configmap'])) {
                foreach ($container['env_from_configmap'] as $envFromConfig) {
                    // 跳过无效配置（configmap_name 为空）
                    if (empty($envFromConfig['configmap_name'])) {
                        continue;
                    }

                    if (empty($envFromConfig['key'])) {
                        // 引用整个 ConfigMap - 使用 envFrom
                        $envFrom[] = [
                            'configMapRef' => [
                                'name' => $envFromConfig['configmap_name'],
                            ],
                        ];
                    } else {
                        // 引用 ConfigMap 中的特定键 - 使用 env
                        $envName = $envFromConfig['env_name'] ?: $envFromConfig['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'configMapKeyRef' => [
                                        'name' => $envFromConfig['configmap_name'],
                                        'key' => $envFromConfig['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            // 添加从 Secret 引用的环境变量
            if (! empty($container['env_from_secret'])) {
                foreach ($container['env_from_secret'] as $envFromSecret) {
                    // 跳过无效配置（secret_name 为空）
                    if (empty($envFromSecret['secret_name'])) {
                        continue;
                    }

                    if (empty($envFromSecret['key'])) {
                        // 引用整个 Secret - 使用 envFrom
                        $envFrom[] = [
                            'secretRef' => [
                                'name' => $envFromSecret['secret_name'],
                            ],
                        ];
                    } else {
                        // 引用 Secret 中的特定键 - 使用 env
                        $envName = $envFromSecret['env_name'] ?: $envFromSecret['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'secretKeyRef' => [
                                        'name' => $envFromSecret['secret_name'],
                                        'key' => $envFromSecret['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            if (! empty($envVars)) {
                $containerSpec['env'] = $envVars;
            }

            if (! empty($envFrom)) {
                $containerSpec['envFrom'] = $envFrom;
            }

            // 添加资源限制
            if (! empty($container['resources'])) {
                $resources = $this->buildResourceRequirements($container['resources']);
                if (! empty($resources)) {
                    $containerSpec['resources'] = $resources;
                }
            }

            // 添加健康检查探针
            if (! empty($container['liveness_probe'])) {
                $containerSpec['livenessProbe'] = $this->buildProbe($container['liveness_probe']);
            }
            if (! empty($container['readiness_probe'])) {
                $containerSpec['readinessProbe'] = $this->buildProbe($container['readiness_probe']);
            }
            if (! empty($container['startup_probe'])) {
                $containerSpec['startupProbe'] = $this->buildProbe($container['startup_probe']);
            }

            // 构建卷挂载
            $volumeMounts = [];

            // 添加存储卷挂载
            if (! empty($container['volume_mounts'])) {
                foreach ($container['volume_mounts'] as $mount) {
                    $volumeMount = [
                        'name' => $mount['storage_name'], // 使用storage_name作为name
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => $mount['read_only'] ?? false,
                    ];

                    // 添加subPath支持
                    if (! empty($mount['sub_path'])) {
                        $volumeMount['subPath'] = $mount['sub_path'];
                    }

                    $volumeMounts[] = $volumeMount;
                }
            }

            // 添加 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "configmap-{$mount['configmap_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            // 添加 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "secret-{$mount['secret_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            if (! empty($volumeMounts)) {
                $containerSpec['volumeMounts'] = $volumeMounts;
            }

            return $containerSpec;
        }, $containers);
    }

    /**
     * 构建资源需求
     */
    protected function buildResourceRequirements(array $resources): array
    {
        $result = [];

        if (isset($resources['memory']) || isset($resources['cpu'])) {
            $limits = [];
            $requests = [];

            if (isset($resources['memory'])) {
                $memory = (int) $resources['memory'];
                if ($memory < 512 || $memory % 512 !== 0) {
                    throw new \Exception('内存必须是 512Mi 的倍数且不小于 512Mi');
                }
                $limits['memory'] = $memory.'Mi';
                $requests['memory'] = (int) ($memory / 2).'Mi';
            }

            if (isset($resources['cpu'])) {
                $cpu = (int) $resources['cpu'];
                if ($cpu < 500 || $cpu % 500 !== 0) {
                    throw new \Exception('CPU 必须是 500m 的倍数且不小于 500m');
                }
                $limits['cpu'] = $cpu.'m';
                $requests['cpu'] = (int) ($cpu / 2).'m';
            }

            if (! empty($limits)) {
                $result['limits'] = $limits;
            }
            if (! empty($requests)) {
                $result['requests'] = $requests;
            }
        }

        return $result;
    }

    /**
     * 构建卷配置
     */
    protected function buildVolumes(array $volumeMounts, array $containers = []): array
    {
        $volumes = [];
        $processedStorages = [];
        $processedConfigMaps = [];
        $processedSecrets = [];

        // 处理存储卷挂载
        foreach ($volumeMounts as $mount) {
            $storageName = $mount['storage_name'];

            // 避免重复添加同一个存储卷
            if (! in_array($storageName, $processedStorages)) {
                $volumes[] = [
                    'name' => $storageName,
                    'persistentVolumeClaim' => [
                        'claimName' => $storageName,
                    ],
                ];
                $processedStorages[] = $storageName;
            }
        }

        // 处理 ConfigMap 和 Secret 卷
        foreach ($containers as $container) {
            // 处理 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $configMapName = $mount['configmap_name'];

                    if (! in_array($configMapName, $processedConfigMaps)) {
                        $volume = [
                            'name' => "configmap-{$configMapName}-volume",
                            'configMap' => [
                                'name' => $configMapName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['configMap']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['configMap']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedConfigMaps[] = $configMapName;
                    }
                }
            }

            // 处理 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $secretName = $mount['secret_name'];

                    if (! in_array($secretName, $processedSecrets)) {
                        $volume = [
                            'name' => "secret-{$secretName}-volume",
                            'secret' => [
                                'secretName' => $secretName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['secret']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['secret']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedSecrets[] = $secretName;
                    }
                }
            }
        }

        return $volumes;
    }

    /**
     * 构建卷声明模板
     */
    protected function buildVolumeClaimTemplates(array $templates): array
    {
        return array_map(function ($template) {
            $size = (int) $template['size'];

            // 验证存储大小
            if ($size < 512 || $size % 512 !== 0) {
                throw new \Exception('存储容量必须是 512Mi 的倍数且不小于 512Mi');
            }

            return [
                'metadata' => [
                    'name' => $template['name'],
                ],
                'spec' => [
                    'accessModes' => ['ReadWriteOnce'], // StatefulSet 通常使用 RWO
                    'storageClassName' => 'longhorn',
                    'resources' => [
                        'requests' => [
                            'storage' => $size.'Mi',
                        ],
                    ],
                ],
            ];
        }, $templates);
    }

    /**
     * 构建镜像拉取密钥
     */
    protected function buildImagePullSecrets(array $secrets): array
    {
        return array_map(function ($secret) {
            return ['name' => $secret];
        }, $secrets);
    }

    /**
     * 创建 Headless Service
     */
    protected function createHeadlessService(string $name): void
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Service',
            'metadata' => [
                'name' => $name.'-headless',
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name.'-headless'),
            ],
            'spec' => [
                'clusterIP' => 'None',
                'selector' => [
                    'app' => $name,
                ],
                'ports' => [
                    [
                        'port' => 80,
                        'targetPort' => 80,
                        'protocol' => 'TCP',
                        'name' => 'http',
                    ],
                ],
            ],
        ];

        try {
            $this->workspace->cluster->http()
                ->post("/api/v1/namespaces/{$this->workspace->namespace}/services", $payload);
        } catch (\Exception $e) {
            Log::warning('创建 Headless Service 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $name.'-headless',
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 Headless Service 失败：'.$e->getMessage());
        }
    }

    /**
     * 验证镜像拉取密钥是否存在
     */
    protected function validateImagePullSecrets(array $secrets): void
    {
        foreach ($secrets as $secretName) {
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$secretName}");
            } catch (\Exception $e) {
                throw new \Exception("镜像拉取密钥 '{$secretName}' 不存在");
            }
        }
    }

    /**
     * 验证存储卷挂载
     */
    protected function validateVolumeMounts(array $volumeMounts): void
    {
        foreach ($volumeMounts as $mount) {
            if (empty($mount['storage_name'])) {
                throw new \Exception('存储名称不能为空');
            }

            // 验证存储是否存在
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$mount['storage_name']}");
            } catch (\Exception $e) {
                throw new \Exception("存储 '{$mount['storage_name']}' 不存在");
            }
        }
    }

    /**
     * 验证卷声明模板
     */
    protected function validateVolumeClaimTemplates(array $templates): void
    {
        foreach ($templates as $template) {
            if (empty($template['name'])) {
                throw new \Exception('卷声明模板名称不能为空');
            }

            if (empty($template['size']) || ! is_numeric($template['size'])) {
                throw new \Exception('卷声明模板容量必须为数字');
            }
        }
    }

    /**
     * 验证服务是否存在
     */
    protected function validateService(string $serviceName): void
    {
        try {
            $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$serviceName}");
        } catch (\Exception $e) {
            throw new \Exception("服务 '{$serviceName}' 不存在");
        }
    }

    /**
     * 构建健康检查探针
     */
    protected function buildProbe(array $probe): array
    {
        $result = [];

        // 设置通用参数
        if (isset($probe['initial_delay_seconds'])) {
            $result['initialDelaySeconds'] = (int) $probe['initial_delay_seconds'];
        }
        if (isset($probe['period_seconds'])) {
            $result['periodSeconds'] = (int) $probe['period_seconds'];
        }
        if (isset($probe['timeout_seconds'])) {
            $result['timeoutSeconds'] = (int) $probe['timeout_seconds'];
        }
        if (isset($probe['success_threshold'])) {
            $result['successThreshold'] = (int) $probe['success_threshold'];
        }
        if (isset($probe['failure_threshold'])) {
            $result['failureThreshold'] = (int) $probe['failure_threshold'];
        }

        // 根据类型设置特定配置
        switch ($probe['type']) {
            case 'http':
                $httpGet = [
                    'path' => $probe['http_path'] ?? '/',
                    'port' => (int) ($probe['http_port'] ?? 80),
                    'scheme' => $probe['http_scheme'] ?? 'HTTP',
                ];

                if (! empty($probe['http_headers'])) {
                    $httpGet['httpHeaders'] = array_map(function ($header) {
                        return [
                            'name' => $header['name'],
                            'value' => $header['value'],
                        ];
                    }, $probe['http_headers']);
                }

                $result['httpGet'] = $httpGet;
                break;

            case 'tcp':
                $result['tcpSocket'] = [
                    'port' => (int) ($probe['tcp_port'] ?? 80),
                ];
                break;

            case 'exec':
                $result['exec'] = [
                    'command' => $probe['exec_command'] ?? [],
                ];
                break;
        }

        return $result;
    }
}
