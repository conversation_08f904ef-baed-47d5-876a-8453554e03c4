<?php

namespace App\Service;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class JwtService
{
    private ?string $privateKey = null;

    private ?string $publicKey = null;

    public function __construct()
    {
        $this->loadKeys();
    }

    /**
     * 生成 JWT token
     */
    public function generateToken(array $payload): string
    {
        $now = time();

        $tokenPayload = array_merge($payload, [
            'iat' => $now,
            'exp' => $now + config('websocket.jwt.ttl'),
            'iss' => config('app.name'),
        ]);

        return JWT::encode($tokenPayload, $this->getPrivateKey(), config('websocket.jwt.algorithm'));
    }

    /**
     * 验证 JWT token
     */
    public function verifyToken(string $token): array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->getPublicKey(), config('websocket.jwt.algorithm')));

            return (array) $decoded;
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Invalid JWT token: '.$e->getMessage());
        }
    }

    /**
     * 为用户生成 WebSocket 认证 token
     */
    public function generateWebSocketToken(int $userId, int $workspaceId, array $permissions = []): string
    {
        return $this->generateToken([
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'permissions' => $permissions,
            'type' => 'websocket_auth',
        ]);
    }

    /**
     * 生成 Pod 终端访问 token
     */
    public function generatePodTerminalToken(
        int $userId,
        int $workspaceId,
        string $podName,
        ?string $containerName = null,
        string $mode = 'shell'
    ): string {
        return $this->generateToken([
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'pod_name' => $podName,
            'container_name' => $containerName,
            'mode' => $mode, // 'shell' 或 'attach'
            'type' => 'pod_terminal',
        ]);
    }

    /**
     * 从内存缓存获取私钥
     */
    private function getPrivateKey(): string
    {
        if ($this->privateKey === null) {
            $this->loadKeys();
        }

        if ($this->privateKey === null) {
            throw new \RuntimeException('Keys not found.');
        }

        return $this->privateKey;
    }

    /**
     * 从内存缓存获取公钥
     */
    private function getPublicKey(): string
    {
        if ($this->publicKey === null) {
            $this->loadKeys();
        }

        if ($this->publicKey === null) {
            throw new \RuntimeException('Public key not found. Please run: php artisan websocket:generate-jwt-keys');
        }

        return $this->publicKey;
    }

    /**
     * 加载密钥到内存
     */
    private function loadKeys(): void
    {
        $privatePath = config('websocket.jwt.private_key_path');
        $publicPath = config('websocket.jwt.public_key_path');

        // 尝试从缓存获取
        $cacheKey = 'jwt_keys_'.md5($privatePath.$publicPath);
        $cached = Cache::get($cacheKey);

        if ($cached && isset($cached['private']) && isset($cached['public'])) {
            $this->privateKey = $cached['private'];
            $this->publicKey = $cached['public'];

            return;
        }

        // 从文件读取
        if (File::exists($privatePath) && File::exists($publicPath)) {
            $this->privateKey = File::get($privatePath);
            $this->publicKey = File::get($publicPath);

            // 缓存到内存中，避免频繁文件读取
            Cache::put($cacheKey, [
                'private' => $this->privateKey,
                'public' => $this->publicKey,
            ], 3600); // 缓存 1 小时
        }
    }

    /**
     * 清除密钥缓存
     */
    public function clearKeyCache(): void
    {
        $privatePath = config('websocket.jwt.private_key_path');
        $publicPath = config('websocket.jwt.public_key_path');
        $cacheKey = 'jwt_keys_'.md5($privatePath.$publicPath);

        Cache::forget($cacheKey);
        $this->privateKey = null;
        $this->publicKey = null;
    }
}
