<?php

namespace App\Service;

use App\Models\Workspace;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WorkspaceService
{
    /**
     * 生成唯一的 namespace 名称
     * 格式：ns-{random_unique_short_id}
     */
    public function generateUniqueNamespace(): string
    {
        do {
            // 生成 8 位随机字符串
            $randomId = Str::lower(Str::random(8));
            $namespace = "ns-{$randomId}";
        } while (Workspace::where('namespace', $namespace)->exists());

        return $namespace;
    }

    /**
     * 创建工作空间
     */
    public function createWorkspace(array $data): Workspace
    {
        return DB::transaction(function () use ($data) {
            // 生成唯一的 namespace
            $namespace = $this->generateUniqueNamespace();

            // 创建工作空间（模型事件会自动启动创建任务）
            return Workspace::create([
                'user_id' => $data['user_id'],
                'cluster_id' => $data['cluster_id'],
                'name' => $data['name'],
                'namespace' => $namespace,
                'status' => Workspace::STATUS_PENDING,
            ]);
        });
    }

    /**
     * 启动工作空间删除流程
     */
    public function startWorkspaceDeletion(Workspace $workspace): void
    {
        DB::transaction(function () use ($workspace) {
            // 设置工作空间状态为删除中
            $workspace->update(['status' => Workspace::STATUS_DELETING]);

            // 启动删除任务
            $workspace->startDeletionTask();
        });
    }

    /**
     * 验证工作空间名称是否唯一（在用户范围内）
     */
    public function isWorkspaceNameUnique(string $name, int $userId, ?int $excludeId = null): bool
    {
        $query = Workspace::where('user_id', $userId)
            ->where('name', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    /**
     * 重新创建工作空间的 namespace（用于重试失败的创建）
     */
    public function retryCreateNamespace(Workspace $workspace): void
    {
        $workspace->retryCreateNamespace();
    }

    /**
     * 检查 Kubernetes namespace 状态
     */
    public function checkKubernetesNamespaceStatus(Workspace $workspace): string
    {
        if (! $workspace->namespace) {
            return 'not_found';
        }

        try {
            $response = $workspace->cluster->http()->get("/api/v1/namespaces/{$workspace->namespace}");

            if ($response->successful()) {
                $namespace = $response->json();

                // 检查 namespace 的状态
                if (isset($namespace['status']['phase'])) {
                    $phase = $namespace['status']['phase'];

                    if ($phase === 'Terminating') {
                        return 'terminating';
                    }

                    if ($phase === 'Active') {
                        return 'active';
                    }
                }

                // 检查是否有删除时间戳
                if (isset($namespace['metadata']['deletionTimestamp'])) {
                    return 'terminating';
                }

                return 'active';
            }

            return 'not_found';
        } catch (\Exception $e) {
            // 404 错误表示 namespace 不存在
            if (str_contains($e->getMessage(), '404') || str_contains($e->getMessage(), 'not found')) {
                return 'not_found';
            }

            Log::error('检查 namespace 状态失败', [
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
                'cluster_id' => $workspace->cluster_id,
            ]);

            // 其他错误返回未知状态
            return 'unknown';
        }
    }

    public function deleteWorkspace(Workspace $workspace): bool
    {
        $namespaceService = app(NamespaceService::class);
        $namespaceService->deleteNamespace($workspace);

        return $workspace->delete();
    }
}
