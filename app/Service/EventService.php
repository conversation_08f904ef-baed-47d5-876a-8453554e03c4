<?php

namespace App\Service;

use App\DTOs\EventDTO;
use App\DTOs\NamespaceEventsDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class EventService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取命名空间下的所有事件
     */
    public function getNamespaceEvents(): NamespaceEventsDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/events");

            $items = $response->json('items', []);

            return NamespaceEventsDTO::fromK8sResource($items);
        } catch (\Exception $e) {
            Log::error('获取命名空间事件失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return new NamespaceEventsDTO;
        }
    }

    /**
     * 获取特定资源的事件
     */
    public function getResourceEvents(string $kind, string $name): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/events", [
                    'fieldSelector' => "involvedObject.name={$name},involvedObject.kind={$kind}",
                ]);

            $items = $response->json('items', []);
            $events = [];

            foreach ($items as $item) {
                $events[] = EventDTO::fromK8sResource($item);
            }

            return array_map(function (EventDTO $event) {
                return $event->toArray();
            }, $events);
        } catch (\Exception $e) {
            Log::error('获取资源事件失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'kind' => $kind,
                'name' => $name,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }
}
