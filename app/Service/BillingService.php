<?php

namespace App\Service;

use App\Service\Billing\BillingPipeline;
use App\Service\Billing\OverdueManagementService;

/**
 * 计费服务
 * 基于流水线架构的计费系统
 */
class BillingService
{
    public function __construct(
        protected BillingPipeline $billingPipeline,
        protected OverdueManagementService $overdueManagementService,
    ) {}

    /**
     * 执行所有集群的计费流水线
     */
    public function processAllClustersBilling(): array
    {
        return $this->billingPipeline->processAllClustersBilling();
    }

    /**
     * 处理单个集群的计费流水线
     */
    public function processClusterBilling($cluster): array
    {
        return $this->billingPipeline->processClusterBilling($cluster);
    }

    /**
     * 检查并恢复欠费工作空间
     */
    public function checkAndResumeOverdueWorkspaces(): array
    {
        return $this->overdueManagementService->checkAndResumeOverdueWorkspaces();
    }

    /**
     * 获取欠费统计
     */
    public function getOverdueStatistics(): array
    {
        return $this->overdueManagementService->getOverdueStatistics();
    }

    /**
     * 获取需要删除的过期工作空间
     */
    public function getWorkspacesForDeletion(?int $deletionDays = null): array
    {
        return $this->overdueManagementService->getWorkspacesForDeletion($deletionDays);
    }

    /**
     * 获取欠费通知对象
     */
    public function getWorkspacesForNotification(int $warningDays): array
    {
        return $this->overdueManagementService->getWorkspacesForNotification($warningDays);
    }
}
