<?php

namespace App\Service;

use App\Models\TopUpRecord;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected BalanceService $balanceService;

    protected PaymentManagerService $paymentManager;

    public function __construct(BalanceService $balanceService, PaymentManagerService $paymentManager)
    {
        $this->balanceService = $balanceService;
        $this->paymentManager = $paymentManager;
    }

    /**
     * 创建支付订单
     */
    public function createPayment(User $user, float $amount, string $gatewayIdentifier, array $extra = []): array
    {
        return $this->paymentManager->createPayment($gatewayIdentifier, $user, $amount, $extra);
    }

    /**
     * 查询支付状态
     */
    public function queryPaymentStatus(TopUpRecord $record): array
    {
        return $this->paymentManager->getPaymentStatus($record->payment_method, $record->transaction_number);
    }

    /**
     * 处理支付回调
     */
    public function handleCallback(string $gatewayIdentifier, array $callbackData): array
    {
        return $this->paymentManager->verifyCallback($gatewayIdentifier, $callbackData);
    }

    /**
     * 退款
     */
    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        return $this->paymentManager->refund($record->payment_method, $record, $amount, $reason);
    }

    /**
     * 获取支持的支付方式
     */
    public function getSupportedPaymentMethods(): array
    {
        return $this->paymentManager->getGatewayList();
    }

    /**
     * 创建待支付的充值记录
     */
    public function createPendingTopUpRecord(User $user, float $amount, string $paymentMethod, array $extra = []): TopUpRecord
    {
        $transactionNumber = $this->generateTransactionNumber();

        return TopUpRecord::create([
            'user_id' => $user->id,
            'transaction_number' => $transactionNumber,
            'amount' => $amount,
            'remaining_amount' => 0, // 待支付状态，剩余金额为0
            'status' => TopUpRecord::STATUS_PENDING,
            'payment_method' => $paymentMethod,
            'remark' => $extra['remark'] ?? null,
        ]);
    }

    /**
     * 标记支付成功
     */
    public function markPaymentSuccess(TopUpRecord $record, array $gatewayResponse = []): bool
    {
        if ($record->status !== TopUpRecord::STATUS_PENDING) {
            return false;
        }

        return DB::transaction(function () use ($record, $gatewayResponse) {
            $record->update([
                'status' => TopUpRecord::STATUS_COMPLETED,
                'remaining_amount' => $record->amount,
                'payment_gateway_response' => $gatewayResponse,
                'completed_at' => now(),
            ]);

            // 更新用户余额
            $record->user->increment('current_balance', $record->amount);

            // 使用支付网关处理成功逻辑
            $this->paymentManager->handlePaymentSuccess($record->payment_method, $record, $gatewayResponse);

            Log::info('支付成功处理完成', [
                'record_id' => $record->id,
                'user_id' => $record->user_id,
                'amount' => $record->amount,
                'payment_method' => $record->payment_method,
            ]);

            return true;
        });
    }

    /**
     * 标记支付失败
     */
    public function markPaymentFailed(TopUpRecord $record, array $gatewayResponse = []): bool
    {
        if ($record->status !== TopUpRecord::STATUS_PENDING) {
            return false;
        }

        $record->update([
            'status' => TopUpRecord::STATUS_FAILED,
            'payment_gateway_response' => $gatewayResponse,
        ]);

        // 使用支付网关处理失败逻辑
        $this->paymentManager->handlePaymentFailure($record->payment_method, $record, $gatewayResponse);

        Log::info('支付失败处理完成', [
            'record_id' => $record->id,
            'user_id' => $record->user_id,
            'amount' => $record->amount,
            'payment_method' => $record->payment_method,
        ]);

        return true;
    }

    /**
     * 处理兑换码充值
     */
    public function processRedeemCode(User $user, string $code, ?string $ipAddress = null, ?string $userAgent = null): array
    {
        $gateway = $this->paymentManager->getGateway('redeem_code');

        if (! $gateway) {
            throw new \Exception('兑换码功能未启用');
        }

        return $gateway->processRedeemCode($user, $code, $ipAddress, $userAgent);
    }

    /**
     * 处理手动充值
     */
    public function processManualTopUp(User $user, float $amount, array $extra = []): array
    {
        $gateway = $this->paymentManager->getGateway('manual');

        if (! $gateway) {
            throw new \Exception('手动充值功能未启用');
        }

        return $gateway->processManualTopUp($user, $amount, $extra);
    }

    /**
     * 生成流水号
     */
    private function generateTransactionNumber(): string
    {
        return 'TXN'.date('YmdHis').str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);
    }
}
