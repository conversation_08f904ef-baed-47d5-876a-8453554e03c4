<?php

namespace App\Service;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class ResourceCollectionService
{
    /**
     * 收集集群中所有工作空间的资源使用情况
     */
    public function collectAllWorkspacesUsage(Cluster $cluster): array
    {
        $workspaces = $cluster->workspaces()
            ->where('status', 'active')
            ->get();

        $results = [];

        foreach ($workspaces as $workspace) {
            try {
                $usage = $this->collectWorkspaceResourceUsage($cluster, $workspace);

                $results[] = [
                    'workspace_id' => $workspace->id,
                    'cluster_id' => $cluster->id,
                    'usage' => $usage,
                    'collected_at' => now()->toISOString(),
                ];

                Log::debug('工作空间资源使用收集完成', [
                    'workspace_id' => $workspace->id,
                    'workspace_name' => $workspace->name,
                    'usage' => $usage,
                ]);

            } catch (\Exception $e) {
                Log::error('收集工作空间资源使用失败', [
                    'workspace_id' => $workspace->id,
                    'workspace_name' => $workspace->name,
                    'error' => $e->getMessage(),
                ]);

                // 记录失败但继续处理其他工作空间
                $results[] = [
                    'workspace_id' => $workspace->id,
                    'cluster_id' => $cluster->id,
                    'usage' => null,
                    'error' => $e->getMessage(),
                    'collected_at' => now()->toISOString(),
                ];
            }
        }

        return $results;
    }

    /**
     * 收集工作空间资源使用情况
     */
    public function collectWorkspaceResourceUsage(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;

        $usage = [
            'memory_mi' => 0,
            'cpu_m' => 0,
            'storage_gi' => 0,
            'loadbalancer_count' => 0,
        ];

        // 收集 Pod 资源使用
        $podUsage = $this->collectPodUsage($cluster, $namespace, $workspace->name);
        $usage['memory_mi'] += $podUsage['memory_mi'];
        $usage['cpu_m'] += $podUsage['cpu_m'];

        // 收集存储使用
        $storageUsage = $this->collectStorageUsage($cluster, $namespace, $workspace->name);
        $usage['storage_gi'] += $storageUsage['storage_gi'];

        // 收集 LoadBalancer 服务数量
        $loadBalancerUsage = $this->collectLoadBalancerUsage($cluster, $namespace, $workspace->name);
        $usage['loadbalancer_count'] += $loadBalancerUsage['loadbalancer_count'];

        Log::debug('工作空间资源使用详情', [
            'workspace' => $workspace->name,
            'namespace' => $namespace,
            'pod_usage' => $podUsage,
            'storage_usage' => $storageUsage,
            'loadbalancer_usage' => $loadBalancerUsage,
            'total_usage' => $usage,
        ]);

        return $usage;
    }

    /**
     * 收集 Pod 资源使用
     */
    protected function collectPodUsage(Cluster $cluster, string $namespace, string $workspaceName): array
    {
        $usage = ['memory_mi' => 0, 'cpu_m' => 0];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/pods");
            $pods = $response->json()['items'] ?? [];

            foreach ($pods as $pod) {
                $labels = $pod['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 Pod
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 检查 Pod 状态
                $phase = $pod['status']['phase'] ?? '';
                if (! in_array($phase, ['Running', 'Pending'])) {
                    continue;
                }

                $containers = $pod['spec']['containers'] ?? [];

                foreach ($containers as $container) {
                    $resources = $container['resources'] ?? [];
                    $limits = $resources['limits'] ?? [];

                    // 统计内存使用 (单位: Mi)
                    if (isset($limits['memory'])) {
                        $memoryStr = $limits['memory'];
                        if (str_ends_with($memoryStr, 'Mi')) {
                            $usage['memory_mi'] += (int) str_replace('Mi', '', $memoryStr);
                        } elseif (str_ends_with($memoryStr, 'Gi')) {
                            $usage['memory_mi'] += (int) str_replace('Gi', '', $memoryStr) * 1024;
                        }
                    }

                    // 统计 CPU 使用 (单位: m)
                    if (isset($limits['cpu'])) {
                        $cpuStr = $limits['cpu'];
                        if (str_ends_with($cpuStr, 'm')) {
                            $usage['cpu_m'] += (int) str_replace('m', '', $cpuStr);
                        } else {
                            // 如果是核数，转换为毫核
                            $usage['cpu_m'] += (int) ((float) $cpuStr * 1000);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('收集Pod资源使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }

    /**
     * 收集存储使用
     */
    protected function collectStorageUsage(Cluster $cluster, string $namespace, string $workspaceName): array
    {
        $usage = ['storage_gi' => 0];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/persistentvolumeclaims");
            $pvcs = $response->json()['items'] ?? [];

            foreach ($pvcs as $pvc) {
                $labels = $pvc['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 PVC
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 检查 PVC 状态
                $phase = $pvc['status']['phase'] ?? '';
                if ($phase !== 'Bound') {
                    continue;
                }

                $requests = $pvc['spec']['resources']['requests'] ?? [];
                if (isset($requests['storage'])) {
                    $storageStr = $requests['storage'];
                    if (str_ends_with($storageStr, 'Gi')) {
                        $usage['storage_gi'] += (int) str_replace('Gi', '', $storageStr);
                    } elseif (str_ends_with($storageStr, 'Mi')) {
                        $usage['storage_gi'] += (int) str_replace('Mi', '', $storageStr) / 1024;
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('收集存储使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }

    /**
     * 收集 LoadBalancer 服务数量
     */
    protected function collectLoadBalancerUsage(Cluster $cluster, string $namespace, string $workspaceName): array
    {
        $usage = ['loadbalancer_count' => 0];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/services");
            $services = $response->json()['items'] ?? [];

            foreach ($services as $service) {
                $labels = $service['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 Service
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 只统计 LoadBalancer 类型的服务
                $serviceType = $service['spec']['type'] ?? '';
                if ($serviceType === 'LoadBalancer') {
                    $usage['loadbalancer_count']++;
                }
            }
        } catch (\Exception $e) {
            Log::error('收集LoadBalancer使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }
}
