<?php

namespace App\Service\Billing;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

/**
 * 存储计费服务
 * 负责收集和计算存储（PVC）的使用和费用
 */
class StorageBillingService
{
    /**
     * 收集工作空间的存储使用情况
     */
    public function collectStorageUsage(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;
        $workspaceName = $workspace->name;

        $usage = [
            'storage_gi' => 0,
            'pvc_count' => 0,
            'storage_details' => [],
        ];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/persistentvolumeclaims");

            if (! $response->successful()) {
                throw new \Exception('获取 PVCs 失败: '.$response->body());
            }

            $pvcs = $response->json()['items'] ?? [];

            foreach ($pvcs as $pvc) {
                $labels = $pvc['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 PVC
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 检查 PVC 状态
                $phase = $pvc['status']['phase'] ?? '';
                if ($phase !== 'Bound') {
                    continue;
                }

                $usage['pvc_count']++;
                $pvcName = $pvc['metadata']['name'] ?? '';
                $storageClass = $pvc['spec']['storageClassName'] ?? '';

                $requests = $pvc['spec']['resources']['requests'] ?? [];
                if (isset($requests['storage'])) {
                    $storageStr = $requests['storage'];
                    $storageGi = $this->parseStorageToGi($storageStr);
                    $usage['storage_gi'] += $storageGi;

                    $usage['storage_details'][] = [
                        'name' => $pvcName,
                        'storage_class' => $storageClass,
                        'size_gi' => $storageGi,
                        'status' => $phase,
                    ];
                }
            }

            Log::debug('存储使用收集完成', [
                'workspace' => $workspaceName,
                'namespace' => $namespace,
                'usage' => $usage,
            ]);

        } catch (\Exception $e) {
            Log::error('收集存储使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }

    /**
     * 计算存储使用费用
     */
    public function calculateStorageCosts(ClusterPricing $pricing, array $usage): array
    {
        // 存储费用计算 (月价格 -> 分钟价格)
        $storageGb = (string) ($usage['storage_gi'] ?? 0);
        $storagePricePerMinute = bcdiv((string) $pricing->storage_price_per_gb, '43200', 8); // 30天*24小时*60分钟
        $storageCost = bcmul($storageGb, $storagePricePerMinute, 8);

        return [
            'storage_cost' => $storageCost,
            'storage_usage_gb' => $storageGb,
            'price_per_gb_per_minute' => $storagePricePerMinute,
        ];
    }

    /**
     * 检查存储是否需要清理
     * 返回超过指定天数且关联工作空间已删除的PVC
     */
    public function getStorageForCleanup(Cluster $cluster, int $retentionDays = 7): array
    {
        $storageToCleanup = [];
        $cutoffDate = now()->subDays($retentionDays);

        try {
            // 获取所有 namespace 中的 PVC
            $response = $cluster->http()->get('/api/v1/persistentvolumeclaims');

            if (! $response->successful()) {
                throw new \Exception('获取集群 PVCs 失败: '.$response->body());
            }

            $pvcs = $response->json()['items'] ?? [];

            foreach ($pvcs as $pvc) {
                $labels = $pvc['metadata']['labels'] ?? [];
                $annotations = $pvc['metadata']['annotations'] ?? [];

                // 检查是否是我们平台管理的 PVC
                if (! isset($labels[ClusterLabel::WORKSPACE->value])) {
                    continue;
                }

                $workspaceName = $labels[ClusterLabel::WORKSPACE->value];
                $namespace = $pvc['metadata']['namespace'] ?? '';
                $pvcName = $pvc['metadata']['name'] ?? '';
                $creationTimestamp = $pvc['metadata']['creationTimestamp'] ?? '';

                // 检查对应的工作空间是否还存在
                $workspace = Workspace::where('name', $workspaceName)
                    ->where('namespace', $namespace)
                    ->first();

                if (! $workspace || $workspace->status === 'deleting') {
                    // 工作空间不存在或正在删除，检查是否超过保留期
                    $createdAt = \Carbon\Carbon::parse($creationTimestamp);

                    if ($createdAt->lt($cutoffDate)) {
                        $storageToCleanup[] = [
                            'namespace' => $namespace,
                            'name' => $pvcName,
                            'workspace_name' => $workspaceName,
                            'size' => $pvc['spec']['resources']['requests']['storage'] ?? '',
                            'created_at' => $createdAt->toISOString(),
                            'storage_class' => $pvc['spec']['storageClassName'] ?? '',
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('检查存储清理失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $storageToCleanup;
    }

    /**
     * 删除指定的 PVC
     */
    public function deletePvc(Cluster $cluster, string $namespace, string $pvcName): bool
    {
        try {
            $response = $cluster->http()->delete("/api/v1/namespaces/{$namespace}/persistentvolumeclaims/{$pvcName}");

            if ($response->successful()) {
                Log::info('PVC 删除成功', [
                    'namespace' => $namespace,
                    'pvc_name' => $pvcName,
                ]);

                return true;
            } else {
                Log::error('PVC 删除失败', [
                    'namespace' => $namespace,
                    'pvc_name' => $pvcName,
                    'response' => $response->body(),
                ]);

                return false;
            }

        } catch (\Exception $e) {
            Log::error('删除 PVC 异常', [
                'namespace' => $namespace,
                'pvc_name' => $pvcName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 解析存储字符串为 Gi 单位
     */
    protected function parseStorageToGi(string $storageStr): int
    {
        if (str_ends_with($storageStr, 'Gi')) {
            return (int) str_replace('Gi', '', $storageStr);
        } elseif (str_ends_with($storageStr, 'Mi')) {
            return (int) (str_replace('Mi', '', $storageStr) / 1024);
        } elseif (str_ends_with($storageStr, 'Ti')) {
            return (int) str_replace('Ti', '', $storageStr) * 1024;
        } elseif (preg_match('/^(\d+)$/', $storageStr, $matches)) {
            // 字节转换为 Gi
            return (int) $matches[1] / (1024 * 1024 * 1024);
        }

        return 0;
    }
}
