<?php

namespace App\Service\Billing;

use App\Models\Cluster;
use App\Models\Workspace;
use App\Service\ResourceCollectionService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 计费流水线
 * 定义计费处理的明确步骤和顺序
 */
class BillingPipeline
{
    public function __construct(
        protected ResourceCollectionService $resourceCollectionService,
        protected CostCalculationService $costCalculationService,
        protected BillingRecordService $billingRecordService,
        protected OverdueManagementService $overdueManagementService,
    ) {}

    /**
     * 执行完整的计费流水线
     *
     * 处理顺序：
     * 1. 资源收集阶段 (Resource Collection Phase)
     * 2. 成本计算阶段 (Cost Calculation Phase)
     * 3. 计费记录阶段 (Billing Record Phase)
     * 4. 余额扣费阶段 (Balance Deduction Phase)
     * 5. 欠费恢复阶段 (Overdue Recovery Phase)
     * 6. 欠费处理阶段 (Overdue Handling Phase)
     */
    public function processClusterBilling(Cluster $cluster): array
    {
        $startTime = now();

        Log::info('开始执行计费流水线', [
            'cluster_id' => $cluster->id,
            'cluster_name' => $cluster->name,
            'start_time' => $startTime->toISOString(),
        ]);

        try {
            // 阶段1: 资源收集
            $resourcePhaseResult = $this->executeResourceCollectionPhase($cluster);

            // 阶段2: 成本计算
            $costPhaseResult = $this->executeCostCalculationPhase($cluster, $resourcePhaseResult);

            // 阶段3: 计费记录创建
            $recordPhaseResult = $this->executeBillingRecordPhase($costPhaseResult);

            // 阶段4: 余额扣费
            $deductionPhaseResult = $this->executeBalanceDeductionPhase($recordPhaseResult);

            // 阶段5: 欠费恢复
            $recoveryPhaseResult = $this->executeOverdueRecoveryPhase($cluster);

            // 阶段6: 欠费处理
            $overduePhaseResult = $this->executeOverdueHandlingPhase($deductionPhaseResult);

            $endTime = now();
            $duration = $endTime->diffInSeconds($startTime);

            $result = [
                'success' => true,
                'cluster_id' => $cluster->id,
                'cluster_name' => $cluster->name,
                'processing_time_seconds' => $duration,
                'phases' => [
                    'resource_collection' => $resourcePhaseResult,
                    'cost_calculation' => $costPhaseResult,
                    'billing_record' => $recordPhaseResult,
                    'balance_deduction' => $deductionPhaseResult,
                    'overdue_recovery' => $recoveryPhaseResult,
                    'overdue_handling' => $overduePhaseResult,
                ],
                'summary' => $this->generatePipelineSummary([
                    $resourcePhaseResult,
                    $costPhaseResult,
                    $recordPhaseResult,
                    $deductionPhaseResult,
                    $recoveryPhaseResult,
                    $overduePhaseResult,
                ]),
            ];

            Log::info('计费流水线执行完成', [
                'cluster_id' => $cluster->id,
                'duration_seconds' => $duration,
                'summary' => $result['summary'],
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('计费流水线执行失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 阶段1: 资源收集阶段
     * 收集所有工作空间的资源使用情况
     */
    protected function executeResourceCollectionPhase(Cluster $cluster): array
    {
        Log::debug('执行阶段1: 资源收集', ['cluster_id' => $cluster->id]);

        $startTime = microtime(true);

        if (! $cluster->pricing || ! $cluster->pricing->billing_enabled) {
            return [
                'phase' => 'resource_collection',
                'success' => false,
                'reason' => 'billing_disabled',
                'workspaces_processed' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }

        $workspaceUsages = $this->resourceCollectionService->collectAllWorkspacesUsage($cluster);

        $successful = 0;
        $failed = 0;
        $skipped = 0;

        foreach ($workspaceUsages as $usage) {
            if (isset($usage['error'])) {
                $failed++;
            } elseif (($usage['usage']['memory_mi'] ?? 0) === 0 &&
                     ($usage['usage']['cpu_m'] ?? 0) === 0 &&
                     ($usage['usage']['storage_gi'] ?? 0) === 0 &&
                     ($usage['usage']['loadbalancer_count'] ?? 0) === 0) {
                $skipped++;
            } else {
                $successful++;
            }
        }

        return [
            'phase' => 'resource_collection',
            'success' => true,
            'workspaces_data' => $workspaceUsages,
            'workspaces_processed' => count($workspaceUsages),
            'successful_collections' => $successful,
            'failed_collections' => $failed,
            'skipped_collections' => $skipped,
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ];
    }

    /**
     * 阶段2: 成本计算阶段
     * 根据资源使用量计算每个工作空间的费用
     */
    protected function executeCostCalculationPhase(Cluster $cluster, array $resourcePhaseResult): array
    {
        Log::debug('执行阶段2: 成本计算', ['cluster_id' => $cluster->id]);

        $startTime = microtime(true);

        if (! $resourcePhaseResult['success']) {
            return [
                'phase' => 'cost_calculation',
                'success' => false,
                'reason' => 'resource_collection_failed',
                'workspaces_processed' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }

        $workspaceCosts = [];
        $totalCalculated = 0;
        $zeroCosts = 0;

        foreach ($resourcePhaseResult['workspaces_data'] as $workspaceUsage) {
            if (isset($workspaceUsage['error'])) {
                continue;
            }

            $costs = $this->costCalculationService->calculateWorkspaceCosts(
                $cluster->pricing,
                $workspaceUsage['usage']
            );

            $workspaceCosts[] = [
                'workspace_id' => $workspaceUsage['workspace_id'],
                'usage' => $workspaceUsage['usage'],
                'costs' => $costs,
            ];

            $totalCalculated++;

            if (bccomp($costs['total_cost'], '0', 8) === 0) {
                $zeroCosts++;
            }
        }

        return [
            'phase' => 'cost_calculation',
            'success' => true,
            'workspace_costs' => $workspaceCosts,
            'workspaces_processed' => $totalCalculated,
            'zero_cost_workspaces' => $zeroCosts,
            'billable_workspaces' => $totalCalculated - $zeroCosts,
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ];
    }

    /**
     * 阶段3: 计费记录阶段
     * 为每个工作空间创建计费记录
     */
    protected function executeBillingRecordPhase(array $costPhaseResult): array
    {
        Log::debug('执行阶段3: 计费记录创建');

        $startTime = microtime(true);

        if (! $costPhaseResult['success']) {
            return [
                'phase' => 'billing_record',
                'success' => false,
                'reason' => 'cost_calculation_failed',
                'records_created' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }

        $billingRecords = [];
        $recordsCreated = 0;
        $recordsFailed = 0;

        $intervalMinutes = config('billing.cycle.billing_interval_minutes', 1);
        $billingStartAt = now()->subMinutes($intervalMinutes)->toISOString();
        $billingEndAt = now()->toISOString();

        foreach ($costPhaseResult['workspace_costs'] as $workspaceCost) {
            // 只为有费用的工作空间创建记录
            if (bccomp($workspaceCost['costs']['total_cost'], '0', 8) <= 0) {
                continue;
            }

            try {
                $workspace = Workspace::find($workspaceCost['workspace_id']);
                if (! $workspace) {
                    $recordsFailed++;

                    continue;
                }

                $record = $this->billingRecordService->createBillingRecord(
                    $workspace,
                    $workspaceCost['usage'],
                    $workspaceCost['costs'],
                    $billingStartAt,
                    $billingEndAt
                );

                $billingRecords[] = [
                    'workspace_id' => $workspace->id,
                    'record_id' => $record->id,
                    'total_cost' => $record->total_cost,
                ];

                $recordsCreated++;

            } catch (\Exception $e) {
                Log::error('创建计费记录失败', [
                    'workspace_id' => $workspaceCost['workspace_id'],
                    'error' => $e->getMessage(),
                ]);
                $recordsFailed++;
            }
        }

        return [
            'phase' => 'billing_record',
            'success' => true,
            'billing_records' => $billingRecords,
            'records_created' => $recordsCreated,
            'records_failed' => $recordsFailed,
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ];
    }

    /**
     * 阶段4: 余额扣费阶段
     * 执行实际的余额扣费操作
     */
    protected function executeBalanceDeductionPhase(array $recordPhaseResult): array
    {
        Log::debug('执行阶段4: 余额扣费');

        $startTime = microtime(true);

        if (! $recordPhaseResult['success']) {
            return [
                'phase' => 'balance_deduction',
                'success' => false,
                'reason' => 'billing_record_failed',
                'deductions_processed' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }

        $deductionResults = [];
        $successfulDeductions = 0;
        $failedDeductions = 0;
        $totalDeductedAmount = '0.00000000';

        foreach ($recordPhaseResult['billing_records'] as $recordInfo) {
            try {
                $record = \App\Models\BillingRecord::find($recordInfo['record_id']);
                if (! $record) {
                    $failedDeductions++;

                    continue;
                }

                $result = $this->billingRecordService->chargeBillingRecord($record);

                $deductionResults[] = [
                    'workspace_id' => $recordInfo['workspace_id'],
                    'record_id' => $recordInfo['record_id'],
                    'success' => $result['success'],
                    'charged_amount' => $result['success'] ? $record->total_cost : '0.00000000',
                    'error' => $result['error'] ?? null,
                ];

                if ($result['success']) {
                    $successfulDeductions++;
                    $totalDeductedAmount = bcadd($totalDeductedAmount, (string) $record->total_cost, 8);
                } else {
                    $failedDeductions++;
                }

            } catch (\Exception $e) {
                Log::error('余额扣费失败', [
                    'record_id' => $recordInfo['record_id'],
                    'error' => $e->getMessage(),
                ]);
                $failedDeductions++;
            }
        }

        return [
            'phase' => 'balance_deduction',
            'success' => true,
            'deduction_results' => $deductionResults,
            'deductions_processed' => count($recordPhaseResult['billing_records']),
            'successful_deductions' => $successfulDeductions,
            'failed_deductions' => $failedDeductions,
            'total_deducted_amount' => $totalDeductedAmount,
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ];
    }

    /**
     * 阶段5: 欠费恢复阶段
     * 检查并恢复那些余额充足可以结清欠费的工作空间
     */
    protected function executeOverdueRecoveryPhase(Cluster $cluster): array
    {
        Log::debug('执行阶段5: 欠费恢复', ['cluster_id' => $cluster->id]);

        $startTime = microtime(true);

        try {
            // 调用恢复服务检查并恢复欠费工作空间
            $recoveryResults = $this->overdueManagementService->checkAndResumeOverdueWorkspaces();

            // 过滤出属于当前集群的恢复结果
            $clusterRecoveryResults = array_filter($recoveryResults, function ($result) use ($cluster) {
                if (! isset($result['workspace_id'])) {
                    return false;
                }

                $workspace = \App\Models\Workspace::find($result['workspace_id']);

                return $workspace && $workspace->cluster_id === $cluster->id;
            });

            $recoveryProcessed = count($clusterRecoveryResults);
            $recoverySuccessful = count(array_filter($clusterRecoveryResults, fn ($r) => $r['success'] ?? false));
            $recoveryFailed = $recoveryProcessed - $recoverySuccessful;

            Log::info('欠费恢复阶段完成', [
                'cluster_id' => $cluster->id,
                'total_processed' => $recoveryProcessed,
                'successful' => $recoverySuccessful,
                'failed' => $recoveryFailed,
            ]);

            return [
                'phase' => 'overdue_recovery',
                'success' => true,
                'recovery_results' => $clusterRecoveryResults,
                'recovery_processed' => $recoveryProcessed,
                'recovery_successful' => $recoverySuccessful,
                'recovery_failed' => $recoveryFailed,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];

        } catch (\Exception $e) {
            Log::error('欠费恢复阶段执行失败', [
                'cluster_id' => $cluster->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'phase' => 'overdue_recovery',
                'success' => false,
                'error' => $e->getMessage(),
                'recovery_processed' => 0,
                'recovery_successful' => 0,
                'recovery_failed' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }
    }

    /**
     * 阶段6: 欠费处理阶段
     * 处理扣费失败的工作空间欠费情况
     */
    protected function executeOverdueHandlingPhase(array $deductionPhaseResult): array
    {
        Log::debug('执行阶段6: 欠费处理');

        $startTime = microtime(true);

        if (! $deductionPhaseResult['success']) {
            return [
                'phase' => 'overdue_handling',
                'success' => false,
                'reason' => 'balance_deduction_failed',
                'overdue_processed' => 0,
                'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            ];
        }

        $overdueResults = [];
        $overdueProcessed = 0;
        $overdueFailed = 0;

        foreach ($deductionPhaseResult['deduction_results'] as $deductionResult) {
            // 只处理扣费失败且是余额不足的情况
            if ($deductionResult['success'] ||
                ! isset($deductionResult['error']) ||
                ! str_contains($deductionResult['error'], '余额不足')) {
                continue;
            }

            try {
                $record = \App\Models\BillingRecord::find($deductionResult['record_id']);
                if (! $record) {
                    $overdueFailed++;

                    continue;
                }

                $this->overdueManagementService->handleUserOverdue($record);

                $overdueResults[] = [
                    'workspace_id' => $deductionResult['workspace_id'],
                    'record_id' => $deductionResult['record_id'],
                    'overdue_amount' => $record->total_cost,
                    'processed' => true,
                ];

                $overdueProcessed++;

            } catch (\Exception $e) {
                Log::error('欠费处理失败', [
                    'record_id' => $deductionResult['record_id'],
                    'error' => $e->getMessage(),
                ]);

                $overdueResults[] = [
                    'workspace_id' => $deductionResult['workspace_id'],
                    'record_id' => $deductionResult['record_id'],
                    'processed' => false,
                    'error' => $e->getMessage(),
                ];

                $overdueFailed++;
            }
        }

        return [
            'phase' => 'overdue_handling',
            'success' => true,
            'overdue_results' => $overdueResults,
            'overdue_processed' => $overdueProcessed,
            'overdue_failed' => $overdueFailed,
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        ];
    }

    /**
     * 生成流水线执行摘要
     */
    protected function generatePipelineSummary(array $phases): array
    {
        $totalExecutionTime = array_sum(array_column($phases, 'execution_time_ms'));

        return [
            'total_execution_time_ms' => round($totalExecutionTime, 2),
            'phases_completed' => count(array_filter($phases, fn ($phase) => $phase['success'] ?? false)),
            'phases_failed' => count(array_filter($phases, fn ($phase) => ! ($phase['success'] ?? true))),
            'workspaces_with_resources' => $phases[0]['successful_collections'] ?? 0,
            'billable_workspaces' => $phases[1]['billable_workspaces'] ?? 0,
            'records_created' => $phases[2]['records_created'] ?? 0,
            'successful_charges' => $phases[3]['successful_deductions'] ?? 0,
            'recovered_workspaces' => $phases[4]['recovery_successful'] ?? 0,
            'overdue_workspaces' => $phases[5]['overdue_processed'] ?? 0,
            'total_revenue' => $phases[3]['total_deducted_amount'] ?? '0.00000000',
        ];
    }

    /**
     * 处理所有集群的计费
     */
    public function processAllClustersBilling(): array
    {
        $startTime = now();
        $results = [];

        $clusters = Cluster::with('pricing')->whereHas('pricing', function ($query) {
            $query->where('billing_enabled', true);
        })->get();

        Log::info('开始批量计费流水线', [
            'total_clusters' => $clusters->count(),
            'start_time' => $startTime->toISOString(),
        ]);

        foreach ($clusters as $cluster) {
            $results[$cluster->name] = $this->processClusterBilling($cluster);
        }

        $endTime = now();
        $totalDuration = $endTime->diffInSeconds($startTime);

        Log::info('批量计费流水线完成', [
            'total_duration_seconds' => $totalDuration,
            'clusters_processed' => count($results),
            'successful_clusters' => count(array_filter($results, fn ($r) => $r['success'])),
        ]);

        return [
            'start_time' => $startTime->toISOString(),
            'end_time' => $endTime->toISOString(),
            'total_duration_seconds' => $totalDuration,
            'clusters' => $results,
            'summary' => $this->generateBatchSummary($results),
        ];
    }

    /**
     * 生成批处理摘要
     */
    protected function generateBatchSummary(array $clusterResults): array
    {
        $totalWorkspaces = 0;
        $totalRevenue = '0.00000000';
        $totalRecovered = 0;
        $totalOverdue = 0;

        foreach ($clusterResults as $result) {
            if ($result['success']) {
                $summary = $result['summary'];
                $totalWorkspaces += $summary['billable_workspaces'];
                $totalRevenue = bcadd($totalRevenue, $summary['total_revenue'], 8);
                $totalRecovered += $summary['recovered_workspaces'];
                $totalOverdue += $summary['overdue_workspaces'];
            }
        }

        return [
            'total_billable_workspaces' => $totalWorkspaces,
            'total_revenue' => $totalRevenue,
            'total_recovered_workspaces' => $totalRecovered,
            'total_overdue_workspaces' => $totalOverdue,
            'successful_clusters' => count(array_filter($clusterResults, fn ($r) => $r['success'])),
            'failed_clusters' => count(array_filter($clusterResults, fn ($r) => ! $r['success'])),
        ];
    }
}
