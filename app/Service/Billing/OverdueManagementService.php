<?php

namespace App\Service\Billing;

use App\Events\UserOverdue;
use App\Events\UserOverdueRecovered;
use App\Jobs\ResumeWorkspaceResources;
use App\Jobs\SuspendWorkspaceResources;
use App\Models\BillingRecord;
use App\Models\Workspace;
use App\Service\BalanceService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 欠费管理服务
 * 负责处理用户欠费、工作空间暂停和恢复
 */
class OverdueManagementService
{
    public function __construct(
        protected BalanceService $balanceService
    ) {}

    /**
     * 处理用户欠费
     */
    public function handleUserOverdue(BillingRecord $record): void
    {
        $workspace = $record->workspace;
        $user = $record->user;

        if (! $workspace || ! $user) {
            Log::error('欠费处理失败：工作空间或用户不存在', [
                'record_id' => $record->id,
                'workspace_id' => $record->workspace_id,
                'user_id' => $record->user_id,
            ]);

            return;
        }

        // 验证计费记录的费用是否有效
        $totalCost = (string) $record->total_cost;
        if (! is_numeric($totalCost) || bccomp($totalCost, '0', 8) <= 0) {
            Log::error('计费记录费用无效，跳过欠费处理', [
                'record_id' => $record->id,
                'total_cost' => $totalCost,
            ]);

            return;
        }

        // 检查暂停保护期
        $protectionMinutes = config('billing.suspension.protection_minutes', 10);
        if ($workspace->suspended_at &&
            $workspace->suspended_at->diffInMinutes(now()) < $protectionMinutes) {
            Log::info('工作空间在暂停保护期内，跳过处理', [
                'workspace_id' => $workspace->id,
                'suspended_at' => $workspace->suspended_at,
                'protection_minutes' => $protectionMinutes,
            ]);

            return;
        }

        try {
            DB::transaction(function () use ($workspace, $user, $totalCost, $record) {
                // 计算新的欠费金额
                $currentOverdueAmount = (string) ($workspace->overdue_amount ?? 0);
                $newOverdueAmount = bcadd($currentOverdueAmount, $totalCost, 4);

                // 更新工作空间欠费信息
                $workspace->update([
                    'status' => Workspace::STATUS_SUSPENDED,
                    'suspended_at' => now(),
                    'suspension_reason' => 'overdue',
                    'overdue_amount' => $newOverdueAmount,
                    'last_overdue_at' => now(),
                ]);

                // 检查是否启用自动暂停
                if (config('billing.suspension.auto_suspend_on_overdue', true)) {
                    $this->scheduleWorkspaceSuspension($workspace->id);
                }

                // 触发欠费事件
                UserOverdue::dispatch(
                    $user,
                    $workspace,
                    $newOverdueAmount,
                    '余额不足，无法支付资源使用费'
                );

                Log::warning('用户欠费，工作空间已暂停', [
                    'user_id' => $user->id,
                    'workspace_id' => $workspace->id,
                    'record_id' => $record->id,
                    'overdue_amount' => $newOverdueAmount,
                    'billing_amount' => $totalCost,
                    'auto_suspend' => config('billing.suspension.auto_suspend_on_overdue', true),
                ]);
            });

        } catch (\Exception $e) {
            Log::error('处理用户欠费失败', [
                'record_id' => $record->id,
                'workspace_id' => $workspace->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 检查并恢复欠费工作空间
     */
    public function checkAndResumeOverdueWorkspaces(): array
    {
        $suspendedWorkspaces = Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user', 'cluster'])
            ->get();

        $results = [];

        foreach ($suspendedWorkspaces as $workspace) {
            try {
                $result = $this->attemptWorkspaceRecovery($workspace);
                if ($result['attempted']) {
                    $results[] = $result;
                }
            } catch (\Exception $e) {
                Log::error('检查恢复欠费工作空间失败', [
                    'workspace_id' => $workspace->id,
                    'error' => $e->getMessage(),
                ]);

                $results[] = [
                    'workspace_id' => $workspace->id,
                    'attempted' => true,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * 尝试恢复单个工作空间
     */
    protected function attemptWorkspaceRecovery(Workspace $workspace): array
    {
        $user = $workspace->user;
        $overdueAmount = (string) $workspace->overdue_amount;

        // 验证欠费金额是否有效
        if (! is_numeric($overdueAmount) || bccomp($overdueAmount, '0', 8) <= 0) {
            Log::warning('跳过无效欠费金额的工作空间', [
                'workspace_id' => $workspace->id,
                'overdue_amount' => $overdueAmount,
            ]);

            return ['attempted' => false];
        }

        // 检查用户是否有足够余额支付欠费
        if (! $user->hasEnoughBalance($overdueAmount)) {
            return ['attempted' => false];
        }

        return $this->resumeOverdueWorkspace($workspace);
    }

    /**
     * 恢复欠费工作空间
     */
    public function resumeOverdueWorkspace(Workspace $workspace): array
    {
        return DB::transaction(function () use ($workspace) {
            $user = $workspace->user;
            $overdueAmount = (string) $workspace->overdue_amount;

            // 验证欠费金额是否有效
            if (! is_numeric($overdueAmount) || bccomp($overdueAmount, '0', 8) <= 0) {
                throw new \Exception("无效的欠费金额: {$overdueAmount}");
            }

            // 扣除欠费金额
            $this->balanceService->deductBalance(
                $user,
                $overdueAmount,
                "恢复工作空间 {$workspace->name} - 支付欠费"
            );

            // 更新工作空间状态
            $workspace->update([
                'status' => Workspace::STATUS_ACTIVE,
                'suspended_at' => null,
                'suspension_reason' => null,
                'overdue_amount' => null,
                'last_overdue_at' => null,
            ]);

            // 检查是否启用自动恢复
            if (config('billing.suspension.auto_resume_on_payment', true)) {
                $this->scheduleWorkspaceResumption($workspace->id);
            }

            // 触发恢复事件
            UserOverdueRecovered::dispatch(
                $user,
                $workspace,
                '余额已充值，服务已恢复'
            );

            Log::info('欠费工作空间恢复成功', [
                'workspace_id' => $workspace->id,
                'user_id' => $user->id,
                'paid_amount' => $overdueAmount,
                'new_balance' => $user->fresh()->current_balance,
                'auto_resume' => config('billing.suspension.auto_resume_on_payment', true),
            ]);

            return [
                'attempted' => true,
                'workspace_id' => $workspace->id,
                'success' => true,
                'paid_amount' => $overdueAmount,
                'new_balance' => $user->fresh()->current_balance,
            ];
        });
    }

    /**
     * 获取需要删除的过期工作空间
     */
    public function getWorkspacesForDeletion(?int $deletionDays = null): array
    {
        $deletionDays = $deletionDays ?? config('billing.overdue.deletion_days', 7);
        $cutoffDate = now()->subDays($deletionDays);

        return Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->where('suspended_at', '<=', $cutoffDate)
            ->whereNotNull('suspended_at')
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user', 'cluster'])
            ->get()
            ->map(function ($workspace) {
                return [
                    'workspace' => $workspace,
                    'suspended_days' => $workspace->suspended_at->diffInDays(now()),
                    'overdue_amount' => $workspace->overdue_amount,
                ];
            })
            ->toArray();
    }

    /**
     * 获取欠费通知对象
     */
    public function getWorkspacesForNotification(int $warningDays): array
    {
        $warningDate = now()->subDays($warningDays);
        $startOfDay = $warningDate->copy()->startOfDay();
        $endOfDay = $warningDate->copy()->endOfDay();

        return Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->whereBetween('suspended_at', [$startOfDay, $endOfDay])
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user'])
            ->get()
            ->toArray();
    }

    /**
     * 调度工作空间暂停任务
     */
    protected function scheduleWorkspaceSuspension(int $workspaceId): void
    {
        $suspendQueue = config('billing.suspension.suspend_queue', 'default');
        $job = SuspendWorkspaceResources::dispatch($workspaceId);

        if ($suspendQueue && $suspendQueue !== 'default') {
            $job->onQueue($suspendQueue);
        }

        Log::debug('工作空间暂停任务已调度', [
            'workspace_id' => $workspaceId,
            'queue' => $suspendQueue,
        ]);
    }

    /**
     * 调度工作空间恢复任务
     */
    protected function scheduleWorkspaceResumption(int $workspaceId): void
    {
        $resumeQueue = config('billing.suspension.resume_queue', 'default');
        $job = ResumeWorkspaceResources::dispatch($workspaceId);

        if ($resumeQueue && $resumeQueue !== 'default') {
            $job->onQueue($resumeQueue);
        }

        Log::debug('工作空间恢复任务已调度', [
            'workspace_id' => $workspaceId,
            'queue' => $resumeQueue,
        ]);
    }

    /**
     * 获取欠费统计信息
     */
    public function getOverdueStatistics(): array
    {
        $suspended = Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->whereNotNull('overdue_amount')
            ->get();

        $totalOverdueAmount = $suspended->sum('overdue_amount');
        $avgSuspendedDays = $suspended->filter(function ($workspace) {
            return $workspace->suspended_at;
        })->avg(function ($workspace) {
            return $workspace->suspended_at->diffInDays(now());
        });

        return [
            'total_suspended_workspaces' => $suspended->count(),
            'total_overdue_amount' => $totalOverdueAmount,
            'average_suspended_days' => round($avgSuspendedDays ?? 0, 1),
            'suspended_by_user' => $suspended->groupBy('user_id')->map->count()->toArray(),
        ];
    }
}
