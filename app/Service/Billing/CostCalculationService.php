<?php

namespace App\Service\Billing;

use App\Models\ClusterPricing;

/**
 * 成本计算服务
 * 负责各种资源的费用计算
 */
class CostCalculationService
{
    /**
     * 计算工作空间总费用
     */
    public function calculateWorkspaceCosts(ClusterPricing $pricing, array $usage): array
    {
        $memoryCost = $this->calculateMemoryCost($pricing, $usage['memory_mi'] ?? 0);
        $cpuCost = $this->calculateCpuCost($pricing, $usage['cpu_m'] ?? 0);
        $storageCost = $this->calculateStorageCost($pricing, $usage['storage_gi'] ?? 0);
        $loadBalancerCost = $this->calculateLoadBalancerCost($pricing, $usage['loadbalancer_count'] ?? 0);

        $totalCost = bcadd(
            bcadd($memoryCost, $cpuCost, 8),
            bcadd($storageCost, $loadBalancerCost, 8),
            8
        );

        return [
            'memory_cost' => $memoryCost,
            'cpu_cost' => $cpuCost,
            'storage_cost' => $storageCost,
            'loadbalancer_cost' => $loadBalancerCost,
            'total_cost' => $totalCost,
        ];
    }

    /**
     * 计算内存费用
     */
    public function calculateMemoryCost(ClusterPricing $pricing, int $memoryMi): string
    {
        if ($memoryMi <= 0) {
            return '0.00000000';
        }

        // 内存费用计算 (Mi -> GB, 月价格 -> 分钟价格)
        $memoryGb = bcdiv((string) $memoryMi, '1024', 8);
        $memoryPricePerMinute = bcdiv((string) $pricing->memory_price_per_gb, '43200', 8); // 30天*24小时*60分钟

        return bcmul($memoryGb, $memoryPricePerMinute, 8);
    }

    /**
     * 计算CPU费用
     */
    public function calculateCpuCost(ClusterPricing $pricing, int $cpuM): string
    {
        if ($cpuM <= 0) {
            return '0.00000000';
        }

        // CPU费用计算 (m -> core, 月价格 -> 分钟价格)
        $cpuCores = bcdiv((string) $cpuM, '1000', 8);
        $cpuPricePerMinute = bcdiv((string) $pricing->cpu_price_per_core, '43200', 8);

        return bcmul($cpuCores, $cpuPricePerMinute, 8);
    }

    /**
     * 计算存储费用
     */
    public function calculateStorageCost(ClusterPricing $pricing, int $storageGi): string
    {
        if ($storageGi <= 0) {
            return '0.00000000';
        }

        // 存储费用计算 (月价格 -> 分钟价格)
        $storagePricePerMinute = bcdiv((string) $pricing->storage_price_per_gb, '43200', 8);

        return bcmul((string) $storageGi, $storagePricePerMinute, 8);
    }

    /**
     * 计算LoadBalancer费用
     */
    public function calculateLoadBalancerCost(ClusterPricing $pricing, int $loadBalancerCount): string
    {
        if ($loadBalancerCount <= 0) {
            return '0.00000000';
        }

        // LoadBalancer费用计算 (月价格 -> 分钟价格)
        $loadBalancerPricePerMinute = bcdiv((string) $pricing->loadbalancer_price_per_instance, '43200', 8);

        return bcmul((string) $loadBalancerCount, $loadBalancerPricePerMinute, 8);
    }

    /**
     * 计算使用量统计
     */
    public function calculateUsageStatistics(array $usage): array
    {
        return [
            'memory_gb' => isset($usage['memory_mi']) ?
                bcdiv((string) $usage['memory_mi'], '1024', 4) : '0',
            'cpu_cores' => isset($usage['cpu_m']) ?
                bcdiv((string) $usage['cpu_m'], '1000', 4) : '0',
            'storage_gb' => (string) ($usage['storage_gi'] ?? 0),
            'loadbalancer_count' => (int) ($usage['loadbalancer_count'] ?? 0),
        ];
    }

    /**
     * 获取单位价格信息
     */
    public function getPricingRates(ClusterPricing $pricing): array
    {
        return [
            'memory_per_gb_per_minute' => bcdiv((string) $pricing->memory_price_per_gb, '43200', 8),
            'cpu_per_core_per_minute' => bcdiv((string) $pricing->cpu_price_per_core, '43200', 8),
            'storage_per_gb_per_minute' => bcdiv((string) $pricing->storage_price_per_gb, '43200', 8),
            'loadbalancer_per_instance_per_minute' => bcdiv((string) $pricing->loadbalancer_price_per_instance, '43200', 8),
        ];
    }

    /**
     * 验证计费费用
     */
    public function validateCost(string $cost): bool
    {
        return is_numeric($cost) && bccomp($cost, '0', 8) >= 0;
    }

    /**
     * 格式化费用显示
     */
    public function formatCost(string $cost, int $precision = 8): string
    {
        return bcadd($cost, '0', $precision);
    }
}
