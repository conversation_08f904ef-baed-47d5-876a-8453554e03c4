<?php

namespace App\Service\Billing;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

/**
 * Pod 资源计费服务
 * 负责收集和计算 Pod 资源（CPU、内存）的使用和费用
 */
class PodResourceBillingService
{
    /**
     * 收集工作空间的 Pod 资源使用情况
     */
    public function collectPodResourceUsage(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;
        $workspaceName = $workspace->name;

        $usage = [
            'memory_mi' => 0,
            'cpu_m' => 0,
            'pod_count' => 0,
        ];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/pods");

            if (! $response->successful()) {
                throw new \Exception('获取 Pods 失败: '.$response->body());
            }

            $pods = $response->json()['items'] ?? [];

            foreach ($pods as $pod) {
                $labels = $pod['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 Pod
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 检查 Pod 状态
                $phase = $pod['status']['phase'] ?? '';
                if (! in_array($phase, ['Running', 'Pending'])) {
                    continue;
                }

                $usage['pod_count']++;
                $containers = $pod['spec']['containers'] ?? [];

                foreach ($containers as $container) {
                    $resources = $container['resources'] ?? [];
                    $limits = $resources['limits'] ?? [];

                    // 统计内存使用 (单位: Mi)
                    if (isset($limits['memory'])) {
                        $memoryStr = $limits['memory'];
                        $usage['memory_mi'] += $this->parseMemoryToMi($memoryStr);
                    }

                    // 统计 CPU 使用 (单位: m)
                    if (isset($limits['cpu'])) {
                        $cpuStr = $limits['cpu'];
                        $usage['cpu_m'] += $this->parseCpuToMillicores($cpuStr);
                    }
                }
            }

            Log::debug('Pod 资源使用收集完成', [
                'workspace' => $workspaceName,
                'namespace' => $namespace,
                'usage' => $usage,
            ]);

        } catch (\Exception $e) {
            Log::error('收集 Pod 资源使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }

    /**
     * 计算 Pod 资源使用费用
     */
    public function calculatePodResourceCosts(ClusterPricing $pricing, array $usage): array
    {
        // 内存费用计算 (Mi -> GB, 月价格 -> 分钟价格)
        $memoryGb = bcdiv((string) ($usage['memory_mi'] ?? 0), '1024', 8);
        $memoryPricePerMinute = bcdiv((string) $pricing->memory_price_per_gb, '43200', 8); // 30天*24小时*60分钟
        $memoryCost = bcmul($memoryGb, $memoryPricePerMinute, 8);

        // CPU费用计算 (m -> core, 月价格 -> 分钟价格)
        $cpuCores = bcdiv((string) ($usage['cpu_m'] ?? 0), '1000', 8);
        $cpuPricePerMinute = bcdiv((string) $pricing->cpu_price_per_core, '43200', 8);
        $cpuCost = bcmul($cpuCores, $cpuPricePerMinute, 8);

        return [
            'memory_cost' => $memoryCost,
            'cpu_cost' => $cpuCost,
            'memory_usage_gb' => $memoryGb,
            'cpu_usage_cores' => $cpuCores,
        ];
    }

    /**
     * 解析内存字符串为 Mi 单位
     */
    protected function parseMemoryToMi(string $memoryStr): int
    {
        if (str_ends_with($memoryStr, 'Mi')) {
            return (int) str_replace('Mi', '', $memoryStr);
        } elseif (str_ends_with($memoryStr, 'Gi')) {
            return (int) str_replace('Gi', '', $memoryStr) * 1024;
        } elseif (str_ends_with($memoryStr, 'Ki')) {
            return (int) str_replace('Ki', '', $memoryStr) / 1024;
        } elseif (preg_match('/^(\d+)$/', $memoryStr, $matches)) {
            // 字节转换为 Mi
            return (int) $matches[1] / (1024 * 1024);
        }

        return 0;
    }

    /**
     * 解析 CPU 字符串为毫核单位
     */
    protected function parseCpuToMillicores(string $cpuStr): int
    {
        if (str_ends_with($cpuStr, 'm')) {
            return (int) str_replace('m', '', $cpuStr);
        } else {
            // 如果是核数，转换为毫核
            return (int) ((float) $cpuStr * 1000);
        }
    }
}
