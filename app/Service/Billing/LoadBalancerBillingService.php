<?php

namespace App\Service\Billing;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

/**
 * LoadBalancer 计费服务
 * 负责收集和计算 LoadBalancer 服务的使用和费用
 */
class LoadBalancerBillingService
{
    /**
     * 收集工作空间的 LoadBalancer 使用情况
     */
    public function collectLoadBalancerUsage(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;
        $workspaceName = $workspace->name;

        $usage = [
            'loadbalancer_count' => 0,
            'loadbalancer_details' => [],
        ];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/services");

            if (! $response->successful()) {
                throw new \Exception('获取 Services 失败: '.$response->body());
            }

            $services = $response->json()['items'] ?? [];

            foreach ($services as $service) {
                $labels = $service['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 Service
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                // 只统计 LoadBalancer 类型的服务
                $serviceType = $service['spec']['type'] ?? '';
                if ($serviceType === 'LoadBalancer') {
                    $usage['loadbalancer_count']++;

                    $serviceName = $service['metadata']['name'] ?? '';
                    $status = $service['status'] ?? [];
                    $loadBalancer = $status['loadBalancer'] ?? [];
                    $ingress = $loadBalancer['ingress'] ?? [];

                    $externalIp = null;
                    if (! empty($ingress)) {
                        $externalIp = $ingress[0]['ip'] ?? $ingress[0]['hostname'] ?? null;
                    }

                    $usage['loadbalancer_details'][] = [
                        'name' => $serviceName,
                        'type' => $serviceType,
                        'external_ip' => $externalIp,
                        'ports' => $service['spec']['ports'] ?? [],
                    ];
                }
            }

            Log::debug('LoadBalancer 使用收集完成', [
                'workspace' => $workspaceName,
                'namespace' => $namespace,
                'usage' => $usage,
            ]);

        } catch (\Exception $e) {
            Log::error('收集 LoadBalancer 使用失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $usage;
    }

    /**
     * 计算 LoadBalancer 使用费用
     */
    public function calculateLoadBalancerCosts(ClusterPricing $pricing, array $usage): array
    {
        // LoadBalancer费用计算 (月价格 -> 分钟价格)
        $loadbalancerCount = (string) ($usage['loadbalancer_count'] ?? 0);
        $loadbalancerPricePerMinute = bcdiv((string) $pricing->loadbalancer_price_per_service, '43200', 8); // 30天*24小时*60分钟
        $loadbalancerCost = bcmul($loadbalancerCount, $loadbalancerPricePerMinute, 8);

        return [
            'loadbalancer_cost' => $loadbalancerCost,
            'loadbalancer_count' => $loadbalancerCount,
            'price_per_service_per_minute' => $loadbalancerPricePerMinute,
        ];
    }

    /**
     * 获取工作空间中所有的网络服务统计
     */
    public function getServiceStatistics(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;
        $workspaceName = $workspace->name;

        $stats = [
            'total_services' => 0,
            'loadbalancer_services' => 0,
            'nodeport_services' => 0,
            'clusterip_services' => 0,
            'service_details' => [],
        ];

        try {
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/services");

            if (! $response->successful()) {
                throw new \Exception('获取 Services 失败: '.$response->body());
            }

            $services = $response->json()['items'] ?? [];

            foreach ($services as $service) {
                $labels = $service['metadata']['labels'] ?? [];

                // 只统计属于此工作空间的 Service
                if (($labels[ClusterLabel::WORKSPACE->value] ?? '') !== $workspaceName) {
                    continue;
                }

                $stats['total_services']++;
                $serviceType = $service['spec']['type'] ?? 'ClusterIP';
                $serviceName = $service['metadata']['name'] ?? '';

                switch ($serviceType) {
                    case 'LoadBalancer':
                        $stats['loadbalancer_services']++;
                        break;
                    case 'NodePort':
                        $stats['nodeport_services']++;
                        break;
                    case 'ClusterIP':
                    default:
                        $stats['clusterip_services']++;
                        break;
                }

                $stats['service_details'][] = [
                    'name' => $serviceName,
                    'type' => $serviceType,
                    'cluster_ip' => $service['spec']['clusterIP'] ?? '',
                    'ports' => $service['spec']['ports'] ?? [],
                    'selector' => $service['spec']['selector'] ?? [],
                ];
            }

        } catch (\Exception $e) {
            Log::error('获取服务统计失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $stats;
    }

    /**
     * 检查是否有孤立的 LoadBalancer 服务（没有对应 Pod 的服务）
     */
    public function findOrphanedLoadBalancers(Cluster $cluster, Workspace $workspace): array
    {
        $namespace = $workspace->namespace;
        $workspaceName = $workspace->name;
        $orphanedServices = [];

        try {
            // 获取所有 LoadBalancer 服务
            $serviceResponse = $cluster->http()->get("/api/v1/namespaces/{$namespace}/services");
            if (! $serviceResponse->successful()) {
                throw new \Exception('获取 Services 失败: '.$serviceResponse->body());
            }

            $services = $serviceResponse->json()['items'] ?? [];
            $loadBalancerServices = array_filter($services, function ($service) use ($workspaceName) {
                $labels = $service['metadata']['labels'] ?? [];
                $serviceType = $service['spec']['type'] ?? '';

                return ($labels[ClusterLabel::WORKSPACE->value] ?? '') === $workspaceName
                    && $serviceType === 'LoadBalancer';
            });

            // 获取所有 Pod
            $podResponse = $cluster->http()->get("/api/v1/namespaces/{$namespace}/pods");
            if (! $podResponse->successful()) {
                throw new \Exception('获取 Pods 失败: '.$podResponse->body());
            }

            $pods = $podResponse->json()['items'] ?? [];

            // 检查每个 LoadBalancer 服务是否有对应的 Pod
            foreach ($loadBalancerServices as $service) {
                $serviceName = $service['metadata']['name'] ?? '';
                $selector = $service['spec']['selector'] ?? [];

                if (empty($selector)) {
                    $orphanedServices[] = [
                        'name' => $serviceName,
                        'reason' => '服务没有选择器',
                        'external_ip' => $this->getExternalIp($service),
                    ];

                    continue;
                }

                // 检查是否有 Pod 匹配选择器
                $hasMatchingPod = false;
                foreach ($pods as $pod) {
                    $podLabels = $pod['metadata']['labels'] ?? [];
                    $podWorkspace = $podLabels[ClusterLabel::WORKSPACE->value] ?? '';

                    if ($podWorkspace !== $workspaceName) {
                        continue;
                    }

                    // 检查 Pod 状态
                    $phase = $pod['status']['phase'] ?? '';
                    if (! in_array($phase, ['Running', 'Pending'])) {
                        continue;
                    }

                    // 检查 Pod 标签是否匹配服务选择器
                    $matches = true;
                    foreach ($selector as $key => $value) {
                        if (! isset($podLabels[$key]) || $podLabels[$key] !== $value) {
                            $matches = false;
                            break;
                        }
                    }

                    if ($matches) {
                        $hasMatchingPod = true;
                        break;
                    }
                }

                if (! $hasMatchingPod) {
                    $orphanedServices[] = [
                        'name' => $serviceName,
                        'reason' => '没有匹配的运行中 Pod',
                        'selector' => $selector,
                        'external_ip' => $this->getExternalIp($service),
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('检查孤立 LoadBalancer 失败', [
                'namespace' => $namespace,
                'workspace' => $workspaceName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }

        return $orphanedServices;
    }

    /**
     * 获取 LoadBalancer 服务的外部 IP
     */
    protected function getExternalIp(array $service): ?string
    {
        $status = $service['status'] ?? [];
        $loadBalancer = $status['loadBalancer'] ?? [];
        $ingress = $loadBalancer['ingress'] ?? [];

        if (! empty($ingress)) {
            return $ingress[0]['ip'] ?? $ingress[0]['hostname'] ?? null;
        }

        return null;
    }
}
