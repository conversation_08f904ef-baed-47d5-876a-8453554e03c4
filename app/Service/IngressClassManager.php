<?php

namespace App\Service;

use App\Contracts\IngressClassDriverInterface;
use App\Service\IngressDrivers\NginxDriver;
use App\Service\IngressDrivers\TraefikDriver;
use InvalidArgumentException;

class IngressClassManager
{
    private array $drivers = [];

    public function __construct()
    {
        $this->registerDefaultDrivers();
    }

    /**
     * 注册默认驱动
     */
    private function registerDefaultDrivers(): void
    {
        $this->register('nginx', new NginxDriver);
        $this->register('traefik', new TraefikDriver);
    }

    /**
     * 注册驱动
     */
    public function register(string $name, IngressClassDriverInterface $driver): void
    {
        $this->drivers[$name] = $driver;
    }

    /**
     * 获取驱动
     */
    public function driver(string $name): IngressClassDriverInterface
    {
        if (! isset($this->drivers[$name])) {
            throw new InvalidArgumentException("Ingress class driver [{$name}] not found.");
        }

        return $this->drivers[$name];
    }

    /**
     * 获取所有可用的驱动名称
     */
    public function getAvailableDrivers(): array
    {
        return array_keys($this->drivers);
    }

    /**
     * 检查驱动是否存在
     */
    public function hasDriver(string $name): bool
    {
        return isset($this->drivers[$name]);
    }

    /**
     * 获取默认驱动（Traefik，因为集群中安装了它）
     */
    public function getDefaultDriver(): IngressClassDriverInterface
    {
        return $this->driver('traefik');
    }

    /**
     * 根据驱动名称构建 Ingress spec
     */
    public function buildIngressSpec(string $driverName, array $data): array
    {
        $driver = $this->driver($driverName);

        $spec = [
            'ingressClassName' => $driver->getIngressClassName(),
        ];

        // 处理规则
        if (! empty($data['rules'])) {
            $spec['rules'] = $driver->processRules($data['rules']);
        }

        // 处理 TLS
        if (! empty($data['tls'])) {
            $spec['tls'] = $driver->processTls($data['tls']);
        }

        return $spec;
    }

    /**
     * 构建 Ingress metadata
     */
    public function buildIngressMetadata(string $driverName, array $baseMetadata): array
    {
        $driver = $this->driver($driverName);

        $metadata = $baseMetadata;

        // 合并默认注解
        $metadata['annotations'] = array_merge(
            $driver->getDefaultAnnotations(),
            $metadata['annotations'] ?? []
        );

        // 合并默认标签
        $metadata['labels'] = array_merge(
            $driver->getDefaultLabels(),
            $metadata['labels'] ?? []
        );

        return $metadata;
    }

    /**
     * 验证 Ingress 配置
     */
    public function validateIngressSpec(string $driverName, array $spec): array
    {
        $driver = $this->driver($driverName);

        return $driver->validateSpec($spec);
    }

    /**
     * 获取驱动的健康检查配置
     */
    public function getHealthCheckConfig(string $driverName): array
    {
        $driver = $this->driver($driverName);

        return $driver->getHealthCheckConfig();
    }
}
