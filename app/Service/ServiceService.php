<?php

namespace App\Service;

use App\DTOs\ServiceDTO;
use App\Models\Service;
use App\Models\Workspace;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ServiceService
{
    public function __construct(
        protected Workspace $workspace,
        protected IpPoolService $ipPoolService
    ) {}

    /**
     * 获取 Service 列表
     */
    public function getServices(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services");

            $items = $response->json('items', []);

            // 获取数据库中的 Service 记录以补充 workload 信息
            $dbServices = Service::where('workspace_id', $this->workspace->id)
                ->get()
                ->keyBy('name');

            return array_map(function ($item) use ($dbServices) {
                $serviceName = $item['metadata']['name'] ?? '';
                $dbService = $dbServices->get($serviceName);

                $serviceDTO = ServiceDTO::fromK8sResource($item);

                if ($dbService) {
                    // 从数据库补充 workload 信息
                    return new ServiceDTO(
                        name: $serviceDTO->name,
                        namespace: $serviceDTO->namespace,
                        type: $serviceDTO->type,
                        clusterIp: $serviceDTO->clusterIp,
                        externalIps: $serviceDTO->externalIps,
                        loadBalancerIp: $serviceDTO->loadBalancerIp,
                        ports: $serviceDTO->ports,
                        selector: $serviceDTO->selector,
                        externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                        internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                        sessionAffinity: $serviceDTO->sessionAffinity,
                        status: $serviceDTO->status,
                        labels: $serviceDTO->labels,
                        annotations: $serviceDTO->annotations,
                        createdAt: $serviceDTO->createdAt,
                        uid: $serviceDTO->uid,
                        resourceVersion: $serviceDTO->resourceVersion,
                        workloadType: $dbService->target_workload_type,
                        workloadName: $dbService->target_workload_name
                    );
                }

                return $serviceDTO;
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 Service 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Service 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Service
     */
    public function getService(string $name): ServiceDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}");

            $serviceResource = $response->json();
            $serviceDTO = ServiceDTO::fromK8sResource($serviceResource);

            // 从数据库获取 workload 信息
            $dbService = Service::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->first();

            if ($dbService) {
                // 返回包含工作负载信息的新 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    type: $serviceDTO->type,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $dbService->target_workload_type,
                    workloadName: $dbService->target_workload_name
                );
            }

            return $serviceDTO;

        } catch (\Exception $e) {
            Log::error('获取 Service 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Service 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 Service
     */
    public function createService(array $data): ServiceDTO
    {
        return DB::transaction(function () use ($data) {
            // 验证目标工作负载是否存在并生成选择器
            $selector = $this->generateSelectorFromWorkload($data['target_workload_type'], $data['target_workload_name']);

            // 创建数据库记录
            $service = Service::create([
                'workspace_id' => $this->workspace->id,
                'name' => $data['name'],
                'type' => $data['type'],
                'ports' => $data['ports'],
                'selector' => $selector,
                'target_workload_type' => $data['target_workload_type'],
                'target_workload_name' => $data['target_workload_name'],
                'session_affinity' => $data['session_affinity'] ?? 'None',
                'external_traffic_policy' => $data['external_traffic_policy'] ?? 'Cluster',
                'allow_shared_ip' => $data['allow_shared_ip'] ?? false,
                'status' => 'pending',
            ]);

            try {
                // 处理 LoadBalancer 类型的特殊逻辑
                if ($service->isLoadBalancer()) {
                    $this->handleLoadBalancerService($service, $data);
                }

                // 构建 K8s Service 配置
                $payload = $this->buildServicePayload($service, $data);

                Log::debug('Creating K8s Service with payload:', $payload);

                // 创建 K8s Service
                $response = $this->workspace->cluster->http()
                    ->post("/api/v1/namespaces/{$this->workspace->namespace}/services", $payload);

                // 更新状态为成功
                $service->update([
                    'status' => 'active',
                    'status_message' => null,
                ]);

                Log::info('Service 创建成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $service->name,
                    'service_type' => $service->type,
                    'target_workload' => $data['target_workload_type'].'/'.$data['target_workload_name'],
                ]);

                $k8sServiceData = $response->json();
                $serviceDTO = ServiceDTO::fromK8sResource($k8sServiceData);

                // 返回包含 workload 信息的 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    type: $serviceDTO->type,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $data['target_workload_type'],
                    workloadName: $data['target_workload_name']
                );

            } catch (\Illuminate\Http\Client\RequestException $e) {
                // 更新状态为失败
                $service->update([
                    'status' => 'failed',
                    'status_message' => $e->getMessage(),
                ]);

                // 如果分配了资源，需要清理
                if ($service->isLoadBalancer() && $service->allocated_ip) {
                    $this->ipPoolService->releasePort($service->name, $this->workspace->namespace);
                }

                Log::error('创建 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $service->name,
                    'error' => $e->getMessage(),
                    'response' => $e->response->body(),
                ]);

                throw $e;
            }
        });
    }

    /**
     * 更新 Service
     */
    public function updateService(string $name, array $data): ServiceDTO
    {
        return DB::transaction(function () use ($name, $data) {
            // 获取数据库中的 Service 记录
            $service = Service::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->first();

            if (! $service) {
                throw new \Exception("Service '{$name}' 不存在");
            }

            // 验证目标工作负载是否存在并生成选择器
            $selector = $this->generateSelectorFromWorkload($data['target_workload_type'], $data['target_workload_name']);

            try {
                // 先获取现有的 K8s Service
                $existing = $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}")
                    ->json();

                // 更新数据库记录
                $service->update([
                    'ports' => $data['ports'],
                    'selector' => $selector,
                    'target_workload_type' => $data['target_workload_type'],
                    'target_workload_name' => $data['target_workload_name'],
                    'session_affinity' => $data['session_affinity'] ?? $service->session_affinity,
                    'external_traffic_policy' => $data['external_traffic_policy'] ?? $service->external_traffic_policy,
                    'allow_shared_ip' => $data['allow_shared_ip'] ?? $service->allow_shared_ip,
                    'status' => 'pending',
                ]);

                // 如果是 LoadBalancer 且修改了共享设置，需要重新处理
                if ($service->isLoadBalancer()) {
                    $this->handleLoadBalancerService($service, $data, true);
                }

                // 构建更新的 payload
                $payload = $this->buildServicePayload($service, $data, $existing);

                $response = $this->workspace->cluster->http()
                    ->put("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}", $payload);

                // 更新状态为成功
                $service->update([
                    'status' => 'active',
                    'status_message' => null,
                ]);

                Log::info('Service 更新成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'target_workload' => $data['target_workload_type'].'/'.$data['target_workload_name'],
                ]);

                $k8sServiceData = $response->json();
                $serviceDTO = ServiceDTO::fromK8sResource($k8sServiceData);

                // 返回包含 workload 信息的 DTO
                return new ServiceDTO(
                    name: $serviceDTO->name,
                    namespace: $serviceDTO->namespace,
                    type: $serviceDTO->type,
                    clusterIp: $serviceDTO->clusterIp,
                    externalIps: $serviceDTO->externalIps,
                    loadBalancerIp: $serviceDTO->loadBalancerIp,
                    ports: $serviceDTO->ports,
                    selector: $serviceDTO->selector,
                    sessionAffinity: $serviceDTO->sessionAffinity,
                    externalTrafficPolicy: $serviceDTO->externalTrafficPolicy,
                    internalTrafficPolicy: $serviceDTO->internalTrafficPolicy,
                    status: $serviceDTO->status,
                    labels: $serviceDTO->labels,
                    annotations: $serviceDTO->annotations,
                    createdAt: $serviceDTO->createdAt,
                    uid: $serviceDTO->uid,
                    resourceVersion: $serviceDTO->resourceVersion,
                    workloadType: $data['target_workload_type'],
                    workloadName: $data['target_workload_name']
                );

            } catch (\Exception $e) {
                // 更新状态为失败
                $service->update([
                    'status' => 'failed',
                    'status_message' => $e->getMessage(),
                ]);

                Log::error('更新 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new \Exception('更新 Service 失败：'.$e->getMessage());
            }
        });
    }

    /**
     * 删除 Service
     */
    public function deleteService(string $name): bool
    {
        return DB::transaction(function () use ($name) {
            try {
                // 获取数据库中的 Service 记录
                $service = Service::where('workspace_id', $this->workspace->id)
                    ->where('name', $name)
                    ->first();

                // 删除 K8s Service
                $this->workspace->cluster->http()
                    ->delete("/api/v1/namespaces/{$this->workspace->namespace}/services/{$name}");

                // 如果是 LoadBalancer，释放分配的端口
                if ($service && $service->isLoadBalancer()) {
                    $this->ipPoolService->releasePort($name, $this->workspace->namespace);
                }

                // 删除数据库记录
                if ($service) {
                    $service->delete();
                }

                Log::info('Service 删除成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                ]);

                return true;
            } catch (\Exception $e) {
                Log::error('删除 Service 失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new \Exception('删除 Service 失败：'.$e->getMessage());
            }
        });
    }

    /**
     * 处理 LoadBalancer 类型 Service 的特殊逻辑
     */
    protected function handleLoadBalancerService(Service $service, array $data, bool $isUpdate = false): void
    {
        // 如果是更新且已有分配，根据情况决定是否重新分配
        if ($isUpdate && $service->allocated_ip) {
            $needReallocation = false;

            // 如果修改了共享 IP 设置，需要重新分配
            if (isset($data['allow_shared_ip']) && $data['allow_shared_ip'] !== $service->allow_shared_ip) {
                $needReallocation = true;
            }

            if (! $needReallocation) {
                return; // 不需要重新分配
            }

            // 释放当前分配
            $this->ipPoolService->releasePort($service->name, $this->workspace->namespace);
        }

        // 自动分配 IP（顺序选择 IP 池和 IP）
        $allowSharedIp = $data['allow_shared_ip'] ?? true;
        $poolIp = $this->ipPoolService->autoAllocateIp($this->workspace->cluster_id, $allowSharedIp);

        if (! $poolIp) {
            $message = $allowSharedIp
                ? '集群中没有可用的共享 IP 地址'
                : '集群中没有可用的独占 IP 地址';
            throw new \Exception($message);
        }

        // 分配端口
        $port = $this->ipPoolService->allocatePort($poolIp, $service->name, $this->workspace->namespace);

        // 更新 Service 记录
        $service->update([
            'pool_ip_id' => $poolIp->id,
            'allocated_ip' => $poolIp->ip_address,
            'allocated_port' => $port,
        ]);
    }

    /**
     * 构建 Service 载荷
     */
    protected function buildServicePayload(Service $service, array $data, ?array $existing = null): array
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Service',
            'metadata' => [
                'name' => $service->name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $service->name),
                'annotations' => [],
            ],
            'spec' => [
                'type' => $service->type,
                'ports' => $this->buildPorts($data['ports'], $service),
                'selector' => $service->selector,
                'sessionAffinity' => $service->session_affinity,
            ],
        ];

        // 添加 LoadBalancer 特定配置
        if ($service->isLoadBalancer()) {
            $payload['metadata']['annotations'] = array_merge(
                $payload['metadata']['annotations'],
                $this->buildLoadBalancerAnnotations($service)
            );

            if ($service->external_traffic_policy) {
                $payload['spec']['externalTrafficPolicy'] = $service->external_traffic_policy;
            }
            $payload['spec']['allocateLoadBalancerNodePorts'] = false;
        }

        // 添加 NodePort 特定配置
        if ($service->isNodePort()) {
            if ($service->external_traffic_policy) {
                $payload['spec']['externalTrafficPolicy'] = $service->external_traffic_policy;
            }
        }

        if (empty($payload['metadata']['annotations'])) {
            $payload['metadata']['annotations'] = new \stdClass;
        }

        return $payload;
    }

    /**
     * 构建端口配置
     */
    protected function buildPorts(array $ports, Service $service): array
    {
        return array_map(function ($port, $index) use ($service) {
            $portConfig = [
                'name' => $port['name'] ?? 'port-'.($index + 1),
                'protocol' => $port['protocol'] ?? 'TCP',
                'targetPort' => is_numeric($port['target_port']) ? (int) $port['target_port'] : $port['target_port'],
            ];

            // 根据服务类型设置端口
            if ($service->isLoadBalancer()) {
                // LoadBalancer 使用分配的端口
                $portConfig['port'] = $service->allocated_port + $index;
            } elseif ($service->isNodePort()) {
                // NodePort 使用用户指定的端口，但不设置 nodePort（让 K8s 自动分配）
                $portConfig['port'] = (int) $port['port'];
                // nodePort 会由 Kubernetes 自动分配，范围通常是 30000-32767
            } else {
                // ClusterIP 使用用户指定的端口
                $portConfig['port'] = (int) $port['port'];
            }

            return $portConfig;
        }, $ports, array_keys($ports));
    }

    /**
     * 构建 LoadBalancer 注解
     */
    protected function buildLoadBalancerAnnotations(Service $service): array
    {
        return $service->getLoadBalancerAnnotations();
    }

    /**
     * 根据目标工作负载生成标签选择器
     */
    protected function generateSelectorFromWorkload(string $workloadType, string $workloadName): array
    {
        try {
            if ($workloadType === 'Deployment') {
                // 验证 Deployment 是否存在
                $this->workspace->cluster->http()
                    ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$workloadName}");
            } elseif ($workloadType === 'StatefulSet') {
                // 验证 StatefulSet 是否存在
                $this->workspace->cluster->http()
                    ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$workloadName}");
            } else {
                throw new \Exception("不支持的工作负载类型: {$workloadType}");
            }

            // 根据标准规范生成选择器
            return ['app' => $workloadName];

        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), '404')) {
                throw new \Exception("{$workloadType} '{$workloadName}' 不存在");
            }
            throw $e;
        }
    }

    /**
     * 验证选择器对应的 Pod 是否存在（保留方法但修改实现）
     */
    protected function validateSelector(array $selector): void
    {
        if (empty($selector)) {
            return; // 允许空选择器
        }

        try {
            // 构建标签选择器查询字符串
            $labelSelector = [];
            foreach ($selector as $key => $value) {
                $labelSelector[] = "{$key}={$value}";
            }
            $labelSelectorStr = implode(',', $labelSelector);

            // 查询匹配的 Pod
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/pods", [
                    'labelSelector' => $labelSelectorStr,
                ]);

            $pods = $response->json('items', []);

            if (empty($pods)) {
                Log::warning('Service 选择器未匹配到任何 Pod', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'selector' => $selector,
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('验证 Service 选择器失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'selector' => $selector,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
