<?php

namespace App\Service;

use App\DTOs\MetricsDTO;
use App\DTOs\NamespaceMetricsDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class MetricsService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取命名空间下的所有Pod指标
     */
    public function getNamespaceMetrics(): NamespaceMetricsDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/metrics.k8s.io/v1beta1/namespaces/{$this->workspace->namespace}/pods");

            if (! $response->successful()) {
                return new NamespaceMetricsDTO;
            }

            $items = $response->json('items', []);

            return NamespaceMetricsDTO::fromK8sResource($items);
        } catch (\Exception $e) {
            Log::warning('获取命名空间指标失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return new NamespaceMetricsDTO;
        }
    }

    /**
     * 获取特定Pod的指标
     */
    public function getPodMetrics(string $podName): ?MetricsDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/metrics.k8s.io/v1beta1/namespaces/{$this->workspace->namespace}/pods/{$podName}");

            if (! $response->successful()) {
                return null;
            }

            $data = $response->json();

            return MetricsDTO::fromK8sResource([
                'timestamp' => $data['timestamp'] ?? null,
                'window' => $data['window'] ?? null,
                'containers' => $data['containers'] ?? [],
            ]);
        } catch (\Exception $e) {
            Log::warning('获取Pod指标失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'pod_name' => $podName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }
}
