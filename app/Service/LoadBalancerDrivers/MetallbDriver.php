<?php

namespace App\Service\LoadBalancerDrivers;

use App\Contracts\LoadBalancerDriverInterface;
use App\Models\IpPool;
use App\Models\Service;
use Illuminate\Support\Facades\Log;

class MetallbDriver implements LoadBalancerDriverInterface
{
    protected const METALLB_NAMESPACE = 'metallb-system';

    public function syncIpPool(IpPool $ipPool): void
    {
        $this->createOrUpdateIpAddressPool($ipPool);
        $this->createOrUpdateL2Advertisement($ipPool);
    }

    public function deleteIpPool(IpPool $ipPool): void
    {
        $this->deleteL2Advertisement($ipPool);
        $this->deleteIpAddressPool($ipPool);
    }

    public function getServiceAnnotations(Service $service): array
    {
        $annotations = [];

        if ($service->allocated_ip) {
            $annotations['metallb.io/loadBalancerIPs'] = $service->allocated_ip;

            if ($service->poolIp && $service->poolIp->ipPool) {
                $annotations['metallb.io/ip-address-pool'] = $service->poolIp->ipPool->name;
            }

            if ($service->allow_shared_ip) {
                $sharingKey = 'share-ip-'.str_replace(['.', ':'], '-', $service->allocated_ip);
                $annotations['metallb.io/allow-shared-ip'] = $sharingKey;
            }
        }

        return $annotations;
    }

    protected function createOrUpdateIpAddressPool(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;
        $ipAddressPoolResource = [
            'apiVersion' => 'metallb.io/v1beta1',
            'kind' => 'IPAddressPool',
            'metadata' => [
                'name' => $poolName,
                'namespace' => self::METALLB_NAMESPACE,
            ],
            'spec' => [
                'addresses' => $this->buildIpPoolRanges($ipPool),
            ],
        ];

        try {
            $existing = $cluster->http()->get('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$poolName}");
            if ($existing->successful()) {
                $cluster->http()->put('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$poolName}", $ipAddressPoolResource);
                Log::info('Metallb IPAddressPool updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing IPAddressPool, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE.'/ipaddresspools', $ipAddressPoolResource);
                Log::info('Metallb IPAddressPool created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync Metallb IPAddressPool', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync Metallb IPAddressPool: '.$e->getMessage());
            }
        }
    }

    protected function createOrUpdateL2Advertisement(IpPool $ipPool): void
    {
        $cluster = $ipPool->cluster;
        $poolName = $ipPool->name;
        $adName = "{$poolName}-l2-advertisement";

        $l2AdvertisementResource = [
            'apiVersion' => 'metallb.io/v1beta1',
            'kind' => 'L2Advertisement',
            'metadata' => [
                'name' => $adName,
                'namespace' => self::METALLB_NAMESPACE,
            ],
            'spec' => [
                'ipAddressPools' => [$poolName],
            ],
        ];

        try {
            $existing = $cluster->http()->get('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}");
            if ($existing->successful()) {
                $cluster->http()->put('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}", $l2AdvertisementResource);
                Log::info('Metallb L2Advertisement updated successfully', ['pool_name' => $poolName]);
            } else {
                throw new \Exception('Failed to get existing L2Advertisement, status: '.$existing->status());
            }
        } catch (\Exception $e) {
            if ($e instanceof \Illuminate\Http\Client\RequestException && $e->response->status() === 404) {
                // Not found, so create it
                $cluster->http()->post('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE.'/l2advertisements', $l2AdvertisementResource);
                Log::info('Metallb L2Advertisement created successfully', ['pool_name' => $poolName]);
            } else {
                Log::error('Failed to sync Metallb L2Advertisement', ['pool_name' => $poolName, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to sync Metallb L2Advertisement: '.$e->getMessage());
            }
        }
    }

    protected function deleteIpAddressPool(IpPool $ipPool): void
    {
        try {
            $ipPool->cluster->http()->delete('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/ipaddresspools/{$ipPool->name}");
            Log::info('Metallb IPAddressPool deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            // Ignore if not found, log other errors
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete Metallb IPAddressPool', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete Metallb IPAddressPool: '.$e->getMessage());
            }
        }
    }

    protected function deleteL2Advertisement(IpPool $ipPool): void
    {
        $adName = "{$ipPool->name}-l2-advertisement";
        try {
            $ipPool->cluster->http()->delete('/apis/metallb.io/v1beta1/namespaces/'.self::METALLB_NAMESPACE."/l2advertisements/{$adName}");
            Log::info('Metallb L2Advertisement deleted successfully', ['pool_name' => $ipPool->name]);
        } catch (\Exception $e) {
            if ($e->getCode() !== 404) {
                Log::error('Failed to delete Metallb L2Advertisement', ['pool_name' => $ipPool->name, 'error' => $e->getMessage()]);
                throw new \Exception('Failed to delete Metallb L2Advertisement: '.$e->getMessage());
            }
        }
    }

    protected function buildIpPoolRanges(IpPool $ipPool): array
    {
        $ips = $ipPool->poolIps()->where('is_active', true)->pluck('ip_address')->toArray();
        if (empty($ips)) {
            return [];
        }

        usort($ips, fn ($a, $b) => ip2long($a) <=> ip2long($b));

        $ranges = [];
        if (empty($ips)) {
            return $ranges;
        }

        $start = $ips[0];
        $end = $ips[0];

        for ($i = 1; $i < count($ips); $i++) {
            if (ip2long($ips[$i]) == ip2long($end) + 1) {
                $end = $ips[$i];
            } else {
                if ($start === $end) {
                    $ranges[] = $start.'/32';
                } else {
                    $ranges[] = "$start-$end";
                }
                $start = $ips[$i];
                $end = $ips[$i];
            }
        }

        if ($start === $end) {
            $ranges[] = $start.'/32';
        } else {
            $ranges[] = "$start-$end";
        }

        return $ranges;
    }
}
