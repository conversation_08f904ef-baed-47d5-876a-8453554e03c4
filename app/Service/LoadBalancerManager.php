<?php

namespace App\Service;

use App\Contracts\LoadBalancerDriverInterface;
use App\Service\LoadBalancerDrivers\MetallbDriver;
use InvalidArgumentException;

class LoadBalancerManager
{
    private array $drivers = [];

    public function __construct()
    {
        $this->registerDefaultDrivers();
    }

    private function registerDefaultDrivers(): void
    {
        $this->register('metallb', new MetallbDriver);
    }

    public function register(string $name, LoadBalancerDriverInterface $driver): void
    {
        $this->drivers[$name] = $driver;
    }

    public function driver(?string $name = null): LoadBalancerDriverInterface
    {
        $name = $name ?: $this->getDefaultDriverName();

        if (! isset($this->drivers[$name])) {
            throw new InvalidArgumentException("LoadBalancer driver [{$name}] not found.");
        }

        return $this->drivers[$name];
    }

    public function getDefaultDriverName(): string
    {
        return 'metallb';
    }
}
