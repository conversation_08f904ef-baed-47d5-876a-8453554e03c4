<?php

namespace App\Service\PaymentGateways;

use App\Models\TopUpRecord;
use App\Models\User;

class ManualGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '手动充值';
    }

    public function getDescription(): string
    {
        return '管理员手动添加余额';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        // 如果不是管理员 guard 或者命令行，则抛出异常
        if (! app()->runningInConsole()) {
            throw new \Exception('非管理员用户不能手动充值');
        }

        // 手动充值不需要创建外部支付订单
        $transactionNumber = $this->generateTransactionNumber();

        $this->log('info', '创建手动充值订单', [
            'user_id' => $user->id,
            'amount' => $amount,
            'transaction_number' => $transactionNumber,
            'operator' => $extra['operator'] ?? 'system',
            'remark' => $extra['remark'] ?? '',
        ]);

        return [
            'url' => null,
            'order_id' => $transactionNumber,
            'payment_data' => array_merge($extra, [
                'transaction_number' => $transactionNumber,
                'payment_method' => 'manual',
            ]),
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        // 手动充值不需要外部回调验证
        return [
            'success' => false,
            'transaction_id' => null,
            'amount' => 0,
            'message' => '手动充值不支持回调验证',
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 手动充值通常是即时完成的
        return [
            'status' => 'completed',
            'amount' => 0,
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $refundAmount = $amount ?? $record->remaining_amount;
        $refundId = 'MANUAL_REFUND_'.date('YmdHis').mt_rand(1000, 9999);

        try {
            // 执行退款逻辑
            $success = $record->refund($refundAmount);

            $this->log('info', '手动退款处理', [
                'record_id' => $record->id,
                'refund_amount' => $refundAmount,
                'refund_id' => $refundId,
                'reason' => $reason,
                'success' => $success,
            ]);

            return [
                'success' => $success,
                'refund_id' => $refundId,
                'message' => $success ? '手动退款成功' : '手动退款失败',
            ];
        } catch (\Exception $e) {
            $this->log('error', '手动退款失败', [
                'record_id' => $record->id,
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'refund_id' => $refundId,
                'message' => '手动退款失败：'.$e->getMessage(),
            ];
        }
    }

    /**
     * 处理手动充值
     *
     * @param  User  $user  用户
     * @param  float  $amount  充值金额
     * @param  array  $extra  额外信息
     * @return array 处理结果
     */
    public function processManualTopUp(User $user, float $amount, array $extra = []): array
    {
        try {
            $transactionNumber = $this->generateTransactionNumber();

            // 使用数据库事务确保数据一致性
            \DB::transaction(function () use ($user, $amount, $transactionNumber, $extra) {
                // 创建充值记录
                $record = TopUpRecord::create([
                    'user_id' => $user->id,
                    'transaction_number' => $transactionNumber,
                    'amount' => $amount,
                    'remaining_amount' => $amount,
                    'status' => TopUpRecord::STATUS_COMPLETED,
                    'payment_method' => 'manual',
                    'payment_gateway_response' => [
                        'operator' => $extra['operator'] ?? 'system',
                        'processed_at' => now()->toISOString(),
                    ],
                    'completed_at' => now(),
                    'remark' => $extra['remark'] ?? null,
                ]);

                // 更新用户余额
                $user->increment('current_balance', $amount);
            });

            $this->log('info', '手动充值处理成功', [
                'user_id' => $user->id,
                'amount' => $amount,
                'transaction_number' => $transactionNumber,
                'operator' => $extra['operator'] ?? 'system',
                'remark' => $extra['remark'] ?? '',
            ]);

            return [
                'success' => true,
                'amount' => $amount,
                'transaction_number' => $transactionNumber,
                'message' => "手动充值成功！为用户 {$user->name} 充值 ¥{$amount}",
            ];
        } catch (\Exception $e) {
            $this->log('error', '手动充值处理失败', [
                'user_id' => $user->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '手动充值失败：'.$e->getMessage(),
            ];
        }
    }
}
