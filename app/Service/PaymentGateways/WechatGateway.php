<?php

namespace App\Service\PaymentGateways;

use App\Models\TopUpRecord;
use App\Models\User;

class WechatGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '微信支付';
    }

    public function getDescription(): string
    {
        return '使用微信支付进行在线支付';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        $transactionNumber = $this->generateTransactionNumber();

        // 模拟微信支付创建
        $paymentData = [
            'out_trade_no' => $transactionNumber,
            'total_fee' => (int) ($amount * 100), // 微信支付使用分为单位
            'body' => $extra['body'] ?? "用户 {$user->name} 充值",
            'notify_url' => route('payment.callback', ['gateway' => 'wechat']),
            'trade_type' => $extra['trade_type'] ?? 'NATIVE', // 扫码支付
        ];

        // 在实际实现中，这里会调用微信支付SDK生成支付链接
        $paymentUrl = $this->generateWechatPayUrl($paymentData);

        $this->log('info', '创建微信支付订单', [
            'user_id' => $user->id,
            'amount' => $amount,
            'transaction_number' => $transactionNumber,
        ]);

        return [
            'url' => $paymentUrl,
            'order_id' => $transactionNumber,
            'payment_data' => $paymentData,
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        // 验证微信支付回调签名
        if (! $this->verifyWechatSignature($callbackData)) {
            return [
                'success' => false,
                'transaction_id' => null,
                'amount' => 0,
                'message' => '签名验证失败',
            ];
        }

        $resultCode = $callbackData['result_code'] ?? '';
        $success = $resultCode === 'SUCCESS';

        return [
            'success' => $success,
            'transaction_id' => $callbackData['out_trade_no'] ?? '',
            'amount' => ((int) ($callbackData['total_fee'] ?? 0)) / 100, // 转换为元
            'message' => $success ? '支付成功' : "支付失败: {$resultCode}",
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 在实际实现中，这里会调用微信支付查询接口
        // 这里返回模拟数据
        return [
            'status' => 'SUCCESS',
            'amount' => 0, // 需要从实际查询结果中获取
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $refundAmount = $amount ?? $record->amount;
        $refundId = 'WECHAT_REFUND_'.date('YmdHis').mt_rand(1000, 9999);

        // 在实际实现中，这里会调用微信支付退款接口
        $refundData = [
            'out_trade_no' => $record->transaction_number,
            'out_refund_no' => $refundId,
            'total_fee' => (int) ($record->amount * 100),
            'refund_fee' => (int) ($refundAmount * 100),
            'refund_desc' => $reason ?: '用户申请退款',
        ];

        // 模拟退款成功
        $success = true; // 实际应该根据微信支付接口返回结果判断

        $this->log('info', '微信支付退款请求', [
            'record_id' => $record->id,
            'refund_amount' => $refundAmount,
            'refund_id' => $refundId,
            'success' => $success,
        ]);

        return [
            'success' => $success,
            'refund_id' => $refundId,
            'message' => $success ? '退款成功' : '退款失败',
        ];
    }

    /**
     * 生成微信支付链接（模拟）
     */
    private function generateWechatPayUrl(array $paymentData): string
    {
        // 在实际实现中，这里会使用微信支付SDK生成真实的支付链接
        $config = config('payments.gateways.wechat.config', []);
        $params = http_build_query([
            'appid' => $config['app_id'] ?? 'demo_app_id',
            'mch_id' => $config['mch_id'] ?? 'demo_mch_id',
            'nonce_str' => md5(uniqid()),
            'sign_type' => 'MD5',
            'out_trade_no' => $paymentData['out_trade_no'],
            'total_fee' => $paymentData['total_fee'],
            'body' => $paymentData['body'],
            'notify_url' => $paymentData['notify_url'],
            'trade_type' => $paymentData['trade_type'],
        ]);

        return "https://api.mch.weixin.qq.com/pay/unifiedorder?{$params}";
    }

    /**
     * 验证微信支付签名（模拟）
     */
    private function verifyWechatSignature(array $data): bool
    {
        // 在实际实现中，这里会使用微信支付密钥验证签名
        // 这里返回模拟结果
        return isset($data['sign']) && ! empty($data['sign']);
    }
}
