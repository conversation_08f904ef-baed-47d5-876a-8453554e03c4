<?php

namespace App\Service\PaymentGateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\TopUpRecord;
use Illuminate\Support\Facades\Log;

abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * 处理支付成功
     */
    public function handleSuccess(TopUpRecord $record, array $paymentData = []): bool
    {
        try {
            $record->update([
                'status' => TopUpRecord::STATUS_COMPLETED,
                'payment_gateway_response' => $paymentData,
                'completed_at' => now(),
            ]);

            Log::info('支付成功处理完成', [
                'record_id' => $record->id,
                'amount' => $record->amount,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('支付成功处理失败', [
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 处理支付失败
     */
    public function handleFailure(TopUpRecord $record, array $paymentData = []): bool
    {
        try {
            $record->update([
                'status' => TopUpRecord::STATUS_FAILED,
                'payment_gateway_response' => $paymentData,
            ]);

            Log::info('支付失败处理完成', [
                'record_id' => $record->id,
                'amount' => $record->amount,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('支付失败处理失败', [
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 生成交易号
     */
    protected function generateTransactionNumber(): string
    {
        return strtoupper(class_basename(static::class)).'_'.date('YmdHis').mt_rand(1000, 9999);
    }

    /**
     * 记录日志
     */
    protected function log(string $level, string $message, array $context = []): void
    {
        $context['gateway'] = class_basename(static::class);
        Log::log($level, $message, $context);
    }
}
