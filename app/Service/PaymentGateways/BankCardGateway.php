<?php

namespace App\Service\PaymentGateways;

use App\Models\TopUpRecord;
use App\Models\User;

class BankCardGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '银行卡';
    }

    public function getDescription(): string
    {
        return '使用银行卡进行在线支付';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        $transactionNumber = $this->generateTransactionNumber();

        // 模拟银行卡支付创建
        $paymentData = [
            'order_no' => $transactionNumber,
            'amount' => $amount,
            'currency' => 'CNY',
            'subject' => $extra['subject'] ?? '账户充值',
            'description' => $extra['description'] ?? "用户 {$user->name} 充值 ¥{$amount}",
            'notify_url' => route('payment.callback', ['gateway' => 'bank_card']),
            'return_url' => route('balance.index'),
            'payment_method' => 'bank_card',
        ];

        // 在实际实现中，这里会调用银行支付网关SDK生成支付链接
        $paymentUrl = $this->generateBankPayUrl($paymentData);

        $this->log('info', '创建银行卡支付订单', [
            'user_id' => $user->id,
            'amount' => $amount,
            'transaction_number' => $transactionNumber,
        ]);

        return [
            'url' => $paymentUrl,
            'order_id' => $transactionNumber,
            'payment_data' => $paymentData,
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        // 验证银行支付回调签名
        if (! $this->verifyBankSignature($callbackData)) {
            return [
                'success' => false,
                'transaction_id' => null,
                'amount' => 0,
                'message' => '签名验证失败',
            ];
        }

        $status = $callbackData['status'] ?? '';
        $success = $status === 'SUCCESS';

        return [
            'success' => $success,
            'transaction_id' => $callbackData['order_no'] ?? '',
            'amount' => (float) ($callbackData['amount'] ?? 0),
            'message' => $success ? '支付成功' : "支付状态: {$status}",
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 在实际实现中，这里会调用银行支付查询接口
        // 这里返回模拟数据
        return [
            'status' => 'SUCCESS',
            'amount' => 0, // 需要从实际查询结果中获取
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $refundAmount = $amount ?? $record->amount;
        $refundId = 'BANK_REFUND_'.date('YmdHis').mt_rand(1000, 9999);

        // 在实际实现中，这里会调用银行支付退款接口
        $refundData = [
            'original_order_no' => $record->transaction_number,
            'refund_order_no' => $refundId,
            'refund_amount' => $refundAmount,
            'refund_reason' => $reason ?: '用户申请退款',
        ];

        // 模拟退款成功
        $success = true; // 实际应该根据银行支付接口返回结果判断

        $this->log('info', '银行卡退款请求', [
            'record_id' => $record->id,
            'refund_amount' => $refundAmount,
            'refund_id' => $refundId,
            'success' => $success,
        ]);

        return [
            'success' => $success,
            'refund_id' => $refundId,
            'message' => $success ? '退款成功' : '退款失败',
        ];
    }

    /**
     * 生成银行支付链接（模拟）
     */
    private function generateBankPayUrl(array $paymentData): string
    {
        // 在实际实现中，这里会使用银行支付网关SDK生成真实的支付链接
        $config = config('payments.gateways.bank_card.config', []);
        $params = http_build_query([
            'merchant_id' => $config['merchant_id'] ?? 'demo_merchant',
            'order_no' => $paymentData['order_no'],
            'amount' => $paymentData['amount'],
            'currency' => $paymentData['currency'],
            'subject' => $paymentData['subject'],
            'notify_url' => $paymentData['notify_url'],
            'return_url' => $paymentData['return_url'],
            'payment_method' => 'bank_card',
            'timestamp' => time(),
        ]);

        return "https://payment.bank.example.com/pay?{$params}";
    }

    /**
     * 验证银行支付签名（模拟）
     */
    private function verifyBankSignature(array $data): bool
    {
        // 在实际实现中，这里会使用银行提供的公钥验证签名
        // 这里返回模拟结果
        return isset($data['signature']) && ! empty($data['signature']);
    }
}
