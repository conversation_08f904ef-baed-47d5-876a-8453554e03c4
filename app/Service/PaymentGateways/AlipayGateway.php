<?php

namespace App\Service\PaymentGateways;

use App\Models\TopUpRecord;
use App\Models\User;

class AlipayGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '支付宝';
    }

    public function getDescription(): string
    {
        return '使用支付宝进行在线支付';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        $transactionNumber = $this->generateTransactionNumber();

        // 模拟支付宝支付创建
        $paymentData = [
            'out_trade_no' => $transactionNumber,
            'total_amount' => $amount,
            'subject' => $extra['subject'] ?? '账户充值',
            'body' => $extra['body'] ?? "用户 {$user->name} 充值 ¥{$amount}",
            'notify_url' => route('payment.callback', ['gateway' => 'alipay']),
            'return_url' => route('balance.index'),
        ];

        // 在实际实现中，这里会调用支付宝SDK生成支付链接
        $paymentUrl = $this->generateAlipayUrl($paymentData);

        $this->log('info', '创建支付宝支付订单', [
            'user_id' => $user->id,
            'amount' => $amount,
            'transaction_number' => $transactionNumber,
        ]);

        return [
            'url' => $paymentUrl,
            'order_id' => $transactionNumber,
            'payment_data' => $paymentData,
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        // 验证支付宝回调签名
        if (! $this->verifyAlipaySignature($callbackData)) {
            return [
                'success' => false,
                'transaction_id' => null,
                'amount' => 0,
                'message' => '签名验证失败',
            ];
        }

        $tradeStatus = $callbackData['trade_status'] ?? '';
        $success = in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED']);

        return [
            'success' => $success,
            'transaction_id' => $callbackData['out_trade_no'] ?? '',
            'amount' => (float) ($callbackData['total_amount'] ?? 0),
            'message' => $success ? '支付成功' : "支付状态: {$tradeStatus}",
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 在实际实现中，这里会调用支付宝查询接口
        // 这里返回模拟数据
        return [
            'status' => 'TRADE_SUCCESS',
            'amount' => 0, // 需要从实际查询结果中获取
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $refundAmount = $amount ?? $record->amount;
        $refundId = 'REFUND_'.date('YmdHis').mt_rand(1000, 9999);

        // 在实际实现中，这里会调用支付宝退款接口
        $refundData = [
            'out_trade_no' => $record->transaction_number,
            'refund_amount' => $refundAmount,
            'refund_reason' => $reason ?: '用户申请退款',
            'out_request_no' => $refundId,
        ];

        // 模拟退款成功
        $success = true; // 实际应该根据支付宝接口返回结果判断

        $this->log('info', '支付宝退款请求', [
            'record_id' => $record->id,
            'refund_amount' => $refundAmount,
            'refund_id' => $refundId,
            'success' => $success,
        ]);

        return [
            'success' => $success,
            'refund_id' => $refundId,
            'message' => $success ? '退款成功' : '退款失败',
        ];
    }

    /**
     * 生成支付宝支付链接（模拟）
     */
    private function generateAlipayUrl(array $paymentData): string
    {
        // 在实际实现中，这里会使用支付宝SDK生成真实的支付链接
        $config = config('payments.gateways.alipay.config', []);
        $params = http_build_query([
            'app_id' => $config['app_id'] ?? 'demo_app_id',
            'method' => 'alipay.trade.page.pay',
            'format' => 'JSON',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'biz_content' => json_encode($paymentData),
        ]);

        return "https://openapi.alipay.com/gateway.do?{$params}";
    }

    /**
     * 验证支付宝签名（模拟）
     */
    private function verifyAlipaySignature(array $data): bool
    {
        // 在实际实现中，这里会使用支付宝公钥验证签名
        // 这里返回模拟结果
        return isset($data['sign']) && ! empty($data['sign']);
    }
}
