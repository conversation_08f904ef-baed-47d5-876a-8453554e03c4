<?php

namespace App\Service\PaymentGateways;

use App\Models\TopUpRecord;
use App\Models\User;

class TestPaymentGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '测试支付';
    }

    public function getDescription(): string
    {
        return '仅用于测试的支付方式，会重定向到模拟支付页面';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        $orderId = $this->generateOrderId();
        $subject = $extra['subject'] ?? '账户充值';

        // 生成简单的签名（仅用于测试）
        $signature = $this->generateTestSignature($orderId, $amount, $user->id);

        // 构建重定向URL，直接重定向到回调地址模拟支付成功
        $callbackUrl = route('payment.callback', ['gateway' => 'test_payment']);
        $redirectUrl = $callbackUrl.'?'.http_build_query([
            'order_id' => $orderId,
            'amount' => $amount,
            'user_id' => $user->id,
            'signature' => $signature,
            'status' => 'success',
            'transaction_id' => 'TEST_'.time().'_'.rand(1000, 9999),
        ]);

        $this->log('info', '创建测试支付订单', [
            'order_id' => $orderId,
            'amount' => $amount,
            'user_id' => $user->id,
            'redirect_url' => $redirectUrl,
        ]);

        return [
            'url' => $redirectUrl,
            'order_id' => $orderId,
            'payment_data' => [
                'subject' => $subject,
                'amount' => $amount,
                'signature' => $signature,
            ],
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        $orderId = $callbackData['order_id'] ?? '';
        $amount = $callbackData['amount'] ?? 0;
        $userId = $callbackData['user_id'] ?? 0;
        $signature = $callbackData['signature'] ?? '';
        $status = $callbackData['status'] ?? '';
        $transactionId = $callbackData['transaction_id'] ?? '';

        // 验证必要参数
        if (empty($orderId) || empty($amount) || empty($userId) || empty($signature)) {
            throw new \Exception('回调参数不完整');
        }

        // 验证签名
        $expectedSignature = $this->generateTestSignature($orderId, $amount, $userId);
        if ($signature !== $expectedSignature) {
            throw new \Exception('签名验证失败');
        }

        // 检查支付状态
        if ($status !== 'success') {
            throw new \Exception('支付失败');
        }

        $this->log('info', '测试支付回调验证成功', [
            'order_id' => $orderId,
            'amount' => $amount,
            'user_id' => $userId,
            'transaction_id' => $transactionId,
        ]);

        return [
            'success' => true,
            'order_id' => $orderId,
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'user_id' => $userId,
            'gateway_response' => $callbackData,
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 对于测试网关，总是返回成功状态
        return [
            'status' => 'completed',
            'transaction_id' => $transactionId,
            'amount' => 0, // 实际应该从数据库查询
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        $refundAmount = $amount ?? $record->remaining_amount;

        // 验证退款金额
        if ($refundAmount > $record->remaining_amount) {
            throw new \Exception('退款金额不能大于剩余金额');
        }

        // 模拟退款处理
        $refundId = 'REFUND_TEST_'.time().'_'.uniqid();

        return [
            'success' => true,
            'refund_id' => $refundId,
            'refund_amount' => $refundAmount,
            'refunded_at' => now()->format('Y-m-d H:i:s'),
            'transaction_number' => $record->transaction_number,
            'reason' => $reason ?: '测试退款',
        ];
    }

    /**
     * 生成测试用的简单签名
     */
    private function generateTestSignature(string $orderId, float $amount, int $userId): string
    {
        $data = $orderId.'|'.$amount.'|'.$userId.'|'.config('app.key');

        return hash('sha256', $data);
    }

    /**
     * 生成订单ID
     */
    private function generateOrderId(): string
    {
        return 'TEST_ORDER_'.date('YmdHis').'_'.rand(1000, 9999);
    }
}
