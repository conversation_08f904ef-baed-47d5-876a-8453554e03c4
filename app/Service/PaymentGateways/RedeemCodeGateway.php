<?php

namespace App\Service\PaymentGateways;

use App\Exceptions\PaymentNotSupportedException;
use App\Models\RedeemCode;
use App\Models\TopUpRecord;
use App\Models\User;

class RedeemCodeGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return '兑换码';
    }

    public function getDescription(): string
    {
        return '使用兑换码进行充值';
    }

    public function createPayment(User $user, float $amount, array $extra = []): array
    {
        // 兑换码支付不需要创建外部支付订单
        // 这个方法主要是为了接口一致性
        return [
            'url' => null,
            'order_id' => null,
            'payment_data' => $extra,
        ];
    }

    public function verifyCallback(array $callbackData): array
    {
        // 兑换码不需要外部回调验证
        return [
            'success' => false,
            'transaction_id' => null,
            'amount' => 0,
            'message' => '兑换码不支持回调验证',
        ];
    }

    public function getPaymentStatus(string $transactionId): array
    {
        // 兑换码支付是即时的，没有中间状态
        return [
            'status' => 'completed',
            'amount' => 0,
            'paid_at' => now()->toISOString(),
        ];
    }

    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array
    {
        throw new PaymentNotSupportedException('退款', '兑换码');
    }

    /**
     * 处理兑换码兑换
     *
     * @param  User  $user  用户
     * @param  string  $code  兑换码
     * @param  string|null  $ipAddress  IP地址
     * @param  string|null  $userAgent  用户代理
     * @return array 兑换结果
     */
    public function processRedeemCode(User $user, string $code, ?string $ipAddress = null, ?string $userAgent = null): array
    {
        try {
            // 查找兑换码
            $redeemCode = RedeemCode::findByCode($code);

            if (! $redeemCode) {
                return [
                    'success' => false,
                    'message' => '兑换码不存在',
                ];
            }

            // 检查是否可以使用
            if (! $redeemCode->canUse($user)) {
                $message = $this->getCannotUseMessage($redeemCode, $user);

                return [
                    'success' => false,
                    'message' => $message,
                ];
            }

            // 使用兑换码
            $usage = $redeemCode->use($user, $ipAddress, $userAgent);

            $this->log('info', '兑换码使用成功', [
                'user_id' => $user->id,
                'code' => $code,
                'amount' => $redeemCode->amount,
                'usage_id' => $usage->id,
                'ip_address' => $ipAddress,
            ]);

            return [
                'success' => true,
                'amount' => $redeemCode->amount,
                'redeem_code' => $redeemCode,
                'usage' => $usage,
                'message' => "兑换成功！获得 ¥{$redeemCode->amount} 余额",
            ];
        } catch (\Exception $e) {
            $this->log('error', '兑换码处理失败', [
                'user_id' => $user->id,
                'code' => $code,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取不能使用兑换码的原因
     */
    private function getCannotUseMessage(RedeemCode $redeemCode, User $user): string
    {
        if (! $redeemCode->is_active) {
            return '兑换码已被禁用';
        }

        if ($redeemCode->isExpired()) {
            return '兑换码已过期';
        }

        if ($redeemCode->isExhausted()) {
            return '兑换码使用次数已用完';
        }

        if ($redeemCode->max_uses == 1 && $redeemCode->usages()->where('user_id', $user->id)->exists()) {
            return '该兑换码你已经使用过了';
        }

        return '兑换码不可用';
    }
}
