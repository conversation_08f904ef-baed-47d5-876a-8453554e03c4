<?php

namespace App\Service;

use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class TlsCertificateService
{
    protected Workspace $workspace;

    protected SecretService $secretService;

    public function __construct(Workspace $workspace)
    {
        $this->workspace = $workspace;
        $this->secretService = new SecretService($workspace);
    }

    /**
     * 为指定域名生成自签名证书并创建 K8s Secret
     */
    public function generateAndCreateTlsSecret(array $hosts, ?string $secretName = null): string
    {
        if (empty($hosts)) {
            throw new \InvalidArgumentException('必须提供至少一个主机名');
        }

        // 生成 secret 名称
        if (! $secretName) {
            $hostHash = substr(md5(implode('-', $hosts)), 0, 8);
            $secretName = 'auto-tls-'.$hostHash;
        }

        // 验证 secret 名称格式
        if (! preg_match('/^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/', $secretName)) {
            throw new \InvalidArgumentException('Secret 名称格式无效');
        }

        try {
            // 检查 Secret 是否已存在
            try {
                $response = $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$secretName}");

                if ($response->successful()) {
                    Log::info('TLS Secret 已存在，跳过创建', [
                        'workspace_id' => $this->workspace->id,
                        'secret_name' => $secretName,
                        'hosts' => $hosts,
                    ]);

                    return $secretName;
                }
            } catch (\Exception $e) {
                // Secret 不存在，继续创建
            }

            // 生成自签名证书
            $certificate = $this->generateSelfSignedCertificate($hosts);

            // 创建 TLS Secret
            $this->secretService->createTlsSecret(
                $secretName,
                $certificate['cert'],
                $certificate['key']
            );

            Log::info('自动生成 TLS Secret 成功', [
                'workspace_id' => $this->workspace->id,
                'secret_name' => $secretName,
                'hosts' => $hosts,
            ]);

            return $secretName;

        } catch (\Exception $e) {
            Log::error('生成 TLS Secret 失败', [
                'workspace_id' => $this->workspace->id,
                'secret_name' => $secretName,
                'hosts' => $hosts,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('生成 TLS 证书失败: '.$e->getMessage());
        }
    }

    /**
     * 生成自签名证书
     */
    public function generateSelfSignedCertificate(array $hosts): array
    {
        if (empty($hosts)) {
            throw new \Exception('至少需要提供一个主机名');
        }

        if (! extension_loaded('openssl')) {
            throw new \Exception('需要 OpenSSL 扩展来生成证书');
        }

        // 创建私钥
        $privateKey = openssl_pkey_new([
            'digest_alg' => 'sha256',
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ]);

        if (! $privateKey) {
            throw new \Exception('生成私钥失败: '.openssl_error_string());
        }

        // 准备证书签名请求 (CSR)
        $dn = [
            'C' => 'CN',
            'ST' => 'Beijing',
            'L' => 'Beijing',
            'O' => 'PaaS Platform',
            'OU' => 'IT Department',
            'CN' => $hosts[0], // 使用第一个主机名作为 CN
        ];

        // 创建 CSR
        $csr = openssl_csr_new($dn, $privateKey, [
            'digest_alg' => 'sha256',
        ]);

        if (! $csr) {
            throw new \Exception('生成 CSR 失败: '.openssl_error_string());
        }

        // 准备 SAN 扩展
        $san = [];
        foreach ($hosts as $host) {
            $san[] = 'DNS:'.$host;
        }
        $sanString = implode(',', $san);

        // 生成自签名证书
        $certificate = openssl_csr_sign($csr, null, $privateKey, 365, [
            'digest_alg' => 'sha256',
            'x509_extensions' => 'v3_req',
            'req_extensions' => 'v3_req',
            'config' => $this->createOpenSSLConfig($sanString),
        ]);

        if (! $certificate) {
            throw new \Exception('生成证书失败: '.openssl_error_string());
        }

        // 导出证书和私钥
        openssl_x509_export($certificate, $certOut);
        openssl_pkey_export($privateKey, $keyOut);

        // 清理资源
        openssl_pkey_free($privateKey);
        openssl_x509_free($certificate);

        return [
            'cert' => $certOut,
            'key' => $keyOut,
        ];
    }

    /**
     * 创建 OpenSSL 配置文件内容
     */
    private function createOpenSSLConfig(string $sanString): string
    {
        $configFile = tempnam(sys_get_temp_dir(), 'openssl_config_');

        $config = "[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = {$sanString}

[v3_ca]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = CA:true
";

        file_put_contents($configFile, $config);

        // 注册清理函数
        register_shutdown_function(function () use ($configFile) {
            if (file_exists($configFile)) {
                unlink($configFile);
            }
        });

        return $configFile;
    }

    /**
     * 验证证书是否有效
     */
    public function validateCertificate(string $cert, string $key): bool
    {
        try {
            $certResource = openssl_x509_read($cert);
            $keyResource = openssl_pkey_get_private($key);

            if (! $certResource || ! $keyResource) {
                return false;
            }

            // 验证证书和私钥是否匹配
            $publicKey = openssl_pkey_get_public($certResource);
            $certDetails = openssl_pkey_get_details($publicKey);
            $keyDetails = openssl_pkey_get_details($keyResource);

            $match = $certDetails['key'] === $keyDetails['key'];

            // 清理资源
            openssl_x509_free($certResource);
            openssl_pkey_free($keyResource);
            openssl_pkey_free($publicKey);

            return $match;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 从证书中提取主机名
     */
    public function extractHostsFromCertificate(string $cert): array
    {
        try {
            $certResource = openssl_x509_read($cert);
            if (! $certResource) {
                return [];
            }

            $certInfo = openssl_x509_parse($certResource);
            openssl_x509_free($certResource);

            $hosts = [];

            // 从 CN 获取主机名
            if (isset($certInfo['subject']['CN'])) {
                $hosts[] = $certInfo['subject']['CN'];
            }

            // 从 SAN 扩展获取主机名
            if (isset($certInfo['extensions']['subjectAltName'])) {
                $sanEntries = explode(',', $certInfo['extensions']['subjectAltName']);
                foreach ($sanEntries as $entry) {
                    $entry = trim($entry);
                    if (str_starts_with($entry, 'DNS:')) {
                        $hosts[] = substr($entry, 4);
                    }
                }
            }

            return array_unique($hosts);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 检查证书是否即将过期（默认30天内）
     */
    public function isCertificateExpiring(string $cert, int $daysThreshold = 30): bool
    {
        try {
            $certResource = openssl_x509_read($cert);
            if (! $certResource) {
                return true;
            }

            $certInfo = openssl_x509_parse($certResource);
            openssl_x509_free($certResource);

            if (! isset($certInfo['validTo_time_t'])) {
                return true;
            }

            $expiryTime = $certInfo['validTo_time_t'];
            $thresholdTime = time() + ($daysThreshold * 24 * 60 * 60);

            return $expiryTime <= $thresholdTime;
        } catch (\Exception $e) {
            return true;
        }
    }
}
