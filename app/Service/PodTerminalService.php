<?php

namespace App\Service;

use App\Models\Workspace;
use Illuminate\Support\Facades\Log;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Connection\TcpConnection;

class PodTerminalService
{
    protected JwtService $jwtService;

    public function __construct(JwtService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * 验证用户权限并建立 Pod 终端连接
     */
    public function connectToPod(TcpConnection $connection, array $tokenData): bool
    {
        try {
            // 验证 token 类型
            if (($tokenData['type'] ?? '') !== 'pod_terminal') {
                $this->sendError($connection, 'Invalid token type');

                return false;
            }

            // 获取必要参数
            $userId = $tokenData['user_id'] ?? null;
            $workspaceId = $tokenData['workspace_id'] ?? null;
            $podName = $tokenData['pod_name'] ?? null;
            $containerName = $tokenData['container_name'] ?? null;
            $mode = $tokenData['mode'] ?? 'shell';

            if (! $userId || ! $workspaceId || ! $podName) {
                $this->sendError($connection, 'Missing required parameters');

                return false;
            }

            // 验证工作空间权限
            $workspace = Workspace::find($workspaceId);
            if (! $workspace || $workspace->user_id !== $userId) {
                $this->sendError($connection, 'Access denied to workspace');

                return false;
            }

            // 验证 Pod 是否存在
            if (! $this->validatePodExists($workspace, $podName)) {
                $this->sendError($connection, "Pod '{$podName}' not found");

                return false;
            }

            // 如果没有指定容器，获取第一个容器
            if (! $containerName) {
                $containerName = $this->getDefaultContainer($workspace, $podName);
                if (! $containerName) {
                    $this->sendError($connection, 'No containers found in pod');

                    return false;
                }
            }

            // 建立到 Pod 的连接
            return $this->establishPodConnection($connection, $workspace, $podName, $containerName, $mode);

        } catch (\Exception $e) {
            Log::error('Pod terminal connection failed', [
                'error' => $e->getMessage(),
                'token_data' => $tokenData,
            ]);

            $this->sendError($connection, 'Connection failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * 建立到 Pod 的实际连接
     */
    protected function establishPodConnection(
        TcpConnection $connection,
        Workspace $workspace,
        string $podName,
        string $containerName,
        string $mode
    ): bool {
        try {
            $cluster = $workspace->cluster;

            // 构建 WebSocket 连接到 K8s API
            $wsUrl = $this->buildWebSocketUrl($cluster, $workspace->namespace, $podName, $containerName, $mode);

            // 保存连接信息到连接对象
            $connection->podInfo = [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ];

            // 建立到 K8s 的 WebSocket 连接
            $this->createKubernetesConnection($connection, $wsUrl, $cluster);

            // 发送连接成功消息
            $this->sendMessage($connection, 'connected', [
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ]);

            Log::info('Pod terminal connection established', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to establish pod connection', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 创建到 Kubernetes 的 WebSocket 连接
     */
    protected function createKubernetesConnection(TcpConnection $userConnection, string $wsUrl, $cluster): void
    {
        // 构建 SSL 上下文选项，参考 Cluster 模型的 http 方法
        $context = [];
        if ($cluster->insecure_skip_tls_verify) {
            $context['ssl'] = [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ];
        } elseif ($cluster->certificate_authority_data) {
            $caFile = CertificateCacheService::getCachedCertificateFile('ca', $cluster->certificate_authority_data);
            $context['ssl'] = [
                'verify_peer' => true,
                'verify_peer_name' => true,
                'cafile' => $caFile,
            ];
        }

        // 处理客户端证书认证
        if ($cluster->auth_type === 'certificate') {
            if (empty($cluster->client_certificate_data) || empty($cluster->client_key_data)) {
                throw new \Exception('Certificate authentication requires certificate and key data');
            }
            // 对于 WebSocket，客户端证书需要在 SSL 上下文中设置
            $certFile = CertificateCacheService::getCachedCertificateFile('cert', $cluster->client_certificate_data);
            $keyFile = CertificateCacheService::getCachedCertificateFile('key', $cluster->client_key_data);
            if (! isset($context['ssl'])) {
                $context['ssl'] = [];
            }
            $context['ssl']['local_cert'] = $certFile;
            $context['ssl']['local_pk'] = $keyFile;
        }

        // 创建 AsyncTcpConnection

        // 将 wss:// 中的 wss 替换为 ws
        $wsUrl = str_replace('wss://', 'ws://', $wsUrl);
        $k8sConnection = new AsyncTcpConnection($wsUrl, $context);

        // 然后再设置 transport 为 ssl，可以解决 class \\Protocols\\Wss not exist 的问题。
        $k8sConnection->transport = 'ssl';

        // 设置请求头，参考 Cluster 模型的鉴权方式
        $headers = [
            'User-Agent' => 'PaaS-Terminal/1.0',
        ];

        switch ($cluster->auth_type) {
            case 'token':
                if (empty($cluster->token)) {
                    throw new \Exception('Token authentication requires a token');
                }
                $headers['Authorization'] = 'Bearer '.$cluster->token;
                break;

            case 'username_password':
                if (empty($cluster->username) || empty($cluster->password)) {
                    throw new \Exception('Username/password authentication requires username and password');
                }
                $headers['Authorization'] = 'Basic '.base64_encode($cluster->username.':'.$cluster->password);
                break;

            case 'certificate':
                // 证书认证已在 SSL 上下文中处理
                break;

            default:
                throw new \Exception("Unsupported authentication type: {$cluster->auth_type}");
        }

        $k8sConnection->headers = $headers;

        // TCP 连接成功后的回调
        $k8sConnection->onConnect = function ($k8sConnection) {
            Log::info('TCP connected to K8s API server');
        };

        // WebSocket 握手成功后的回调
        $k8sConnection->onWebSocketConnect = function ($k8sConnection, $response) use ($userConnection) {
            Log::info('K8s WebSocket handshake successful.');
            $userConnection->k8sConnection = $k8sConnection;

            // 客户端连接关闭时，关闭 K8s 连接
            $userConnection->onClose = function ($userConnection) use ($k8sConnection) {
                if (isset($userConnection->k8sConnection)) {
                    Log::info('Client disconnected, closing K8s connection.');
                    $k8sConnection->close();
                }
            };
        };

        // 收到 K8s 消息的回调
        $k8sConnection->onMessage = function ($k8sConnection, $data) use ($userConnection) {
            $this->handleKubernetesOutput($userConnection, $data);
        };

        // K8s 连接关闭的回调
        $k8sConnection->onClose = function ($k8sConnection) use ($userConnection) {
            Log::info('K8s connection closed');
            if (isset($userConnection->k8sConnection)) {
                unset($userConnection->k8sConnection);
            }
            if ($userConnection->getStatus() !== 'closed') {
                $userConnection->close();
            }
        };

        // K8s 连接错误的回调
        $k8sConnection->onError = function ($k8sConnection, $code, $msg) use ($userConnection) {
            Log::error('K8s connection error', ['code' => $code, 'message' => $msg]);
            $this->sendError($userConnection, "Kubernetes connection error: {$msg}");
            $userConnection->close();
        };

        $k8sConnection->connect();
    }

    /**
     * 构建到 K8s 的 WebSocket URL
     */
    protected function buildWebSocketUrl(
        $cluster,
        string $namespace,
        string $podName,
        string $containerName,
        string $mode
    ): string {
        $baseUrl = str_replace(['http://', 'https://'], ['ws://', 'wss://'], $cluster->server_url);

        if ($mode === 'attach') {
            // attach 模式：连接到容器的主进程
            $path = "/api/v1/namespaces/{$namespace}/pods/{$podName}/attach";
            $params = http_build_query([
                'container' => $containerName,
                'stdin' => 'true',
                'stdout' => 'true',
                'stderr' => 'true',
                'tty' => 'true',
            ]);
        } else {
            // shell 模式：执行 shell 命令
            $path = "/api/v1/namespaces/{$namespace}/pods/{$podName}/exec";

            // 手动构建 command 参数，因为需要多个 command=value 格式
            $commandParams = [
                'container' => $containerName,
                'stdin' => 'true',
                'stdout' => 'true',
                'stderr' => 'true',
                'tty' => 'true',
            ];

            $params = http_build_query($commandParams);

            $commands = ['sh', '-c', 'clear; (zsh || bash || ash || sh)'];
            foreach ($commands as $cmd) {
                $params .= '&command='.urlencode($cmd);
            }
        }

        return "{$baseUrl}{$path}?{$params}";
    }

    /**
     * 验证 Pod 是否存在
     */
    protected function validatePodExists(Workspace $workspace, string $podName): bool
    {
        try {
            $response = $workspace->cluster->http()
                ->get("/api/v1/namespaces/{$workspace->namespace}/pods/{$podName}");

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取 Pod 的默认容器
     */
    protected function getDefaultContainer(Workspace $workspace, string $podName): ?string
    {
        try {
            $response = $workspace->cluster->http()
                ->get("/api/v1/namespaces/{$workspace->namespace}/pods/{$podName}");

            if ($response->successful()) {
                $pod = $response->json();
                $containers = $pod['spec']['containers'] ?? [];

                if (! empty($containers)) {
                    return $containers[0]['name'];
                }
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 处理来自客户端的输入
     */
    public function handleInput(TcpConnection $connection, string $input): void
    {
        if (! isset($connection->k8sConnection)) {
            Log::warning('Received input but no K8s connection available.', ['pod_info' => $connection->podInfo ?? []]);

            return;
        }

        // 将 base64 解码后的数据加上 channel byte 后转发到 K8s Pod
        $this->forwardToKubernetes($connection, $input);
    }

    /**
     * 将输入转发到 Kubernetes
     */
    protected function forwardToKubernetes(TcpConnection $connection, string $input): void
    {
        if (isset($connection->k8sConnection)) {
            // 添加 STDIN channel (0)
            $connection->k8sConnection->send("\x00".$input);
        }
    }

    /**
     * 处理来自 Kubernetes 的输出
     */
    public function handleKubernetesOutput(TcpConnection $connection, string $output): void
    {
        if (strlen($output) > 0) {
            $channel = $output[0];
            $data = substr($output, 1);

            switch ($channel) {
                case "\x01": // STDOUT
                    $this->sendMessage($connection, 'output', ['data' => $data]);
                    break;
                case "\x02": // STDERR
                    // 同样作为 output 发送，前端终端会显示
                    $this->sendMessage($connection, 'output', ['data' => $data]);
                    break;
                case "\x03": // Error
                    $this->sendError($connection, 'Kubernetes API Error: '.$data);
                    break;
            }
        }
    }

    /**
     * 发送消息给客户端
     */
    protected function sendMessage(TcpConnection $connection, string $type, array $data = []): void
    {
        $message = json_encode([
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true),
        ]);

        $connection->send($message);
    }

    /**
     * 发送错误消息
     */
    protected function sendError(TcpConnection $connection, string $message): void
    {
        $this->sendMessage($connection, 'error', ['message' => $message]);
    }

    /**
     * 清理连接
     */
    public function cleanup(TcpConnection $connection): void
    {
        if (isset($connection->k8sConnection)) {
            // 清理 K8s 连接
            $connection->k8sConnection->close();
            unset($connection->k8sConnection);
        }

        if (isset($connection->podInfo)) {
            Log::info('Pod terminal connection closed', [
                'pod_info' => $connection->podInfo,
            ]);
            unset($connection->podInfo);
        }
    }
}
