<?php

namespace App\Service;

use App\Models\Workspace;
use Exception;
use Illuminate\Support\Facades\Log;

class NamespaceService
{
    /**
     * 创建 Kubernetes namespace
     */
    public function createNamespace(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            // 构建 namespace 资源定义
            $namespaceResource = [
                'apiVersion' => 'v1',
                'kind' => 'Namespace',
                'metadata' => [
                    'name' => $namespace,
                    'labels' => array_merge(
                        $workspace->buildDefaultLabels($workspace),
                        [
                            'pod-security.kubernetes.io/enforce' => config('k8s.podSecurity.enforce'),
                            'pod-security.kubernetes.io/audit' => config('k8s.podSecurity.audit'),
                            'pod-security.kubernetes.io/warn' => config('k8s.podSecurity.warn'),
                        ]
                    ),
                ],
            ];

            // 调用 Kubernetes API 创建 namespace
            $response = $cluster->http()->post('/api/v1/namespaces', $namespaceResource);

            if (! $response->successful()) {
                Log::error('Namespace 创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'cluster_id' => $cluster->id,
                    'response' => $response->body(),
                    'status' => $response->status(),
                ]);

                return false;
            }

            Log::info('Namespace 创建成功', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'cluster_id' => $cluster->id,
            ]);

            // 创建资源配额
            if (! $this->createResourceQuota($workspace)) {
                Log::error('资源配额创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                ]);
                // 不返回 false，因为 namespace 已经创建成功
            }

            // 创建网络策略
            if (config('k8s.workspace.networkPolicy.enabled')) {
                if (! $this->createNetworkPolicies($workspace)) {
                    Log::error('网络策略创建失败', [
                        'workspace_id' => $workspace->id,
                        'namespace' => $namespace,
                    ]);
                    // 不返回 false，因为 namespace 已经创建成功
                }
            }

            // 更新工作空间状态为 active
            $workspace->update(['status' => 'active']);

            return true;
        } catch (Exception $e) {
            Log::error('Namespace 创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'cluster_id' => $workspace->cluster_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 创建资源配额
     */
    private function createResourceQuota(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;
            $quotaConfig = config('k8s.workspace.resourceQuota');

            $resourceQuota = [
                'apiVersion' => 'v1',
                'kind' => 'ResourceQuota',
                'metadata' => [
                    'name' => 'workspace-quota',
                    'namespace' => $namespace,
                    'labels' => $workspace->buildDefaultLabels($workspace, 'resource-quota'),
                ],
                'spec' => [
                    'hard' => [
                        'requests.memory' => ($quotaConfig['memory'] / 2).'Mi', // request 是 limit 的一半
                        'limits.memory' => $quotaConfig['memory'].'Mi',
                        'requests.cpu' => ($quotaConfig['cpu'] / 2).'m', // request 是 limit 的一半
                        'limits.cpu' => $quotaConfig['cpu'].'m',
                        'requests.storage' => $quotaConfig['storage'].'Gi',
                        'pods' => (string) $quotaConfig['pods'],
                        'persistentvolumeclaims' => (string) $quotaConfig['persistentvolumeclaims'],
                        'services' => (string) $quotaConfig['services'],
                        'configmaps' => (string) $quotaConfig['configmaps'],
                        'secrets' => (string) $quotaConfig['secrets'],
                    ],
                ],
            ];

            $response = $cluster->http()->post("/api/v1/namespaces/{$namespace}/resourcequotas", $resourceQuota);

            if ($response->successful()) {
                Log::info('资源配额创建成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                ]);

                return true;
            } else {
                Log::error('资源配额创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'response' => $response->body(),
                    'status' => $response->status(),
                ]);

                return false;
            }
        } catch (Exception $e) {
            Log::error('资源配额创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 创建网络策略
     */
    private function createNetworkPolicies(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;
            $networkConfig = config('k8s.workspace.networkPolicy');

            $success = true;

            // 创建默认拒绝入站流量的网络策略
            if ($networkConfig['defaultDenyIngress']) {
                $denyIngressPolicy = [
                    'apiVersion' => 'networking.k8s.io/v1',
                    'kind' => 'NetworkPolicy',
                    'metadata' => [
                        'name' => 'default-deny-ingress',
                        'namespace' => $namespace,
                        'labels' => $workspace->buildDefaultLabels($workspace, 'network-policy-deny-ingress'),
                    ],
                    'spec' => [
                        'podSelector' => new \stdClass, // 选择所有 pod
                        'policyTypes' => ['Ingress'],
                    ],
                ];

                $response = $cluster->http()->post("/apis/networking.k8s.io/v1/namespaces/{$namespace}/networkpolicies", $denyIngressPolicy);

                if (! $response->successful()) {
                    Log::error('默认拒绝入站网络策略创建失败', [
                        'workspace_id' => $workspace->id,
                        'namespace' => $namespace,
                        'response' => $response->body(),
                    ]);
                    $success = false;
                }
            }

            // 创建默认拒绝出站流量的网络策略
            if ($networkConfig['defaultDenyEgress']) {
                $denyEgressPolicy = [
                    'apiVersion' => 'networking.k8s.io/v1',
                    'kind' => 'NetworkPolicy',
                    'metadata' => [
                        'name' => 'default-deny-egress',
                        'namespace' => $namespace,
                        'labels' => $workspace->buildDefaultLabels($workspace, 'network-policy-deny-egress'),
                    ],
                    'spec' => [
                        'podSelector' => new \stdClass, // 选择所有 pod
                        'policyTypes' => ['Egress'],
                    ],
                ];

                $response = $cluster->http()->post("/apis/networking.k8s.io/v1/namespaces/{$namespace}/networkpolicies", $denyEgressPolicy);

                if (! $response->successful()) {
                    Log::error('默认拒绝出站网络策略创建失败', [
                        'workspace_id' => $workspace->id,
                        'namespace' => $namespace,
                        'response' => $response->body(),
                    ]);
                    $success = false;
                }
            }

            // 创建允许同 namespace 内通信的网络策略
            $allowSameNamespacePolicy = [
                'apiVersion' => 'networking.k8s.io/v1',
                'kind' => 'NetworkPolicy',
                'metadata' => [
                    'name' => 'allow-same-namespace',
                    'namespace' => $namespace,
                    'labels' => $workspace->buildDefaultLabels($workspace, 'network-policy-allow-same-namespace'),
                ],
                'spec' => [
                    'podSelector' => new \stdClass, // 选择所有 pod
                    'policyTypes' => ['Ingress', 'Egress'],
                    'ingress' => [
                        [
                            'from' => [
                                [
                                    'namespaceSelector' => [
                                        'matchLabels' => [
                                            'name' => $namespace,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'egress' => [
                        [
                            'to' => [
                                [
                                    'namespaceSelector' => [
                                        'matchLabels' => [
                                            'name' => $namespace,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                        // 允许 DNS 查询
                        [
                            'to' => [],
                            'ports' => [
                                [
                                    'protocol' => 'UDP',
                                    'port' => 53,
                                ],
                                [
                                    'protocol' => 'TCP',
                                    'port' => 53,
                                ],
                            ],
                        ],
                        // 允许 HTTPS 出站（用于拉取镜像等）
                        [
                            'to' => [],
                            'ports' => [
                                [
                                    'protocol' => 'TCP',
                                    'port' => 443,
                                ],
                                [
                                    'protocol' => 'TCP',
                                    'port' => 80,
                                ],
                            ],
                        ],
                    ],
                ],
            ];

            $response = $cluster->http()->post("/apis/networking.k8s.io/v1/namespaces/{$namespace}/networkpolicies", $allowSameNamespacePolicy);

            if (! $response->successful()) {
                Log::error('允许同 namespace 通信网络策略创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'response' => $response->body(),
                ]);
                $success = false;
            }

            if ($success) {
                Log::info('网络策略创建成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                ]);
            }

            return $success;
        } catch (Exception $e) {
            Log::error('网络策略创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 删除 Kubernetes namespace
     */
    public function deleteNamespace(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            // 调用 Kubernetes API 删除 namespace
            $response = $cluster->http()->delete("/api/v1/namespaces/{$namespace}");

            if ($response->successful()) {
                Log::info('Namespace 删除成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'cluster_id' => $cluster->id,
                ]);

                return true;
            } else {
                Log::error('Namespace 删除失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'cluster_id' => $cluster->id,
                    'response' => $response->body(),
                    'status' => $response->status(),
                ]);

                return false;
            }
        } catch (Exception $e) {
            Log::error('Namespace 删除异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'cluster_id' => $workspace->cluster_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 检查 namespace 是否存在
     */
    public function namespaceExists(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}");

            return $response->successful();
        } catch (Exception $e) {
            Log::error('检查 Namespace 存在性失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 获取 namespace 详情
     */
    public function getNamespaceDetails(Workspace $workspace): ?array
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}");

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (Exception $e) {
            Log::error('获取 Namespace 详情失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 获取资源配额信息
     */
    public function getResourceQuota(Workspace $workspace): ?array
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/resourcequotas/workspace-quota");

            if ($response->successful()) {
                $quota = $response->json();

                // 格式化资源配额信息，使其更易读
                $formatted = [
                    'name' => $quota['metadata']['name'] ?? 'workspace-quota',
                    'namespace' => $namespace,
                    'limits' => [],
                    'used' => [],
                    'usage_percentage' => [],
                ];

                $hard = $quota['spec']['hard'] ?? [];
                $used = $quota['status']['used'] ?? [];

                foreach ($hard as $resource => $limit) {
                    $formatted['limits'][$resource] = $limit;
                    $formatted['used'][$resource] = $used[$resource] ?? '0';

                    // 计算使用百分比
                    if (isset($used[$resource])) {
                        $formatted['usage_percentage'][$resource] = $this->calculateUsagePercentage($used[$resource], $limit);
                    } else {
                        $formatted['usage_percentage'][$resource] = 0;
                    }
                }

                return $formatted;
            }

            return null;
        } catch (Exception $e) {
            Log::error('获取资源配额失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 计算资源使用百分比
     */
    private function calculateUsagePercentage(string $used, string $limit): float
    {
        // 处理不同单位的资源
        $usedValue = $this->parseResourceValue($used);
        $limitValue = $this->parseResourceValue($limit);

        if ($limitValue === 0) {
            return 0;
        }

        return round(($usedValue / $limitValue) * 100, 2);
    }

    /**
     * 解析资源值（处理 Mi, Gi, m 等单位）
     */
    private function parseResourceValue(string $value): float
    {
        if (is_numeric($value)) {
            return (float) $value;
        }

        // 处理内存单位
        if (str_ends_with($value, 'Mi')) {
            return (float) str_replace('Mi', '', $value);
        }

        if (str_ends_with($value, 'Gi')) {
            return (float) str_replace('Gi', '', $value) * 1024;
        }

        // 处理 CPU 单位
        if (str_ends_with($value, 'm')) {
            return (float) str_replace('m', '', $value);
        }

        // 处理其他情况，直接返回数值
        return (float) preg_replace('/[^0-9.]/', '', $value);
    }

    /**
     * 获取网络策略列表
     */
    public function getNetworkPolicies(Workspace $workspace): array
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;

            $response = $cluster->http()->get("/apis/networking.k8s.io/v1/namespaces/{$namespace}/networkpolicies");

            if ($response->successful()) {
                $policies = $response->json();

                $formatted = [];
                foreach ($policies['items'] ?? [] as $policy) {
                    $formatted[] = [
                        'name' => $policy['metadata']['name'],
                        'namespace' => $namespace,
                        'policy_types' => $policy['spec']['policyTypes'] ?? [],
                        'created_at' => $policy['metadata']['creationTimestamp'] ?? null,
                    ];
                }

                return $formatted;
            }

            return [];
        } catch (Exception $e) {
            Log::error('获取网络策略失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }
}
