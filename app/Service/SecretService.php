<?php

namespace App\Service;

use App\DTOs\SecretDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class SecretService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 Secret 列表
     */
    public function getSecrets(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return SecretDTO::fromK8sResource($item);
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 Secret 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Secret 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Secret
     */
    public function getSecret(string $name): SecretDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}");

            return SecretDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('获取 Secret 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Secret 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建通用 Secret
     */
    public function createGenericSecret(string $name, array $data): SecretDTO
    {
        $secretData = [];
        foreach ($data as $key => $value) {
            $secretData[$key] = base64_encode($value);
        }

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name),
            ],
            'type' => 'Opaque',
            'data' => $secretData,
        ];

        return $this->createSecret($payload);
    }

    /**
     * 创建 Docker Registry Secret
     */
    public function createDockerRegistrySecret(string $name, string $server, string $username, string $password, ?string $email = null): SecretDTO
    {
        $dockerConfig = [
            'auths' => [
                $server => [
                    'username' => $username,
                    'password' => $password,
                    'auth' => base64_encode($username.':'.$password),
                ],
            ],
        ];

        if ($email) {
            $dockerConfig['auths'][$server]['email'] = $email;
        }

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name),
            ],
            'type' => 'kubernetes.io/dockerconfigjson',
            'data' => [
                '.dockerconfigjson' => base64_encode(json_encode($dockerConfig)),
            ],
        ];

        return $this->createSecret($payload);
    }

    /**
     * 创建 TLS Secret
     */
    public function createTlsSecret(string $name, string $cert, string $key): SecretDTO
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name),
            ],
            'type' => 'kubernetes.io/tls',
            'data' => [
                'tls.crt' => base64_encode($cert),
                'tls.key' => base64_encode($key),
            ],
        ];

        return $this->createSecret($payload);
    }

    /**
     * 创建 Basic Auth Secret
     */
    public function createBasicAuthSecret(string $name, string $username, string $password): SecretDTO
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name),
            ],
            'type' => 'kubernetes.io/basic-auth',
            'data' => [
                'username' => base64_encode($username),
                'password' => base64_encode($password),
            ],
        ];

        return $this->createSecret($payload);
    }

    /**
     * 创建 SSH Auth Secret
     */
    public function createSshAuthSecret(string $name, string $sshPrivateKey): SecretDTO
    {
        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => [
                'name' => $name,
                'namespace' => $this->workspace->namespace,
                'labels' => $this->workspace->buildDefaultLabels(null, $name),
            ],
            'type' => 'kubernetes.io/ssh-auth',
            'data' => [
                'ssh-privatekey' => base64_encode($sshPrivateKey),
            ],
        ];

        return $this->createSecret($payload);
    }

    /**
     * 通用创建 Secret 方法
     */
    protected function createSecret(array $payload): SecretDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->post("/api/v1/namespaces/{$this->workspace->namespace}/secrets", $payload);

            Log::info('Secret 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $payload['metadata']['name'],
                'secret_type' => $payload['type'],
            ]);

            return SecretDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('创建 Secret 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $payload['metadata']['name'],
                'secret_type' => $payload['type'],
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 Secret 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 Secret
     */
    public function deleteSecret(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}");

            Log::info('Secret 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('删除 Secret 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('删除 Secret 失败：'.$e->getMessage());
        }
    }

    /**
     * 获取 Secret 数据（解码后的）
     */
    public function getSecretData(string $name): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}");

            $secret = $response->json();
            $data = $secret['data'] ?? [];
            $decodedData = [];

            foreach ($data as $key => $value) {
                $decodedData[$key] = base64_decode($value);
            }

            return $decodedData;
        } catch (\Exception $e) {
            Log::error('获取 Secret 数据失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Secret 数据失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 Secret 数据（通用方法）
     */
    public function updateSecretData(string $name, array $data): SecretDTO
    {
        try {
            // 获取现有的 Secret
            $response = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}");

            if (! $response->successful()) {
                throw new \Exception('获取 Secret 失败: '.$response->body());
            }

            $secret = $response->json();

            // 更新数据，将字符串 base64 编码
            $encodedData = [];
            foreach ($data as $key => $value) {
                $encodedData[$key] = base64_encode($value);
            }

            $secret['data'] = $encodedData;

            // 更新 Secret
            $updateResponse = $this->workspace->cluster->http()
                ->put("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}", $secret);

            if (! $updateResponse->successful()) {
                throw new \Exception('更新 Secret 失败: '.$updateResponse->body());
            }

            Log::info('Secret 数据更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
            ]);

            return SecretDTO::fromK8sResource($updateResponse->json());
        } catch (\Exception $e) {
            Log::error('更新 Secret 数据失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 Secret 数据失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 Generic Secret
     */
    public function updateGenericSecret(string $name, array $data): SecretDTO
    {
        // 获取现有的 Secret
        $existing = $this->workspace->cluster->http()
            ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}")
            ->json();

        $secretData = [];
        foreach ($data as $key => $value) {
            $secretData[$key] = base64_encode($value);
        }

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => $existing['metadata'],
            'type' => 'Opaque',
            'data' => $secretData,
        ];

        return $this->updateSecret($name, $payload);
    }

    /**
     * 更新 Docker Registry Secret
     */
    public function updateDockerRegistrySecret(string $name, string $server, string $username, string $password, ?string $email = null): SecretDTO
    {
        // 获取现有的 Secret
        $existing = $this->workspace->cluster->http()
            ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}")
            ->json();

        $dockerConfig = [
            'auths' => [
                $server => [
                    'username' => $username,
                    'password' => $password,
                    'auth' => base64_encode($username.':'.$password),
                ],
            ],
        ];

        if ($email) {
            $dockerConfig['auths'][$server]['email'] = $email;
        }

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => $existing['metadata'],
            'type' => 'kubernetes.io/dockerconfigjson',
            'data' => [
                '.dockerconfigjson' => base64_encode(json_encode($dockerConfig)),
            ],
        ];

        return $this->updateSecret($name, $payload);
    }

    /**
     * 更新 TLS Secret
     */
    public function updateTlsSecret(string $name, string $cert, string $key): SecretDTO
    {
        // 获取现有的 Secret
        $existing = $this->workspace->cluster->http()
            ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}")
            ->json();

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => $existing['metadata'],
            'type' => 'kubernetes.io/tls',
            'data' => [
                'tls.crt' => base64_encode($cert),
                'tls.key' => base64_encode($key),
            ],
        ];

        return $this->updateSecret($name, $payload);
    }

    /**
     * 更新 Basic Auth Secret
     */
    public function updateBasicAuthSecret(string $name, string $username, string $password): SecretDTO
    {
        // 获取现有的 Secret
        $existing = $this->workspace->cluster->http()
            ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}")
            ->json();

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => $existing['metadata'],
            'type' => 'kubernetes.io/basic-auth',
            'data' => [
                'username' => base64_encode($username),
                'password' => base64_encode($password),
            ],
        ];

        return $this->updateSecret($name, $payload);
    }

    /**
     * 更新 SSH Auth Secret
     */
    public function updateSshAuthSecret(string $name, string $sshPrivateKey): SecretDTO
    {
        // 获取现有的 Secret
        $existing = $this->workspace->cluster->http()
            ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}")
            ->json();

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Secret',
            'metadata' => $existing['metadata'],
            'type' => 'kubernetes.io/ssh-auth',
            'data' => [
                'ssh-privatekey' => base64_encode($sshPrivateKey),
            ],
        ];

        return $this->updateSecret($name, $payload);
    }

    /**
     * 通用更新 Secret 方法
     */
    protected function updateSecret(string $name, array $payload): SecretDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->put("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$name}", $payload);

            Log::info('Secret 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'secret_type' => $payload['type'],
            ]);

            return SecretDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('更新 Secret 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'secret_name' => $name,
                'secret_type' => $payload['type'],
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 Secret 失败：'.$e->getMessage());
        }
    }
}
