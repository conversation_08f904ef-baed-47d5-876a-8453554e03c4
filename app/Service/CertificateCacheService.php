<?php

namespace App\Service;

use Illuminate\Support\Facades\Storage;

/**
 * 证书缓存服务
 *
 * 统一处理 Kubernetes 证书文件的缓存和管理
 */
class CertificateCacheService
{
    /**
     * 缓存证书文件到临时目录
     *
     * @param  string  $type  证书类型 (ca, cert, key)
     * @param  string  $data  Base64 编码的证书数据
     * @return string 返回证书文件的绝对路径
     *
     * @throws \Exception 当解码证书数据失败时抛出异常
     */
    public static function getCachedCertificateFile(string $type, string $data): string
    {
        // 基于数据内容生成 MD5 hash，确保相同内容使用相同文件
        $dataHash = md5($data);
        $storage = Storage::disk('local');
        $fileName = $storage->path("k8s/{$type}_{$dataHash}.pem");

        // 检查 Storage 中是否已存在该文件
        if ($storage->exists($fileName)) {
            // 返回文件的绝对路径
            return $storage->path($fileName);
        }

        // 文件不存在，创建并缓存
        $decodedData = base64_decode($data, true);

        if ($decodedData === false) {
            throw new \Exception("Failed to decode certificate data for type: {$type}");
        }

        $storage->put($fileName, $decodedData);

        // 设置文件权限为只读（如果是本地存储）
        $filePath = $storage->path($fileName);
        if (file_exists($filePath)) {
            chmod($filePath, 0600);
        }

        return $filePath;
    }

    /**
     * 清理过期的证书缓存文件
     *
     * @param  int  $maxAge  最大保留时间（秒），默认7天
     * @return int 返回清理的文件数量
     */
    public static function cleanupExpiredCertificates(int $maxAge = 604800): int
    {
        $cleaned = 0;
        $k8sPath = 'k8s';

        $storage = Storage::disk('local');
        if (! $storage->exists($k8sPath)) {
            return $cleaned;
        }

        $files = $storage->files($k8sPath);
        $cutoffTime = time() - $maxAge;

        foreach ($files as $file) {
            if (preg_match('/(ca|cert|key)_[a-f0-9]{32}\.pem$/', $file)) {
                $filePath = $storage->path($file);
                if (file_exists($filePath) && filemtime($filePath) < $cutoffTime) {
                    $storage->delete($file);
                    $cleaned++;
                }
            }
        }

        return $cleaned;
    }

    /**
     * 清理所有证书缓存文件
     *
     * @return int 返回清理的文件数量
     */
    public static function clearAllCertificates(): int
    {
        $cleaned = 0;
        $k8sPath = 'k8s';

        $storage = Storage::disk('local');
        if (! $storage->exists($k8sPath)) {
            return $cleaned;
        }

        $files = $storage->files($k8sPath);

        foreach ($files as $file) {
            if (preg_match('/(ca|cert|key)_[a-f0-9]{32}\.pem$/', $file)) {
                $storage->delete($file);
                $cleaned++;
            }
        }

        return $cleaned;
    }

    /**
     * 获取证书缓存统计信息
     */
    public static function getCacheStats(): array
    {
        $k8sPath = 'k8s';

        $storage = Storage::disk('local');
        if (! $storage->exists($k8sPath)) {
            return [
                'total_files' => 0,
                'total_size' => 0,
                'ca_files' => 0,
                'cert_files' => 0,
                'key_files' => 0,
            ];
        }

        $files = $storage->files($k8sPath);
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'ca_files' => 0,
            'cert_files' => 0,
            'key_files' => 0,
        ];

        foreach ($files as $file) {
            if (preg_match('/(ca|cert|key)_[a-f0-9]{32}\.pem$/', $file, $matches)) {
                $stats['total_files']++;
                $stats['total_size'] += $storage->size($file);

                $type = $matches[1];
                $stats["{$type}_files"]++;
            }
        }

        return $stats;
    }
}
