<?php

namespace App\Service\IngressDrivers;

use App\Contracts\IngressClassDriverInterface;

class NginxDriver implements IngressClassDriverInterface
{
    public function getIngressClassName(): string
    {
        return 'nginx';
    }

    public function getDefaultAnnotations(): array
    {
        return [
            'nginx.ingress.kubernetes.io/rewrite-target' => '/',
            'nginx.ingress.kubernetes.io/ssl-redirect' => 'true',
            'nginx.ingress.kubernetes.io/use-regex' => 'false',
            'nginx.ingress.kubernetes.io/proxy-body-size' => '1m',
            'nginx.ingress.kubernetes.io/proxy-connect-timeout' => '60',
            'nginx.ingress.kubernetes.io/proxy-send-timeout' => '60',
            'nginx.ingress.kubernetes.io/proxy-read-timeout' => '60',
        ];
    }

    public function getDefaultLabels(): array
    {
        return [
            'ingress.class' => 'nginx',
            'ingress.provider' => 'nginx',
        ];
    }

    public function processRules(array $rules): array
    {
        return array_map(function ($rule) {
            if (! isset($rule['http']['paths'])) {
                return $rule;
            }

            $rule['http']['paths'] = array_map(function ($path) {
                // Nginx 默认使用 Prefix 匹配
                if (! isset($path['pathType'])) {
                    $path['pathType'] = 'Prefix';
                }

                // 确保路径格式正确
                if (! isset($path['path']) || $path['path'] === '') {
                    $path['path'] = '/';
                }

                // Nginx 特定的路径处理
                if ($path['pathType'] === 'Prefix' && ! str_ends_with($path['path'], '/') && $path['path'] !== '/') {
                    $path['path'] .= '/';
                }

                return $path;
            }, $rule['http']['paths']);

            return $rule;
        }, $rules);
    }

    public function processTls(array $tls): array
    {
        return array_map(function ($tlsConfig) {
            // Nginx 需要明确指定 secretName
            if (! isset($tlsConfig['secretName']) && ! empty($tlsConfig['hosts'])) {
                // 为 Nginx 生成更规范的 secret 名称
                $hostHash = substr(md5(implode('-', $tlsConfig['hosts'])), 0, 8);
                $tlsConfig['secretName'] = 'nginx-tls-'.$hostHash;
            }

            return $tlsConfig;
        }, $tls);
    }

    public function validateSpec(array $spec): array
    {
        $errors = [];

        // Nginx 有更严格的验证要求
        if (! isset($spec['rules']) || empty($spec['rules'])) {
            $errors[] = 'Nginx Ingress 必须至少有一个规则';
        }

        if (isset($spec['rules'])) {
            foreach ($spec['rules'] as $index => $rule) {
                if (! isset($rule['host']) || empty($rule['host'])) {
                    $errors[] = "规则 {$index}: Nginx Ingress 必须指定主机名";
                }

                // 验证主机名格式
                if (isset($rule['host']) && ! $this->isValidHostname($rule['host'])) {
                    $errors[] = "规则 {$index}: 主机名格式无效";
                }

                if (! isset($rule['http']['paths']) || empty($rule['http']['paths'])) {
                    $errors[] = "规则 {$index}: 必须至少有一个路径";
                }

                if (isset($rule['http']['paths'])) {
                    foreach ($rule['http']['paths'] as $pathIndex => $path) {
                        if (! isset($path['backend']['service']['name'])) {
                            $errors[] = "规则 {$index} 路径 {$pathIndex}: 必须指定后端服务名称";
                        }

                        if (! isset($path['backend']['service']['port']['number'])) {
                            $errors[] = "规则 {$index} 路径 {$pathIndex}: 必须指定后端服务端口";
                        }

                        // 验证路径格式
                        if (isset($path['path']) && ! $this->isValidPath($path['path'])) {
                            $errors[] = "规则 {$index} 路径 {$pathIndex}: 路径格式无效";
                        }
                    }
                }
            }
        }

        // 验证 TLS
        if (isset($spec['tls'])) {
            foreach ($spec['tls'] as $index => $tlsConfig) {
                if (! isset($tlsConfig['hosts']) || empty($tlsConfig['hosts'])) {
                    $errors[] = "TLS 配置 {$index}: 必须指定主机列表";
                }

                if (! isset($tlsConfig['secretName']) || empty($tlsConfig['secretName'])) {
                    $errors[] = "TLS 配置 {$index}: Nginx 必须指定 secretName";
                }
            }
        }

        return $errors;
    }

    public function getHealthCheckConfig(): array
    {
        return [
            'enabled' => true,
            'path' => '/nginx-health',
            'interval' => '10s',
            'timeout' => '3s',
        ];
    }

    /**
     * 验证主机名格式
     */
    private function isValidHostname(string $hostname): bool
    {
        // 支持泛域名
        if (str_starts_with($hostname, '*.')) {
            $hostname = substr($hostname, 2);
        }

        return filter_var($hostname, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) !== false;
    }

    /**
     * 验证路径格式
     */
    private function isValidPath(string $path): bool
    {
        // 路径必须以 / 开头
        if (! str_starts_with($path, '/')) {
            return false;
        }

        // 检查是否包含无效字符
        if (preg_match('/[^a-zA-Z0-9\/_\-\.]/', $path)) {
            return false;
        }

        return true;
    }

    /**
     * 获取 Nginx 特定的配置片段
     */
    public function getNginxConfigSnippets(): array
    {
        return [
            'server-snippet' => 'gzip on; gzip_types text/plain text/css application/json application/javascript;',
            'location-snippet' => 'proxy_set_header X-Real-IP $remote_addr;',
        ];
    }
}
