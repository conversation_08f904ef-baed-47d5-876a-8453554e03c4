<?php

namespace App\Service\IngressDrivers;

use App\Contracts\IngressClassDriverInterface;

class TraefikDriver implements IngressClassDriverInterface
{
    public function getIngressClassName(): string
    {
        return 'traefik';
    }

    public function getDefaultAnnotations(): array
    {
        return [
            'traefik.ingress.kubernetes.io/router.entrypoints' => 'web,websecure',
        ];
    }

    public function getDefaultLabels(): array
    {
        return [
            'ingress.class' => 'traefik',
            'ingress.provider' => 'traefik',
        ];
    }

    public function processRules(array $rules): array
    {
        // Traefik 通常不需要特殊处理规则，但我们可以添加一些默认配置
        return array_map(function ($rule) {
            // 确保每个规则都有正确的格式
            if (! isset($rule['http']['paths'])) {
                return $rule;
            }

            $rule['http']['paths'] = array_map(function ($path) {
                // Traefik 默认使用 Prefix 匹配
                if (! isset($path['pathType'])) {
                    $path['pathType'] = 'Prefix';
                }

                // 确保路径以 / 开头
                if (! isset($path['path']) || $path['path'] === '') {
                    $path['path'] = '/';
                }

                return $path;
            }, $rule['http']['paths']);

            return $rule;
        }, $rules);
    }

    public function processTls(array $tls): array
    {
        // Traefik 支持自动 TLS，我们可以添加一些默认配置
        return array_map(function ($tlsConfig) {
            // 如果没有指定 secretName 但有主机名，生成标准化的 secret 名称
            if (! isset($tlsConfig['secretName']) && ! empty($tlsConfig['hosts'])) {
                // 生成一个基于主机名的 secret 名称，用于自动证书生成
                $hostHash = substr(md5(implode('-', $tlsConfig['hosts'])), 0, 8);
                $tlsConfig['secretName'] = 'auto-tls-'.$hostHash;
            }

            return $tlsConfig;
        }, $tls);
    }

    public function validateSpec(array $spec): array
    {
        $errors = [];

        // 验证规则
        if (isset($spec['rules'])) {
            foreach ($spec['rules'] as $index => $rule) {
                if (! isset($rule['host']) || empty($rule['host'])) {
                    $errors[] = "规则 {$index}: 必须指定主机名";
                }

                if (! isset($rule['http']['paths']) || empty($rule['http']['paths'])) {
                    $errors[] = "规则 {$index}: 必须至少有一个路径";
                }

                if (isset($rule['http']['paths'])) {
                    foreach ($rule['http']['paths'] as $pathIndex => $path) {
                        if (! isset($path['backend']['service']['name'])) {
                            $errors[] = "规则 {$index} 路径 {$pathIndex}: 必须指定后端服务名称";
                        }

                        if (! isset($path['backend']['service']['port']['number'])) {
                            $errors[] = "规则 {$index} 路径 {$pathIndex}: 必须指定后端服务端口";
                        }
                    }
                }
            }
        }

        // 验证 TLS
        if (isset($spec['tls'])) {
            foreach ($spec['tls'] as $index => $tlsConfig) {
                if (! isset($tlsConfig['hosts']) || empty($tlsConfig['hosts'])) {
                    $errors[] = "TLS 配置 {$index}: 必须指定主机列表";
                }
            }
        }

        return $errors;
    }

    public function getHealthCheckConfig(): array
    {
        return [
            'enabled' => true,
            'path' => '/ping',
            'interval' => '30s',
            'timeout' => '5s',
        ];
    }

    /**
     * 获取 Traefik 特定的中间件配置
     */
    public function getDefaultMiddlewares(): array
    {
        return [
            'default-headers' => [
                'headers' => [
                    'frameDeny' => true,
                    'sslRedirect' => true,
                    'browserXssFilter' => true,
                    'contentTypeNosniff' => true,
                    'forceSTSHeader' => true,
                    'stsIncludeSubdomains' => true,
                    'stsPreload' => true,
                    'stsSeconds' => 31536000,
                ],
            ],
        ];
    }
}
