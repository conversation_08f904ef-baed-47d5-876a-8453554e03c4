<?php

namespace App\Service;

use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IpPoolService
{
    protected LoadBalancerManager $loadBalancerManager;

    public function __construct()
    {
        $this->loadBalancerManager = new LoadBalancerManager;
    }

    /**
     * 根据策略分配 IP
     */
    public function allocateIp(IpPool $ipPool, bool $allowSharedIp = true): ?PoolIp
    {
        if ($allowSharedIp) {
            // IP 共享逻辑
            // 1. 尝试寻找一个可重用的 IP（已使用，但有空闲端口）
            //    按使用次数降序排列，以便优先填满已在使用的 IP
            $reusableIp = $ipPool->activePoolIps()
                ->where('usage_count', '>', 0)
                ->orderBy('usage_count', 'desc')
                ->get()
                ->first(function (PoolIp $ip) {
                    return $ip->available_ports_count > 0;
                });

            if ($reusableIp) {
                return $reusableIp;
            }

            // 2. 如果没有可重用的 IP，则回退到分配一个全新的、未使用的 IP
            //    `allocateByLeastUsed` 会选择一个 usage_count 为 0 的 IP
            return $this->allocateByLeastUsed($ipPool);
        } else {
            // 不共享 IP 的逻辑
            // 只选择一个从未使用过的 IP
            return $ipPool->activePoolIps()
                ->where('usage_count', 0)
                ->orderBy('id', 'asc') // 保证一致性
                ->first();
        }
    }

    /**
     * 自动分配 IP 池和 IP（顺序选择）
     */
    public function autoAllocateIp(int $clusterId, bool $allowSharedIp = true): ?PoolIp
    {
        // 获取集群的活跃 IP 池，按 ID 顺序排序
        $ipPools = IpPool::where('cluster_id', $clusterId)
            ->where('is_active', true)
            ->orderBy('id', 'asc')
            ->get();

        if ($ipPools->isEmpty()) {
            throw new \Exception('集群中没有可用的 IP 池');
        }

        // 顺序尝试每个 IP 池
        foreach ($ipPools as $ipPool) {
            $poolIp = $this->allocateIp($ipPool, $allowSharedIp);
            if ($poolIp) {
                return $poolIp;
            }
        }

        $message = $allowSharedIp
            ? '集群中没有可用的共享 IP 地址'
            : '集群中没有可用的独占 IP 地址';
        throw new \Exception($message);
    }

    /**
     * 分配端口 - 高性能算法
     */
    public function allocatePort(PoolIp $poolIp, string $serviceName, string $namespace): ?int
    {
        // 首先检查是否已经为这个服务分配了端口
        $existingAllocation = PortAllocation::where('service_name', $serviceName)
            ->where('namespace', $namespace)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->first();

        if ($existingAllocation) {
            return $existingAllocation->port;
        }

        // 策略1: 优先复用已释放的端口（排除禁用的端口）
        $releasedAllocation = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_RELEASED)
            ->where('is_disabled', false) // 排除禁用的端口
            ->whereBetween('port', [$poolIp->port_range_start, $poolIp->port_range_end])
            ->orderBy('released_at', 'desc')
            ->first();

        if ($releasedAllocation) {
            $updated = PortAllocation::where('id', $releasedAllocation->id)
                ->where('status', PortAllocation::STATUS_RELEASED)
                ->where('is_disabled', false) // 再次确认不是禁用的
                ->update([
                    'service_name' => $serviceName,
                    'namespace' => $namespace,
                    'status' => PortAllocation::STATUS_ALLOCATED,
                    'allocated_at' => now(),
                    'released_at' => null,
                ]);

            if ($updated) {
                $poolIp->incrementUsage();

                return (int) $releasedAllocation->port;
            }
        }

        // 策略2: 分配新端口
        // 获取最大已分配端口号来优化起始位置
        $maxPort = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', PortAllocation::STATUS_ALLOCATED)
            ->max('port');

        $startPort = $maxPort ? max($maxPort + 1, $poolIp->port_range_start) : $poolIp->port_range_start;

        // 从起始端口开始分配
        for ($port = $startPort; $port <= $poolIp->port_range_end; $port++) {
            // 检查端口是否已被占用或禁用
            $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                ->where('port', $port)
                ->where(function ($query) {
                    $query->where('status', '!=', PortAllocation::STATUS_RELEASED)
                        ->orWhere('is_disabled', true);
                })
                ->exists();

            if (! $exists) {
                try {
                    // 尝试创建新的端口分配
                    $allocation = PortAllocation::create([
                        'pool_ip_id' => $poolIp->id,
                        'port' => $port,
                        'service_name' => $serviceName,
                        'namespace' => $namespace,
                        'status' => PortAllocation::STATUS_ALLOCATED,
                        'allocated_at' => now(),
                    ]);

                    if ($allocation) {
                        // 更新使用计数
                        $poolIp->incrementUsage();

                        return $port;
                    }
                } catch (\Illuminate\Database\QueryException $e) {
                    // 如果是唯一约束冲突，继续尝试下一个端口
                    if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                        str_contains($e->getMessage(), 'Duplicate entry')) {
                        continue;
                    }
                    throw $e;
                }
            }
        }

        // 如果从优化起始点开始没有找到，从头开始搜索
        if ($startPort > $poolIp->port_range_start) {
            for ($port = $poolIp->port_range_start; $port < $startPort; $port++) {
                $exists = PortAllocation::where('pool_ip_id', $poolIp->id)
                    ->where('port', $port)
                    ->where(function ($query) {
                        $query->where('status', '!=', PortAllocation::STATUS_RELEASED)
                            ->orWhere('is_disabled', true);
                    })
                    ->exists();

                if (! $exists) {
                    try {
                        $allocation = PortAllocation::create([
                            'pool_ip_id' => $poolIp->id,
                            'port' => $port,
                            'service_name' => $serviceName,
                            'namespace' => $namespace,
                            'status' => PortAllocation::STATUS_ALLOCATED,
                            'allocated_at' => now(),
                        ]);

                        if ($allocation) {
                            $poolIp->incrementUsage();

                            return $port;
                        }
                    } catch (\Illuminate\Database\QueryException $e) {
                        if (str_contains($e->getMessage(), 'duplicate key value violates unique constraint') ||
                            str_contains($e->getMessage(), 'Duplicate entry')) {
                            continue;
                        }
                        throw $e;
                    }
                }
            }
        }

        // 如果所有端口都被占用
        throw new \Exception('IP '.$poolIp->ip_address.' 的端口已耗尽');
    }

    /**
     * 释放端口
     */
    public function releasePort(string $serviceName, string $namespace): void
    {
        DB::transaction(function () use ($serviceName, $namespace) {
            // 先获取要释放的端口信息，用于更新使用计数
            $allocationsToRelease = DB::select('
                SELECT pool_ip_id, COUNT(*) as count
                FROM port_allocations 
                WHERE service_name = ? 
                    AND namespace = ? 
                    AND status = ?
                GROUP BY pool_ip_id
            ', [$serviceName, $namespace, PortAllocation::STATUS_ALLOCATED]);

            // 使用原生SQL批量更新，性能更好
            $updatedRows = DB::update('
                UPDATE port_allocations 
                SET status = ?, 
                    released_at = ?, 
                    updated_at = ?
                WHERE service_name = ? 
                    AND namespace = ? 
                    AND status = ?
            ', [
                PortAllocation::STATUS_RELEASED,
                now(),
                now(),
                $serviceName,
                $namespace,
                PortAllocation::STATUS_ALLOCATED,
            ]);

            if ($updatedRows > 0) {
                // 批量更新 PoolIp 的使用计数
                foreach ($allocationsToRelease as $allocation) {
                    DB::update('
                        UPDATE pool_ips 
                        SET usage_count = GREATEST(0, usage_count - ?), 
                            updated_at = ?
                        WHERE id = ?
                    ', [$allocation->count, now(), $allocation->pool_ip_id]);
                }
            }
        });
    }

    /**
     * 同步 IP 池到 K8s 集群
     */
    public function syncToKubernetes(IpPool $ipPool): void
    {
        try {
            $driver = $this->loadBalancerManager->driver($ipPool->driver);
            $driver->syncIpPool($ipPool);

            // 更新同步状态
            $ipPool->update([
                'synced_to_k8s' => true,
                'last_sync_at' => now(),
                'sync_error' => null,
            ]);

        } catch (\Exception $e) {
            // 获取详细错误信息
            $errorMessage = $e->getMessage();

            // 更新同步错误状态
            $ipPool->update([
                'synced_to_k8s' => false,
                'sync_error' => $errorMessage,
            ]);

            Log::error('同步 IP 池到 K8s 失败', [
                'pool_id' => $ipPool->id,
                'pool_name' => $ipPool->name,
                'driver' => $ipPool->driver,
                'error' => $errorMessage,
            ]);
            throw new \Exception('同步到 K8s 失败: '.$errorMessage);
        }
    }

    /**
     * 从 K8s 集群删除 IP 池配置
     */
    public function deleteFromKubernetes(IpPool $ipPool): void
    {
        try {
            $driver = $this->loadBalancerManager->driver($ipPool->driver);
            $driver->deleteIpPool($ipPool);

        } catch (\Exception $e) {
            Log::error('从 K8s 删除 IP 池失败', [
                'pool_id' => $ipPool->id,
                'pool_name' => $ipPool->name,
                'driver' => $ipPool->driver,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('从 K8s 删除失败: '.$e->getMessage());
        }
    }

    /**
     * 获取 IP 池统计信息
     */
    public function getPoolStats(IpPool $ipPool): array
    {
        $totalIps = $ipPool->poolIps()->count();
        $activeIps = $ipPool->activePoolIps()->count();
        $totalPorts = $ipPool->poolIps()
            ->selectRaw('SUM(port_range_end - port_range_start + 1) as total')
            ->value('total') ?? 0;

        $allocatedPorts = PortAllocation::whereHas('poolIp', function ($query) use ($ipPool) {
            $query->where('ip_pool_id', $ipPool->id);
        })->where('status', 'allocated')->count();

        $utilizationRate = $totalPorts > 0 ? round(($allocatedPorts / $totalPorts) * 100, 2) : 0;

        return [
            'total_ips' => $totalIps,
            'active_ips' => $activeIps,
            'inactive_ips' => $totalIps - $activeIps,
            'total_ports' => $totalPorts,
            'allocated_ports' => $allocatedPorts,
            'available_ports' => $totalPorts - $allocatedPorts,
            'utilization_rate' => $utilizationRate,
        ];
    }

    /**
     * 最少使用策略
     */
    protected function allocateByLeastUsed(IpPool $ipPool): ?PoolIp
    {
        return $ipPool->activePoolIps()
            ->orderBy('usage_count', 'asc')
            ->orderBy('id', 'asc') // 使用次数相同时按 ID 排序保证一致性
            ->first();
    }

    /**
     * 轮询策略
     */
    protected function allocateByRoundRobin(IpPool $ipPool): ?PoolIp
    {
        $activeIps = $ipPool->activePoolIps()->get();

        if ($activeIps->isEmpty()) {
            return null;
        }

        // 简单的轮询实现：根据当前时间戳选择
        $index = time() % $activeIps->count();

        return $activeIps->get($index);
    }

    /**
     * 随机策略
     */
    protected function allocateByRandom(IpPool $ipPool): ?PoolIp
    {
        return $ipPool->activePoolIps()
            ->inRandomOrder()
            ->first();
    }

    /**
     * 顺序分配策略
     */
    protected function allocateBySequential(IpPool $ipPool): ?PoolIp
    {
        return $ipPool->activePoolIps()
            ->orderBy('id', 'asc')
            ->first();
    }

    /**
     * 构建 IP 池范围列表
     */
    protected function buildIpPoolRanges(IpPool $ipPool): string
    {
        $ips = [];

        // 收集所有活跃的 IP 地址
        foreach ($ipPool->poolIps as $poolIp) {
            if (! $poolIp->is_active) {
                continue;
            }
            $ips[] = $poolIp->ip_address;
        }

        if (empty($ips)) {
            return '';
        }

        // 对 IP 地址排序
        usort($ips, function ($a, $b) {
            return ip2long($a) - ip2long($b);
        });

        // 查找连续的 IP 范围
        $ranges = [];
        $start = $ips[0];
        $end = $ips[0];

        for ($i = 1; $i < count($ips); $i++) {
            if (ip2long($ips[$i]) == ip2long($end) + 1) {
                // 连续的 IP
                $end = $ips[$i];
            } else {
                // 断开，保存当前范围
                if ($start === $end) {
                    $ranges[] = $start.'-'.$end;
                } else {
                    $ranges[] = $start.'-'.$end;
                }
                $start = $ips[$i];
                $end = $ips[$i];
            }
        }

        // 添加最后一个范围
        if ($start === $end) {
            $ranges[] = $start.'-'.$end;
        } else {
            $ranges[] = $start.'-'.$end;
        }

        return implode(',', $ranges);
    }
}
