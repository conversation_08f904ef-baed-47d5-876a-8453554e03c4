<?php

namespace App\Traits;

use App\Models\Task;
use App\Models\Workspace;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use InvalidArgumentException;

trait HasTasks
{
    /**
     * 获取所有任务
     */
    public function tasks(): MorphMany
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    /**
     * 获取待处理的任务
     */
    public function pendingTasks(): MorphMany
    {
        return $this->tasks()->where('status', Task::STATUS_PENDING);
    }

    /**
     * 获取运行中的任务
     */
    public function runningTasks(): MorphMany
    {
        return $this->tasks()->where('status', Task::STATUS_RUNNING);
    }

    /**
     * 获取已完成的任务
     */
    public function completedTasks(): MorphMany
    {
        return $this->tasks()->where('status', Task::STATUS_COMPLETED);
    }

    /**
     * 获取失败的任务
     */
    public function failedTasks(): MorphMany
    {
        return $this->tasks()->where('status', Task::STATUS_FAILED);
    }

    /**
     * 创建任务
     */
    public function createTask(string $type, array $data = [], int $maxAttempts = 3): Task
    {
        // 自动获取 workspace_id
        $workspaceId = $this->getWorkspaceId();

        if (empty($workspaceId)) {
            throw new InvalidArgumentException('无法为模型 '.get_class($this).' 获取 workspace_id');
        }

        return $this->tasks()->create([
            'workspace_id' => $workspaceId,
            'type' => $type,
            'status' => Task::STATUS_PENDING,
            'data' => $data,
            'max_attempts' => $maxAttempts,
        ]);
    }

    /**
     * 获取模型的 workspace_id
     */
    protected function getWorkspaceId(): ?int
    {
        // 如果当前模型就是 Workspace，返回自己的 ID
        if ($this instanceof Workspace) {
            return $this->id;
        }

        // 如果模型有 workspace_id 属性，直接返回
        if (property_exists($this, 'workspace_id') && ! empty($this->workspace_id)) {
            return $this->workspace_id;
        }

        // 如果模型有 workspace 关系，返回关系的 ID
        if (method_exists($this, 'workspace') && $this->workspace) {
            return $this->workspace->id;
        }

        return null;
    }

    /**
     * 处理任务的抽象方法
     * 子类必须实现此方法来定义如何处理任务
     */
    abstract public function handleTask(Task $task): void;

    /**
     * 获取特定类型的任务
     */
    public function getTasksByType(string $type): MorphMany
    {
        return $this->tasks()->where('type', $type);
    }

    /**
     * 检查是否有待处理的任务
     */
    public function hasPendingTasks(): bool
    {
        return $this->pendingTasks()->exists();
    }

    /**
     * 检查是否有运行中的任务
     */
    public function hasRunningTasks(): bool
    {
        return $this->runningTasks()->exists();
    }

    /**
     * 获取最新的任务
     */
    public function latestTask(): ?Task
    {
        return $this->tasks()->latest()->first();
    }

    /**
     * 获取特定类型的最新任务
     */
    public function latestTaskByType(string $type): ?Task
    {
        return $this->getTasksByType($type)->latest()->first();
    }
}
