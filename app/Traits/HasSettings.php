<?php

namespace App\Traits;

use App\Models\Setting;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\Cache;

trait HasSettings
{
    /**
     * 获取模型的所有设置
     */
    public function settings(): MorphMany
    {
        return $this->morphMany(Setting::class, 'settingable');
    }

    /**
     * 生成缓存键
     */
    protected function generateCacheKey(string $key): string
    {
        return sprintf('setting:%s:%s:%s', get_class($this), $this->getKey(), $key);
    }

    /**
     * 获取指定键的设置值（带缓存）
     */
    public function getSetting(string $key, $default = null)
    {
        $cacheKey = $this->generateCacheKey($key);

        // 尝试从缓存获取
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // 缓存未命中，查询数据库
        $setting = $this->settings()->where('key', $key)->first();

        if ($setting) {
            Cache::put($cacheKey, $setting->value, 86400); // 缓存1天

            return $setting->value;
        }

        return $default;
    }

    /**
     * 设置指定键的值（自动清除缓存）
     */
    public function setSetting(string $key, $value)
    {
        $setting = $this->settings()->updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        Cache::forget($this->generateCacheKey($key));

        return $setting;
    }

    /**
     * 批量设置（自动清除每个键的缓存）
     */
    public function setSettings(array $settings)
    {
        $result = [];
        foreach ($settings as $key => $value) {
            $result[$key] = $this->setSetting($key, $value);
        }

        return $result;
    }

    /**
     * 删除指定设置（自动清除缓存）
     */
    public function deleteSetting(string $key)
    {
        $deleted = $this->settings()->where('key', $key)->delete();
        if ($deleted) {
            Cache::forget($this->generateCacheKey($key));
        }

        return (bool) $deleted;
    }

    /**
     * 批量删除设置（自动清除缓存）
     */
    public function deleteSettings(array $keys)
    {
        $deletedCount = $this->settings()->whereIn('key', $keys)->delete();
        if ($deletedCount > 0) {
            foreach ($keys as $key) {
                Cache::forget($this->generateCacheKey($key));
            }
        }

        return $deletedCount;
    }

    /**
     * 清空所有设置（自动清除所有相关缓存）
     */
    public function clearSettings()
    {
        // 先获取所有键再删除记录
        $keys = $this->settings()->pluck('key')->all();
        $deletedCount = $this->settings()->delete();

        if ($deletedCount > 0) {
            foreach ($keys as $key) {
                Cache::forget($this->generateCacheKey($key));
            }
        }

        return $deletedCount;
    }

    /**
     * 获取所有设置（建议直接访问settings关系）
     */
    public function getAllSettings()
    {
        return $this->settings()->get();
    }

    /**
     * 获取指定键的布尔值设置
     *
     * @param  string  $key  设置键名
     * @param  bool  $default  默认值，当设置不存在时返回
     * @return bool 返回布尔值
     */
    public function getSettingBool(string $key, bool $default = false): bool
    {
        $value = $this->getSetting($key, $default);

        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * 设置指定键的布尔值
     *
     * @param  string  $key  设置键名
     * @param  bool  $value  要设置的布尔值
     * @return Setting
     */
    public function setSettingBool(string $key, bool $value)
    {
        // 将布尔值转换为字符串 'true' 或 'false'
        return $this->setSetting($key, $value ? 'true' : 'false');
    }
}
