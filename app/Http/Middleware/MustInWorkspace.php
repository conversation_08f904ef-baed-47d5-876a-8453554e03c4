<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class MustInWorkspace
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        $currentWorkspace = $user->currentWorkspace;

        if (! $currentWorkspace) {
            if ($request->expectsJson()) {
                return response([
                    'message' => '请先选择工作空间',
                ], 422);
            } else {
                return redirect(route('workspaces.index'))->withErrors('请先选择一个工作空间。');
            }
        }

        if (! $currentWorkspace->isActive()) {
            if ($request->expectsJson()) {
                return response([
                    'message' => '工作区不可用',
                ], 422);
            } else {
                return redirect(route('workspaces.index'))->withErrors('工作区不可用');
            }
        }

        // 设置权限检查的工作空间 ID
        setPermissionsTeamId($user->current_workspace_id);

        return $next($request);
    }
}
