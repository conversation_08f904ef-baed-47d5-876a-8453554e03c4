<?php

namespace App\Http\Middleware;

use App\Models\Workspace;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WorkspaceMustActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取 Workspace
        $workspace = Workspace::find(getPermissionsTeamId());

        if ($workspace && ! $workspace->isActive()) {
            // 如果是 API
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Workspace is not active',
                ], 400);
            } else {
                return redirect()->route('workspaces.index')->with('error', '工作区不可用');
            }
        }

        return $next($request);
    }
}
