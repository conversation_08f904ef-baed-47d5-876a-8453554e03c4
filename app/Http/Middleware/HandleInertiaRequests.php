<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        // if authed
        $user = $request->user();

        $auth = [
            'user' => null,
            'workspace' => null,
        ];

        if ($user) {
            $user = $user->load('currentWorkspace');
        }

        $auth['user'] = $user;

        if (! empty($user->currentWorkspace)) {
            $auth['workspace'] = $user->currentWorkspace;
        }

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'display_name' => config('app.display_name'),
            'auth' => $auth,
            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'success' => fn () => $request->session()->get('success'),
                'error' => fn () => $request->session()->get('error'),
                'info' => fn () => $request->session()->get('info'),
            ],
            // 'ziggy' => [
            //     ...(new Ziggy)->toArray(),
            //     'location' => $request->url(),
            // ],
            // 'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
        ];
    }
}
