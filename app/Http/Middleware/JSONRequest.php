<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class JSONRequest
{
    /**
     * 处理传入的请求，确保默认使用 JSON 格式
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查 Accept 头是否存在或为空
        if (! $request->headers->has('Accept') || empty($request->header('Accept'))) {
            // 设置默认 Accept 头为 application/json
            $request->headers->set('Accept', 'application/json');
        }

        if (! $request->headers->has('Content-Type') || empty($request->header('Content-Type'))) {
            // 设置默认 Accept 头为 application/json
            $request->headers->set('Content-Type', 'application/json');
        }

        return $next($request);
    }
}
