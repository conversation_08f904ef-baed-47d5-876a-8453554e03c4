<?php

namespace App\Http\Middleware;

use App\Models\Workspace;
use Closure;
use Illuminate\Http\Request;

class WorkspacePermission
{
    /**
     * Handle an incoming request.
     *
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $user = $request->user();

        if (! $user) {
            return $next($request);
        }

        // Get workspace ID from various sources
        $workspaceId = null;

        // 1. From current user's workspace
        if ($user->current_workspace_id) {
            $workspaceId = $user->current_workspace_id;
        }

        // 2. From request header (useful for API calls)
        if ($request->header('X-Workspace-ID')) {
            $headerWorkspaceId = $request->header('X-Workspace-ID');

            $workspace = Workspace::find($headerWorkspaceId);

            if (! $workspace) {
                abort(404, 'Workspace not found');
            }

            // Verify user belongs to this workspace
            if ($workspace->user_id !== $user->id) {
                abort(403, 'You do not belong to this workspace');
            }

            $workspaceId = $headerWorkspaceId;
        }

        // Set the workspace ID for permission checks
        if ($workspaceId) {
            setPermissionsTeamId($workspaceId);
        }

        // Check permission if specified
        if ($permission) {
            if (! $user->hasPermissionTo($permission, 'web')) {
                if (! $workspaceId || ! $user->hasWorkspacePermission($workspaceId, $permission)) {
                    abort(403, 'You do not have the required permission');
                }
            }
        }

        return $next($request);
    }
}
