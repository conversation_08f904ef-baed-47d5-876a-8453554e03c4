<?php

namespace App\Http\Requests;

use App\Service\DeploymentService;
use App\Service\ServiceService;
use App\Service\StatefulSetService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'target_workload_type' => 'required|string|in:Deployment,StatefulSet',
            'target_workload_name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'ports' => 'required|array|min:1',
            'ports.*.name' => 'nullable|string|max:15',
            'ports.*.port' => 'required|integer|min:1|max:65535',
            'ports.*.target_port' => 'required|integer|min:1|max:65535',
            'ports.*.protocol' => 'nullable|string|in:TCP,UDP',
            'session_affinity' => 'nullable|string|in:None,ClientIP',
            'external_traffic_policy' => 'nullable|string|in:Cluster,Local',
            'allow_shared_ip' => 'nullable|boolean',
            'ip_pool' => 'nullable|string|exists:ip_pools,name',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'target_workload_type.in' => '目标工作负载类型只能是 Deployment 或 StatefulSet',
            'target_workload_name.required' => '必须指定目标工作负载名称',
            'target_workload_name.regex' => '目标工作负载名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'ports.required' => '至少需要配置一个端口',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();

            $this->validateTargetWorkload($data, $validator);

            $workspace = $this->user()->getWorkspace();
            $serviceName = $this->route('service');

            // 只有当 serviceName 不为空时才进行验证
            if (! $serviceName) {
                return;
            }

            try {
                $serviceService = new ServiceService($workspace, app(\App\Service\IpPoolService::class));
                $existingService = $serviceService->getService($serviceName);

                if ($existingService->type === 'LoadBalancer') {
                    $this->validateLoadBalancerRules($data, $validator);
                }
            } catch (\Exception $e) {
                // service not found, let it pass, controller will handle
            }
        });
    }

    /**
     * 验证目标工作负载是否存在
     */
    protected function validateTargetWorkload(array $data, $validator): void
    {
        $workspace = $this->user()->getWorkspace();
        $workloadType = $data['target_workload_type'] ?? null;
        $workloadName = $data['target_workload_name'] ?? null;

        if (! $workloadType || ! $workloadName) {
            return;
        }

        try {
            if ($workloadType === 'Deployment') {
                $deploymentService = new DeploymentService($workspace);
                if (! $deploymentService->getDeployment($workloadName)) {
                    $validator->errors()->add('target_workload_name', '指定的 Deployment 不存在：'.$workloadName);
                }
            } elseif ($workloadType === 'StatefulSet') {
                $statefulSetService = new StatefulSetService($workspace);
                if (! $statefulSetService->getStatefulSet($workloadName)) {
                    $validator->errors()->add('target_workload_name', '指定的 StatefulSet 不存在：'.$workloadName);
                }
            }
        } catch (\Exception $e) {
            $validator->errors()->add('target_workload_name', '验证工作负载时出错: '.$e->getMessage());
        }
    }

    /**
     * 验证 LoadBalancer 特殊规则
     */
    protected function validateLoadBalancerRules(array $data, $validator): void
    {
        $allowSharedIp = $data['allow_shared_ip'] ?? false;
        $externalTrafficPolicy = $data['external_traffic_policy'] ?? 'Cluster';

        if ($allowSharedIp && $externalTrafficPolicy === 'Local') {
            $validator->errors()->add(
                'external_traffic_policy',
                '启用 IP 共享时不支持 Local 外部流量策略。Address Sharing 不支持 externalTrafficPolicy: Local，因为这可能导致没有 Pod 的节点被通告，造成流量丢失。'
            );
        }
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('target_workload_name')) {
            $this->merge([
                'selector' => [
                    'app' => $this->input('target_workload_name'),
                ],
            ]);
        }
    }
}
