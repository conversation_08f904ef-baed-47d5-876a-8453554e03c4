<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreConfigMapFromFilesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'files' => 'required|array|min:1',
            'files.*' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'ConfigMap 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
        ];
    }
}
