<?php

namespace App\Http\Requests;

use App\Service\WorkspaceService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateWorkspaceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $workspace = $this->route('workspace');

        return [
            'name' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'regex:/^[a-zA-Z][a-zA-Z0-9\-_]*$/',
                function ($attribute, $value, $fail) use ($workspace) {
                    $workspaceService = app(WorkspaceService::class);
                    // 检查名称唯一性，但排除当前工作空间
                    if (! $workspaceService->isWorkspaceNameUnique($value, auth()->id(), $workspace->id)) {
                        $fail('工作空间名称已存在');
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => '工作空间名称不能为空',
            'name.min' => '工作空间名称至少需要3个字符',
            'name.max' => '工作空间名称不能超过50个字符',
            'name.regex' => '工作空间名称必须以字母开头，可以包含字母、数字、连字符和下划线',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => '工作空间名称',
        ];
    }
}
