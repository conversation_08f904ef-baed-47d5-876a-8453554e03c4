<?php

namespace App\Http\Requests;

class UpdateHorizontalPodAutoscalerRequest extends StoreHorizontalPodAutoscalerRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = parent::rules();

        // 更新时名称是可选的，因为通常通过路由参数传递
        $rules['name'] = 'sometimes|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/';

        return $rules;
    }
}
