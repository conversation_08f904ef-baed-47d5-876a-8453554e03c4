<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateConfigMapRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'data' => 'sometimes|array',
            'data.*' => 'required|string',
            'binary_data' => 'sometimes|array',
            'binary_data.*' => 'required|string',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (! $this->has('data') && ! $this->has('binary_data')) {
                $validator->errors()->add('data', '必须提供 data 或 binary_data');
            }
        });
    }
}
