<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreIngressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'rules' => 'required|array|min:1',
            'rules.*.host' => 'required|string|max:253',
            'rules.*.http.paths' => 'required|array|min:1',
            'rules.*.http.paths.*.path' => 'required|string',
            'rules.*.http.paths.*.pathType' => 'required|string|in:Exact,Prefix,ImplementationSpecific',
            'rules.*.http.paths.*.backend.service.name' => 'required|string',
            'rules.*.http.paths.*.backend.service.port.number' => 'required|integer|min:1|max:65535',
            'tls' => 'sometimes|array',
            'tls.*.hosts' => 'required_with:tls|array',
            'tls.*.hosts.*' => 'required|string',
            'tls.*.secretName' => 'sometimes|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'Ingress 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'rules.*.host.required' => '每个规则必须指定主机名',
            'rules.*.http.paths.*.backend.service.name.required' => '每个路径必须指定后端服务名称',
            'rules.*.http.paths.*.backend.service.port.number.required' => '每个路径必须指定后端服务端口',
        ];
    }
}
