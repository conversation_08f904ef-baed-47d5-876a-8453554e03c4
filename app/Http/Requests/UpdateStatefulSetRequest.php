<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStatefulSetRequest extends FormRequest
{
    use BaseWorkloadValidation;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(
            $this->getBaseRules(),
            [
                'service_name' => 'nullable|string|max:63',
                'update_strategy' => 'nullable|string|in:RollingUpdate,OnDelete',
            ]
        );
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return $this->getBaseMessages();
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $this->validateResources($validator);
    }
}
