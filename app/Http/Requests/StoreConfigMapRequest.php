<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreConfigMapRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $workspace = $this->user()->getWorkspace();

        return $this->user()->can('update', $workspace);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'data' => 'sometimes|array',
            'data.*' => 'required|string',
            'binary_data' => 'sometimes|array',
            'binary_data.*' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'ConfigMap 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (! $this->has('data') && ! $this->has('binary_data')) {
                $validator->errors()->add('data', '必须提供 data 或 binary_data');
            }
        });
    }
}
