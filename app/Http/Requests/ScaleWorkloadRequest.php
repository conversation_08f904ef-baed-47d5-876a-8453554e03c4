<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ScaleWorkloadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'replicas' => 'required|integer|min:0|max:100',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'replicas.required' => '副本数不能为空',
            'replicas.integer' => '副本数必须是整数',
            'replicas.min' => '副本数不能小于 0',
            'replicas.max' => '副本数不能大于 100',
        ];
    }
}
