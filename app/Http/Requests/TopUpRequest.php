<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TopUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $limits = config('payments.limits', []);
        $minAmount = $limits['min_amount'] ?? 0.01;
        $maxAmount = $limits['max_amount'] ?? 100000;

        return [
            'amount' => ['required', 'numeric', "min:{$minAmount}", "max:{$maxAmount}"],
            'payment_method' => ['sometimes', 'string'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        $limits = config('payments.limits', []);
        $minAmount = $limits['min_amount'] ?? 0.01;
        $maxAmount = $limits['max_amount'] ?? 100000;

        return [
            'amount.required' => '充值金额不能为空',
            'amount.numeric' => '充值金额必须是数字',
            'amount.min' => "充值金额必须大于等于 {$minAmount}",
            'amount.max' => "单次充值金额不能超过 {$maxAmount}",
            'payment_method.string' => '支付方式格式错误',
        ];
    }
}
