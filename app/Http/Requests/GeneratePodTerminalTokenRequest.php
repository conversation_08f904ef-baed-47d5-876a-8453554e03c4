<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GeneratePodTerminalTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('view', $this->user()->getWorkspace());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'pod_name' => 'required|string|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'container_name' => 'required|string|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'mode' => 'required|string|in:shell,attach',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'pod_name.regex' => 'Pod 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'container_name.regex' => '容器名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
        ];
    }
}
