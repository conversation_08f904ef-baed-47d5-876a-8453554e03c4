<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDeploymentRequest extends FormRequest
{
    use BaseWorkloadValidation;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(
            $this->getNameRulesForCreate('Deployment'),
            $this->getBaseRules(),
            [
                'strategy' => 'nullable|string|in:RollingUpdate,Recreate',
            ]
        );
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return array_merge(
            $this->getNameMessages('Deployment'),
            $this->getBaseMessages()
        );
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $this->validateResources($validator);
    }
}
