<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreHorizontalPodAutoscalerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'target_type' => 'required|string|in:Deployment,StatefulSet,ReplicaSet',
            'target_name' => 'required|string|max:63|regex:/^[a-z]([-a-z0-9]*[a-z0-9])?$/',
            'min_replicas' => 'required|integer|min:1|max:100',
            'max_replicas' => 'required|integer|min:1|max:1000|gte:min_replicas',
            'metrics' => 'required|array|min:1',
            'metrics.*.type' => 'required|string|in:Resource,Pods,Object,External',

            // Resource 类型度量指标
            'metrics.*.resource_name' => 'required_if:metrics.*.type,Resource|string|in:cpu,memory',
            'metrics.*.target_type' => 'required_if:metrics.*.type,Resource|string|in:Utilization,AverageValue',
            'metrics.*.target_value' => 'required_if:metrics.*.type,Resource|numeric',

            // Pods 类型度量指标
            'metrics.*.metric_name' => 'required_if:metrics.*.type,Pods,Object,External|string',
            'metrics.*.selector' => 'sometimes|array',

            // Object 类型度量指标
            'metrics.*.object_api_version' => 'required_if:metrics.*.type,Object|string',
            'metrics.*.object_kind' => 'required_if:metrics.*.type,Object|string',
            'metrics.*.object_name' => 'required_if:metrics.*.type,Object|string',

            // 行为配置（可选）
            'behavior' => 'sometimes|array',
            'behavior.scale_up' => 'sometimes|array',
            'behavior.scale_up.stabilization_window_seconds' => 'sometimes|integer|min:0|max:3600',
            'behavior.scale_up.select_policy' => 'sometimes|string|in:Max,Min,Disabled',
            'behavior.scale_up.policies' => 'sometimes|array',
            'behavior.scale_up.policies.*.type' => 'required_with:behavior.scale_up.policies|string|in:Percent,Pods',
            'behavior.scale_up.policies.*.value' => 'required_with:behavior.scale_up.policies|integer|min:1',
            'behavior.scale_up.policies.*.period_seconds' => 'required_with:behavior.scale_up.policies|integer|min:1|max:1800',

            'behavior.scale_down' => 'sometimes|array',
            'behavior.scale_down.stabilization_window_seconds' => 'sometimes|integer|min:0|max:3600',
            'behavior.scale_down.select_policy' => 'sometimes|string|in:Max,Min,Disabled',
            'behavior.scale_down.policies' => 'sometimes|array',
            'behavior.scale_down.policies.*.type' => 'required_with:behavior.scale_down.policies|string|in:Percent,Pods',
            'behavior.scale_down.policies.*.value' => 'required_with:behavior.scale_down.policies|integer|min:1',
            'behavior.scale_down.policies.*.period_seconds' => 'required_with:behavior.scale_down.policies|integer|min:1|max:1800',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'HPA 名称不能为空',
            'name.regex' => 'HPA 名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'target_type.required' => '目标工作负载类型不能为空',
            'target_type.in' => '目标工作负载类型必须是 Deployment、StatefulSet 或 ReplicaSet',
            'target_name.required' => '目标工作负载名称不能为空',
            'target_name.regex' => '目标工作负载名称必须以小写字母开头，以小写字母或数字结尾，中间可以包含连字符',
            'min_replicas.required' => '最小副本数不能为空',
            'min_replicas.min' => '最小副本数不能小于 1',
            'max_replicas.required' => '最大副本数不能为空',
            'max_replicas.gte' => '最大副本数不能小于最小副本数',
            'metrics.required' => '度量指标不能为空',
            'metrics.min' => '至少需要配置一个度量指标',
            'metrics.*.type.required' => '度量指标类型不能为空',
            'metrics.*.type.in' => '度量指标类型必须是 Resource、Pods、Object 或 External',
            'metrics.*.resource_name.required_if' => '资源类型度量指标必须指定资源名称',
            'metrics.*.resource_name.in' => '资源名称必须是 cpu 或 memory',
            'metrics.*.target_type.required_if' => '资源类型度量指标必须指定目标类型',
            'metrics.*.target_type.in' => '目标类型必须是 Utilization 或 AverageValue',
            'metrics.*.target_value.required_if' => '必须指定目标值',
            'metrics.*.metric_name.required_if' => '必须指定度量指标名称',
            'metrics.*.object_api_version.required_if' => 'Object 类型度量指标必须指定 API 版本',
            'metrics.*.object_kind.required_if' => 'Object 类型度量指标必须指定对象类型',
            'metrics.*.object_name.required_if' => 'Object 类型度量指标必须指定对象名称',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 验证资源类型度量指标的目标值格式
            $metrics = $this->input('metrics', []);

            foreach ($metrics as $index => $metric) {
                if ($metric['type'] === 'Resource') {
                    $targetType = $metric['target_type'] ?? '';
                    $targetValue = $metric['target_value'] ?? '';

                    if ($targetType === 'Utilization') {
                        // 百分比值，应该是 1-100 的整数
                        if (! is_numeric($targetValue) || (int) $targetValue < 1 || (int) $targetValue > 100) {
                            $validator->errors()->add(
                                "metrics.{$index}.target_value",
                                '使用率目标值必须是 1-100 之间的整数'
                            );
                        }
                    } elseif ($targetType === 'AverageValue') {
                        // 绝对值，前端只传数字，后端自动添加单位
                        if (! is_numeric($targetValue)) {
                            $validator->errors()->add(
                                "metrics.{$index}.target_value",
                                '平均值目标必须是数字'
                            );

                            continue;
                        }

                        $value = (int) $targetValue;
                        $resourceName = $metric['resource_name'] ?? '';

                        if ($resourceName === 'memory') {
                            // 内存必须是 512 的倍数且不小于 512
                            if ($value < 512 || $value % 512 !== 0) {
                                $validator->errors()->add(
                                    "metrics.{$index}.target_value",
                                    '内存必须是 512 的倍数且不小于 512'
                                );
                            }
                        } elseif ($resourceName === 'cpu') {
                            // CPU 必须是 500 的倍数且不小于 500
                            if ($value < 500 || $value % 500 !== 0) {
                                $validator->errors()->add(
                                    "metrics.{$index}.target_value",
                                    'CPU 必须是 500 的倍数且不小于 500'
                                );
                            }
                        }
                    }
                }
            }
        });
    }
}
