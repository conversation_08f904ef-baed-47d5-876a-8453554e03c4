<?php

namespace App\Http\Resources;

use App\DTOs\MetricsDTO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MetricsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var MetricsDTO $this->resource */
        $metrics = $this->resource;

        return [
            'timestamp' => $metrics->timestamp,
            'window' => $metrics->window,
            'containers' => $metrics->containers,
            'cpu_usage' => $metrics->getTotalCpuUsage(),
            'memory_usage' => $metrics->getTotalMemoryUsage(),
        ];
    }
}
