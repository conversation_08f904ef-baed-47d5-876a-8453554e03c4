<?php

namespace App\Http\Resources;

use App\DTOs\NamespaceEventsDTO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NamespaceEventsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var NamespaceEventsDTO $this->resource */
        $eventsDTO = $this->resource;

        $events = $eventsDTO->getAllEvents();
        $formattedEvents = [];

        foreach ($events as $event) {
            $formattedEvents[] = [
                'type' => $event->type,
                'reason' => $event->reason,
                'message' => $event->message,
                'timestamp' => $event->timestamp,
                'first_timestamp' => $event->firstTimestamp,
                'last_timestamp' => $event->lastTimestamp,
                'count' => $event->count,
                'source' => $event->source,
                'involved_object' => $event->involvedObject,
                'is_warning' => $event->isWarning(),
            ];
        }

        return $formattedEvents;
    }
}
