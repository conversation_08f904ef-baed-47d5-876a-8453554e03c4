<?php

namespace App\Http\Resources;

use App\DTOs\NamespaceMetricsDTO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NamespaceMetricsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var NamespaceMetricsDTO $this->resource */
        $metricsDTO = $this->resource;

        $podMetrics = $metricsDTO->getAllPodMetrics();
        $result = [];

        foreach ($podMetrics as $podName => $metrics) {
            $result[$podName] = [
                'timestamp' => $metrics->timestamp,
                'window' => $metrics->window,
                'containers' => $metrics->containers,
                'cpu_usage' => $metrics->getTotalCpuUsage(),
                'memory_usage' => $metrics->getTotalMemoryUsage(),
            ];
        }

        return $result;
    }
}
