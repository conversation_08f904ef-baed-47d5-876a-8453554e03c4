<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkspaceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'namespace' => $this->namespace,
            'status' => $this->status,
            'cluster_id' => $this->cluster_id,
            'cluster' => [
                'id' => $this->cluster->id,
                'name' => $this->cluster->name,
            ],
            'suspended_at' => $this->suspended_at?->toISOString(),
            'suspension_reason' => $this->suspension_reason,
            'overdue_amount' => $this->overdue_amount,
            'last_overdue_at' => $this->last_overdue_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }
}
