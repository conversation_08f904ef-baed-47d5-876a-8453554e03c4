<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'status' => $this->status,
            'data' => $this->data,
            'result' => $this->result,
            'message' => $this->message,
            'error_message' => $this->error_message,
            'attempts' => $this->attempts,
            'max_attempts' => $this->max_attempts,
            'started_at' => $this->started_at,
            'completed_at' => $this->completed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'resource' => $this->getResourceAttribute(),

            // 关系数据
            'workspace' => $this->whenLoaded('workspace'),
            'taskable' => $this->whenLoaded('taskable'),
        ];
    }
}
