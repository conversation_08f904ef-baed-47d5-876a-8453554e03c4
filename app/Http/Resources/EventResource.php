<?php

namespace App\Http\Resources;

use App\DTOs\EventDTO;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var EventDTO $this->resource */
        $event = $this->resource;

        return [
            'type' => $event->type,
            'reason' => $event->reason,
            'message' => $event->message,
            'timestamp' => $event->timestamp,
            'first_timestamp' => $event->firstTimestamp,
            'last_timestamp' => $event->lastTimestamp,
            'count' => $event->count,
            'source' => $event->source,
            'involved_object' => $event->involvedObject,
            'is_warning' => $event->isWarning(),
        ];
    }
}
