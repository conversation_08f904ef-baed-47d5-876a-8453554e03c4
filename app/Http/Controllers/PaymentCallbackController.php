<?php

namespace App\Http\Controllers;

use App\Models\TopUpRecord;
use App\Service\PaymentManagerService;
use App\Service\PaymentService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentCallbackController extends Controller
{
    protected PaymentManagerService $paymentManager;

    protected PaymentService $paymentService;

    public function __construct(
        PaymentManagerService $paymentManager,
        PaymentService $paymentService
    ) {
        $this->paymentManager = $paymentManager;
        $this->paymentService = $paymentService;
    }

    /**
     * 处理支付回调
     */
    public function handleCallback(Request $request, string $gatewayName): RedirectResponse
    {
        try {
            // 获取支付网关
            $gateway = $this->paymentManager->getGateway($gatewayName);
            if (! $gateway) {
                Log::error('未知的支付网关', ['gateway' => $gatewayName]);

                return redirect()->route('balance.index')->with('error', '未知的支付网关');
            }

            // 获取回调数据
            $callbackData = $request->all();

            Log::info('收到支付回调', [
                'gateway' => $gatewayName,
                'data' => $callbackData,
            ]);

            // 验证回调
            $verifyResult = $gateway->verifyCallback($callbackData);

            if (! $verifyResult['success']) {
                Log::error('支付回调验证失败', [
                    'gateway' => $gatewayName,
                    'data' => $callbackData,
                ]);

                return redirect()->route('balance.index')->with('error', '支付验证失败');
            }

            // 查找对应的充值记录
            $record = $this->findTopUpRecord($verifyResult);
            if (! $record) {
                Log::error('未找到对应的充值记录', [
                    'gateway' => $gatewayName,
                    'verify_result' => $verifyResult,
                ]);

                return redirect()->route('balance.index')->with('error', '未找到对应的充值记录');
            }

            // 检查记录是否已经处理过
            if ($record->status === TopUpRecord::STATUS_COMPLETED) {
                Log::info('充值记录已经处理过', ['record_id' => $record->id]);

                return redirect()->route('balance.index')->with('info', '充值记录已经处理过');
            }

            // 处理支付成功
            DB::transaction(function () use ($record, $verifyResult) {
                $this->paymentService->markPaymentSuccess($record, $verifyResult['gateway_response']);
            });

            Log::info('支付回调处理成功', [
                'record_id' => $record->id,
                'amount' => $record->amount,
                'user_id' => $record->user_id,
            ]);

            return redirect()->route('balance.index')->with('success', '充值成功！');

        } catch (\Exception $e) {
            Log::error('支付回调处理异常', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('balance.index')->with('error', '支付处理失败：'.$e->getMessage());
        }
    }

    /**
     * 查找对应的充值记录
     */
    private function findTopUpRecord(array $verifyResult): ?TopUpRecord
    {
        // 尝试通过订单ID查找
        if (isset($verifyResult['order_id'])) {
            $record = TopUpRecord::where('transaction_number', 'like', '%'.$verifyResult['order_id'].'%')
                ->where('status', TopUpRecord::STATUS_PENDING)
                ->first();

            if ($record) {
                return $record;
            }
        }

        // 尝试通过用户ID和金额查找最近的待支付记录
        if (isset($verifyResult['user_id']) && isset($verifyResult['amount'])) {
            return TopUpRecord::where('user_id', $verifyResult['user_id'])
                ->where('amount', $verifyResult['amount'])
                ->where('status', TopUpRecord::STATUS_PENDING)
                ->latest()
                ->first();
        }

        return null;
    }
}
