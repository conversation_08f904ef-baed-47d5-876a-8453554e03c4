<?php

namespace App\Http\Controllers;

use App\Service\SecretService;
use Inertia\Inertia;

class SecretController extends Controller
{
    /**
     * 显示 Secret 列表页面
     */
    public function index()
    {
        $workspace = auth()->user()->getWorkspace();

        $this->authorize('view', $workspace);

        return Inertia::render('Secrets/Index', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示创建 Secret 页面
     */
    public function create()
    {
        return Inertia::render('Secrets/Create');
    }

    /**
     * 显示 Secret 详情页面
     */
    public function show(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->getSecret($name);

            return Inertia::render('Secrets/Show', [
                'secret' => $secret->toArray(),
                'workspace' => $workspace,
            ]);
        } catch (\Exception $e) {
            return redirect()->route('secrets.index')
                ->with('error', '获取 Secret 详情失败：'.$e->getMessage());
        }
    }

    /**
     * 显示编辑 Secret 页面
     */
    public function edit(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->getSecret($name);

            return Inertia::render('Secrets/Edit', [
                'secret' => $secret->toArray(),
            ]);
        } catch (\Exception $e) {
            return redirect()->route('secrets.index')
                ->with('error', '获取 Secret 详情失败：'.$e->getMessage());
        }
    }
}
