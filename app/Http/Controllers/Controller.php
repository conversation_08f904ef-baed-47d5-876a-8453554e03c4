<?php

namespace App\Http\Controllers;

use App\DTOs\KubernetesResourceDTO;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Collection;

abstract class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    public function notFound($message = 'Not found'): JsonResponse
    {
        return $this->error($message, 404);
    }

    public function error($message = '', $code = 400): JsonResponse
    {
        return $this->apiResponse(['message' => $message], $code);
    }

    public function apiResponse($data, $status = 200): JsonResponse
    {
        if (is_string($data)) {
            $data = ['message' => $data];
        }

        return response()->json($data, $status);
    }

    public function forbidden($message = 'Forbidden'): JsonResponse
    {
        return $this->error($message, 403);
    }

    public function unauthorized($message = 'Unauthorized'): JsonResponse
    {
        return $this->error($message, 401);
    }

    public function badRequest($message = 'Bad request'): JsonResponse
    {
        return $this->error($message);
    }

    public function created($message = 'Created'): JsonResponse
    {
        return $this->apiResponse($message, 201);
    }

    public function success($data = [], $status = 200): JsonResponse
    {
        return $this->apiResponse($data, $status);
    }

    public function failed($message = 'Failed'): JsonResponse
    {
        return $this->error($message);
    }

    public function accepted($message = 'Accepted'): JsonResponse
    {
        return $this->apiResponse([
            'message' => $message,
        ], 202);
    }

    public function noContent(): JsonResponse
    {
        return $this->apiResponse(null, 204);
    }

    public function updated(mixed $message = 'Updated'): JsonResponse
    {
        if ($message instanceof Model) {
            $message = $message->getChanges();
        }

        return $this->success($message);
    }

    public function deleted(): JsonResponse
    {
        return $this->noContent();
    }

    public function notAllowed($message = 'Not allowed'): JsonResponse
    {
        return $this->error($message, 405);
    }

    public function conflict($message = 'Conflict'): JsonResponse
    {
        return $this->error($message, 409);
    }

    public function tooManyRequests($message = 'Too many requests'): JsonResponse
    {
        return $this->error($message, 429);
    }

    public function serverError($message = 'Server error'): JsonResponse
    {
        return $this->error($message, 500);
    }

    public function serviceUnavailable($message = 'Service unavailable'): JsonResponse
    {
        return $this->error($message, 503);
    }

    public function methodNotAllowed($message = 'Method not allowed'): JsonResponse
    {
        return $this->error($message, 405);
    }

    public function notAcceptable($message = 'Not acceptable'): JsonResponse
    {
        return $this->error($message, 406);
    }

    public function preconditionFailed($message = 'Precondition failed'): JsonResponse
    {
        return $this->error($message, 412);
    }

    /**
     * 分页
     * 分页会自动调用 Model 或者 Collection 的 paginate 方法自动分页，并返回包含数据和分页信息的响应
     *
     * @param  mixed  $data
     * @param  int|null  $perPage
     */
    public function paginate($data, $perPage = null): JsonResponse
    {
        // 如果 perPage 为空，则读取 request 的 per_page 参数，如果没有，则默认为 10
        if ($perPage === null) {
            $perPage = request()->input('per_page', 10);
        }

        // parPage 最大 50
        $perPage = min(max($perPage, 1), 50); // 限制在 1-50 之间

        try {
            // 尝试调用分页方法
            $paginatedData = $data->paginate($perPage);
        } catch (\BadMethodCallException $e) {
            // 如果方法不存在，返回错误
            return $this->serverError('Unable to paginate resource: paginate method not available.');
        } catch (\Throwable $e) {
            // 处理其他可能的异常
            return $this->serverError('Unable to paginate resource: '.$e->getMessage());
        }

        // 构建分页信息
        $pagination = [
            'total' => $paginatedData->total(),
            'count' => $paginatedData->count(),
            'per_page' => $paginatedData->perPage(),
            'current_page' => $paginatedData->currentPage(),
            'total_pages' => $paginatedData->lastPage(),
        ];

        // 返回包含数据和分页信息的响应
        return $this->success([
            'data' => $paginatedData->getCollection(),
            'pagination' => $pagination,
        ]);
    }

    /**
     * DTO 转换
     */
    public function dto(KubernetesResourceDTO|array $data, $status = 200): JsonResponse
    {
        if ($data instanceof KubernetesResourceDTO) {
            return $this->success($data->toArray(), $status);
        }

        return $this->success(array_map(fn ($item) => $item->toArray(), $data), $status);
    }

    /**
     * 使用 Resource 返回成功响应
     */
    public function resource($resource, $status = 200): JsonResponse
    {
        return response()->json($resource, $status);
    }

    /**
     * 使用 Resource 返回创建成功响应
     */
    public function resourceCreated($resource): JsonResponse
    {
        return $this->resource($resource, 201);
    }

    /**
     * 获取当前认证用户
     */
    protected function user()
    {
        return auth()->user();
    }

    /**
     * 使用 Resource 进行分页响应
     */
    public function paginateResource($query, $resourceClass, $perPage = null): JsonResponse
    {
        // 如果 perPage 为空，则读取 request 的 per_page 参数，如果没有，则默认为 10
        if ($perPage === null) {
            $perPage = request()->input('per_page', 10);
        }

        // parPage 最大 50
        $perPage = min(max($perPage, 1), 50); // 限制在 1-50 之间

        try {
            // 尝试调用分页方法
            $paginatedData = $query->paginate($perPage);
        } catch (\BadMethodCallException $e) {
            // 如果方法不存在，返回错误
            return $this->serverError('Unable to paginate resource: paginate method not available.');
        } catch (\Throwable $e) {
            // 处理其他可能的异常
            return $this->serverError('Unable to paginate resource: '.$e->getMessage());
        }

        // 构建分页信息
        $pagination = [
            'total' => $paginatedData->total(),
            'count' => $paginatedData->count(),
            'per_page' => $paginatedData->perPage(),
            'current_page' => $paginatedData->currentPage(),
            'total_pages' => $paginatedData->lastPage(),
        ];

        // 将数据通过 Resource 类转换
        $transformedData = $paginatedData->getCollection()->map(function ($item) use ($resourceClass) {
            return new $resourceClass($item);
        });

        // 返回包含数据和分页信息的响应
        return $this->resource([
            'data' => $transformedData,
            'pagination' => $pagination,
        ]);
    }
}
