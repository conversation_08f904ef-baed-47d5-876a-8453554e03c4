<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class DeploymentController extends Controller
{
    /**
     * 显示 Deployment 列表页面
     */
    public function index()
    {
        return Inertia::render('Deployments/Index');
    }

    /**
     * 显示创建 Deployment 页面
     */
    public function create()
    {
        return Inertia::render('Deployments/Create');
    }

    /**
     * 显示 Deployment 详情页面
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        return Inertia::render('Deployments/Show', [
            'deploymentName' => $name,
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示编辑 Deployment 页面
     */
    public function edit(string $name)
    {
        return Inertia::render('Deployments/Edit', [
            'deploymentName' => $name,
        ]);
    }
}
