<?php

namespace App\Http\Controllers;

use App\Service\ConfigMapService;
use Inertia\Inertia;

class ConfigMapController extends Controller
{
    /**
     * 显示 ConfigMap 列表页面
     */
    public function index()
    {
        return Inertia::render('ConfigMaps/Index');
    }

    /**
     * 显示创建 ConfigMap 页面
     */
    public function create()
    {
        return Inertia::render('ConfigMaps/Create');
    }

    /**
     * 显示 ConfigMap 详情页面
     */
    public function show(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 如果 name 为 kube-root-ca.crt 则返回 404
        if ($name === 'kube-root-ca.crt') {
            return redirect()->route('configmaps.index');
        }

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->getConfigMap($name);

            return Inertia::render('ConfigMaps/Show', [
                'configMap' => $configMap->toArray(),
            ]);
        } catch (\Exception $e) {
            return redirect()->route('configmaps.index')
                ->with('error', '获取 ConfigMap 详情失败：'.$e->getMessage());
        }
    }

    /**
     * 显示编辑 ConfigMap 页面
     */
    public function edit(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('update', $workspace);

        // 如果 name 为 kube-root-ca.crt 则返回 404
        if ($name === 'kube-root-ca.crt') {
            return redirect()->route('configmaps.index');
        }

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->getConfigMap($name);

            return Inertia::render('ConfigMaps/Edit', [
                'configMap' => $configMap->toArray(),
            ]);
        } catch (\Exception $e) {
            return redirect()->route('configmaps.index')
                ->with('error', '获取 ConfigMap 详情失败：'.$e->getMessage());
        }
    }
}
