<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class ServiceController extends Controller
{
    /**
     * Service 列表页面
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('Services/Index', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * Service 详情页面
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('Services/Show', [
            'serviceName' => $name,
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示创建 Service 表单
     */
    public function create(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        return Inertia::render('Services/Create', [
            'workspace' => $workspace->load('cluster'),
        ]);
    }

    /**
     * 显示编辑 Service 表单
     */
    public function edit(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        return Inertia::render('Services/Edit', [
            'serviceName' => $name,
            'workspace' => $workspace->load('cluster'),
        ]);
    }
}
