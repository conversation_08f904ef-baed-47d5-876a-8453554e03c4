<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class HorizontalPodAutoscalerController extends Controller
{
    /**
     * 显示 HPA 列表页面
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('HorizontalPodAutoscalers/Index', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示创建 HPA 页面
     */
    public function create(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        return Inertia::render('HorizontalPodAutoscalers/Create', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示 HPA 详情页面
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('HorizontalPodAutoscalers/Show', [
            'hpaName' => $name,
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示编辑 HPA 页面
     */
    public function edit(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        return Inertia::render('HorizontalPodAutoscalers/Edit', [
            'hpaName' => $name,
            'workspace' => $workspace,
        ]);
    }
}
