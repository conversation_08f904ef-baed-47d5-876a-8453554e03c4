<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Service\MetricsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MetricsController extends Controller
{
    /**
     * 获取命名空间下的所有Pod指标
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $metricsService = new MetricsService($workspace);

        try {
            $metrics = $metricsService->getNamespaceMetrics();

            return response()->json($metrics->toArray());
        } catch (\Exception $e) {
            Log::error('获取命名空间指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取命名空间指标失败');
        }
    }

    /**
     * 获取特定Pod的指标
     */
    public function podMetrics(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $metricsService = new MetricsService($workspace);

        try {
            $metrics = $metricsService->getPodMetrics($name);

            if (! $metrics) {
                return response()->json([]);
            }

            return response()->json($metrics->toArray());
        } catch (\Exception $e) {
            Log::error('获取Pod指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取Pod指标失败');
        }
    }
}
