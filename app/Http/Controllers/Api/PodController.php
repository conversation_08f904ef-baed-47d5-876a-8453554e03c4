<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Service\PodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PodController extends Controller
{
    /**
     * 获取 Pod 列表
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $pods = $podService->getPods();

            return $this->dto($pods);
        } catch (\Exception $e) {
            Log::error('获取 Pod 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Pod 列表失败');
        }
    }

    /**
     * 获取单个 Pod
     */
    public function show(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $pod = $podService->getPod($name);

            return $this->dto($pod);
        } catch (\Exception $e) {
            Log::error('获取 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error($e->getMessage(), 404);
        }
    }

    /**
     * 获取 Pod 日志
     */
    public function logs(Request $request, string $name): JsonResponse
    {
        $validated = $request->validate([
            'container' => 'sometimes|string',
            'tail_lines' => 'sometimes|integer|min:1|max:1000',
            'timestamps' => 'sometimes|boolean',
            'previous' => 'sometimes|boolean',
        ]);

        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $containerName = $validated['container'] ?? null;
            if (! $containerName) {
                $pod = $podService->getPod($name);
                if (! empty($pod->containers)) {
                    $containerName = $pod->containers[0]['name'];
                }
            }

            if (! $containerName) {
                return $this->success(['logs' => '']);
            }

            $options = [
                'tail_lines' => $validated['tail_lines'] ?? 100,
                'timestamps' => $validated['timestamps'] ?? false,
                'previous' => $validated['previous'] ?? false,
            ];

            $logs = $podService->getPodLogs(
                $name,
                $containerName,
                $options
            );

            return $this->success(['logs' => $logs]);
        } catch (\Exception $e) {
            Log::error('获取 Pod 日志失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Pod 日志失败');
        }
    }

    /**
     * 删除 Pod
     */
    public function destroy(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $podService->deletePod($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 Pod 失败');
        }
    }

    /**
     * 重启 Pod (通过删除重新创建)
     */
    public function restart(Request $request, string $name): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $podService->restartPod($name);

            return $this->success(['message' => "Pod '{$name}' 重启成功"]);
        } catch (\Exception $e) {
            Log::error('重启 Pod 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('重启 Pod 失败');
        }
    }

    /**
     * 执行 Pod 命令
     */
    public function exec(Request $request, string $name): JsonResponse
    {
        $validated = $request->validate([
            'container' => 'sometimes|string',
            'command' => 'required|array',
            'command.*' => 'required|string',
        ]);

        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $result = $podService->execPodCommand($name, $validated['command'], $validated['container'] ?? null);

            return $this->success($result);
        } catch (\Exception $e) {
            Log::error('执行 Pod 命令失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('执行 Pod 命令失败');
        }
    }
}
