<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Service\EventService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EventController extends Controller
{
    /**
     * 获取命名空间下的所有事件
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $eventService = new EventService($workspace);

        try {
            $events = $eventService->getNamespaceEvents();

            return response()->json($events->toArray());
        } catch (\Exception $e) {
            Log::error('获取命名空间事件失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取命名空间事件失败');
        }
    }

    /**
     * 获取特定资源的事件
     */
    public function resourceEvents(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'kind' => 'required|string',
            'name' => 'required|string',
        ]);

        $workspace = $request->user()->getWorkspace();
        $eventService = new EventService($workspace);

        try {
            $events = $eventService->getResourceEvents($validated['kind'], $validated['name']);

            return response()->json($events);
        } catch (\Exception $e) {
            Log::error('获取资源事件失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取资源事件失败');
        }
    }
}
