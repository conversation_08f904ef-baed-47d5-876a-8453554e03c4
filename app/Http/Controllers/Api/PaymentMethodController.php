<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Service\PaymentManagerService;
use Illuminate\Http\JsonResponse;

class PaymentMethodController extends Controller
{
    protected PaymentManagerService $paymentManager;

    public function __construct(PaymentManagerService $paymentManager)
    {
        $this->paymentManager = $paymentManager;
    }

    /**
     * 获取可用的支付方式列表
     */
    public function index(): JsonResponse
    {
        $paymentMethods = $this->paymentManager->getGatewayList();

        return $this->success($paymentMethods);
    }

    /**
     * 获取指定支付方式的详细信息
     */
    public function show(string $identifier): JsonResponse
    {
        $gateway = $this->paymentManager->getGateway($identifier);

        if (! $gateway) {
            return $this->notFound('支付方式不存在');
        }

        $config = config("payments.gateways.{$identifier}", []);

        return $this->success([
            'identifier' => $identifier,
            'name' => $config['name'] ?? $gateway->getName(),
            'description' => $config['description'] ?? $gateway->getDescription(),
            'enabled' => $config['enabled'] ?? false,
        ]);
    }
}
