<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreHorizontalPodAutoscalerRequest;
use App\Http\Requests\UpdateHorizontalPodAutoscalerRequest;
use App\Service\HorizontalPodAutoscalerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HorizontalPodAutoscalerController extends Controller
{
    /**
     * 获取 HPA 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $hpas = $hpaService->getHPAs();

            return $this->dto($hpas);
        } catch (\Exception $e) {
            Log::error('获取 HPA 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 HPA 列表失败');
        }
    }

    /**
     * 获取单个 HPA
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $hpa = $hpaService->getHPA($name);

            return $this->dto($hpa);
        } catch (\Exception $e) {
            Log::error('获取 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 HPA 失败');
        }
    }

    /**
     * 创建 HPA
     */
    public function store(StoreHorizontalPodAutoscalerRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);

            // 验证目标工作负载是否存在
            if (! $hpaService->validateTargetWorkload(
                $request->input('target_type'),
                $request->input('target_name')
            )) {
                return $this->error('目标工作负载不存在');
            }

            $hpa = $hpaService->createHPA($request->validated());

            return $this->dto($hpa, 201);
        } catch (\Exception $e) {
            Log::error('创建 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 HPA 失败');
        }
    }

    /**
     * 更新 HPA
     */
    public function update(UpdateHorizontalPodAutoscalerRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            // 如果更新了目标工作负载，验证是否存在
            if (isset($data['target_type']) && isset($data['target_name'])) {
                if (! $hpaService->validateTargetWorkload($data['target_type'], $data['target_name'])) {
                    return $this->error('目标工作负载不存在');
                }
            }

            $hpa = $hpaService->updateHPA($name, $data);

            return $this->dto($hpa);
        } catch (\Exception $e) {
            Log::error('更新 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新 HPA 失败');
        }
    }

    /**
     * 删除 HPA
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $hpaService->deleteHPA($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 HPA 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 HPA 失败');
        }
    }

    /**
     * 获取可扩缩的工作负载列表
     */
    public function scalableWorkloads(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $hpaService = new HorizontalPodAutoscalerService($workspace);
            $workloads = $hpaService->getScalableWorkloads();

            return $this->success($workloads);
        } catch (\Exception $e) {
            Log::error('获取可扩缩工作负载失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取可扩缩工作负载失败');
        }
    }
}
