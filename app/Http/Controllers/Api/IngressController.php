<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckDomainsRequest;
use App\Http\Requests\StoreIngressRequest;
use App\Http\Requests\UpdateIngressRequest;
use App\Service\IngressService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class IngressController extends Controller
{
    /**
     * Display a listing of the ingresses.
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $ingressService = new IngressService($workspace);
            $ingresses = $ingressService->getIngresses();

            return $this->dto($ingresses);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error($e->getMessage());
        }
    }

    /**
     * Store a newly created ingress.
     */
    public function store(StoreIngressRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $ingress = $ingressService->createIngress($request->validated());

            return $this->success($ingress->toArray(), 201);
        } catch (\Exception $e) {
            Log::error('创建 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Ingress 失败');
        }
    }

    /**
     * Display the specified ingress.
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $ingressService = new IngressService($workspace);
            $ingress = $ingressService->getIngress($name);

            return $this->success($ingress->toArray());
        } catch (\Exception $e) {
            Log::error('获取 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Ingress 失败');
        }
    }

    /**
     * Update the specified ingress.
     */
    public function update(UpdateIngressRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $ingress = $ingressService->updateIngress($name, $request->validated());

            return $this->success($ingress->toArray());
        } catch (\Exception $e) {
            Log::error('更新 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新 Ingress 失败');
        }
    }

    /**
     * Remove the specified ingress.
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $ingressService = new IngressService($workspace);
            $ingressService->deleteIngress($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Ingress 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 Ingress 失败');
        }
    }

    /**
     * Get available ingress classes.
     */
    public function ingressClasses(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $ingressService = new IngressService($workspace);
            $classes = $ingressService->getAvailableIngressClasses();

            return $this->success($classes);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 类失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Ingress 类失败');
        }
    }

    /**
     * Check domain availability.
     */
    public function checkDomains(CheckDomainsRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $ingressService = new IngressService($workspace);
            $conflicts = $ingressService->checkDomainAvailability($request->input('domains'));

            return $this->success([
                'available' => empty($conflicts),
                'conflicts' => $conflicts,
            ]);
        } catch (\Exception $e) {
            Log::error('检查域名可用性失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('检查域名可用性失败');
        }
    }
}
