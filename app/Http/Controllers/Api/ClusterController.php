<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Cluster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClusterController extends Controller
{
    /**
     * API: 获取可用的集群列表
     */
    public function index()
    {
        return Cluster::select('id', 'name')->get();
    }

    /**
     * API: 获取集群节点信息（包含外部 IP）
     */
    public function getNodes(Request $request, Cluster $cluster)
    {
        try {
            $response = $cluster->http()->get('/api/v1/nodes');

            if (! $response->successful()) {
                return response()->json(['message' => '获取节点信息失败'], 500);
            }

            $nodes = collect($response->json('items', []))->map(function ($node) {
                $addresses = collect($node['status']['addresses'] ?? []);

                return [
                    'name' => $node['metadata']['name'],
                    'internal_ip' => $addresses->where('type', 'InternalIP')->first()['address'] ?? null,
                    'external_ip' => $node['metadata']['labels']['external-ip'] ?? null,
                    'hostname' => $addresses->where('type', 'Hostname')->first()['address'] ?? null,
                ];
            })->filter(function ($node) {
                return $node['external_ip'] !== null;
            });

            return $this->success($nodes->values());
        } catch (\Exception $e) {
            Log::error('获取节点信息失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取节点信息失败');
        }
    }

    /**
     * API: 获取集群 Ingress 外部 IP 地址
     */
    public function getIngressExternalIps(Request $request, Cluster $cluster)
    {
        try {
            // 从集群设置中获取 Ingress 外部 IP
            $ingressExternalIps = $cluster->getSetting('ingress_external_ips', []);

            if (empty($ingressExternalIps)) {
                return $this->error('未配置 Ingress 外部 IP');
            }

            return $this->success($ingressExternalIps);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 外部 IP 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Ingress 外部 IP 失败');
        }
    }
}
