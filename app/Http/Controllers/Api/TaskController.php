<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TaskResource;
use App\Models\Task;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TaskController extends Controller
{
    /**
     * 获取工作区任务列表
     */
    public function index(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();

        $this->authorize('view', $workspace);

        $query = $workspace->tasks()->latest();

        // 按状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 按任务类型筛选
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        return $this->paginate($query);
    }

    /**
     * 显示特定任务详情
     */
    public function show(Request $request, Task $task): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 确保任务属于该工作区
        if ($task->workspace_id !== $workspace->id) {
            return $this->error('任务不属于该工作区', 404);
        }

        return $this->resource(new TaskResource($task));
    }

    /**
     * 重试失败的任务
     */
    public function retry(Request $request, Task $task): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        // 确保任务属于该工作区
        if ($task->workspace_id !== $workspace->id) {
            return $this->error('任务不属于该工作区', 404);
        }

        if (! $task->canRetry()) {
            return $this->error('任务无法重试', 422);
        }

        $task->reset();

        // 重新分派队列任务
        \App\Jobs\ProcessTaskJob::dispatch($task);

        // 刷新任务状态
        $task->refresh();

        return $this->resource(new TaskResource($task));
    }

    /**
     * 获取任务统计信息
     */
    public function stats(Request $request): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        $stats = [
            'total' => $workspace->tasks()->count(),
            'pending' => $workspace->pendingTasks()->count(),
            'running' => $workspace->runningTasks()->count(),
            'completed' => $workspace->completedTasks()->count(),
            'failed' => $workspace->failedTasks()->count(),
            'cancelled' => $workspace->tasks()->where('status', 'cancelled')->count(),
        ];

        return $this->resource($stats);
    }

    /**
     * 取消待处理的任务
     */
    public function cancel(Request $request, Task $task): JsonResponse
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        // 确保任务属于该工作区
        if ($task->workspace_id !== $workspace->id) {
            return $this->error('任务不属于该工作区', 404);
        }

        // 只能取消待处理的任务
        if ($task->status !== Task::STATUS_PENDING) {
            return $this->error('只能取消待处理的任务', 422);
        }

        $task->markAsFailed('任务已被用户取消');

        return $this->resource(new TaskResource($task));
    }
}
