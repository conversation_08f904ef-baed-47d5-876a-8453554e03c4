<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreWorkspaceRequest;
use App\Http\Requests\UpdateWorkspaceRequest;
use App\Http\Resources\WorkspaceResource;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WorkspaceController extends Controller
{
    public function __construct(
        protected WorkspaceService $workspaceService
    ) {}

    /**
     * API: 获取用户的工作空间列表
     */
    public function index(Request $request)
    {
        $workspaces = $request->user()->workspaces()->with('cluster')->get();

        $currentWorkspace = $request->user()->getWorkspace();

        return [
            'data' => WorkspaceResource::collection($workspaces),
            'currentWorkspace' => $currentWorkspace,
        ];
    }

    /**
     * API: 创建工作空间
     */
    public function store(StoreWorkspaceRequest $request)
    {
        try {
            $workspace = $this->workspaceService->createWorkspace([
                'user_id' => auth()->id(),
                'cluster_id' => $request->cluster_id,
                'name' => $request->name,
            ]);

            return new WorkspaceResource($workspace->load('cluster'));
        } catch (\Exception $e) {
            Log::error('创建工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建工作空间失败');
        }
    }

    /**
     * API: 获取工作空间详情
     */
    public function show(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        return new WorkspaceResource($workspace->load('cluster'));
    }

    /**
     * API: 更新工作空间
     */
    public function update(UpdateWorkspaceRequest $request, Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            $workspace->update($request->validated());

            return new WorkspaceResource($workspace->load('cluster'));
        } catch (\Exception $e) {
            Log::error('更新工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新工作空间失败');
        }
    }

    /**
     * API: 删除工作空间
     */
    public function destroy(Workspace $workspace)
    {
        $this->authorize('delete', $workspace);

        try {
            // 如果是当前工作空间，清除用户的当前工作空间
            if (auth()->user()->current_workspace_id === $workspace->id) {
                auth()->user()->update(['current_workspace_id' => null]);
            }

            // 启动删除任务，不立即删除记录
            $this->workspaceService->startWorkspaceDeletion($workspace);

            return $this->success('工作空间删除已启动，正在清理 Kubernetes 资源');
        } catch (\Exception $e) {
            Log::error('启动工作空间删除失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('启动工作空间删除失败');
        }
    }

    /**
     * API: 设置当前工作空间
     */
    public function setCurrent(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        try {
            auth()->user()->setCurrentWorkspace($workspace);

            return $this->success('已切换到工作空间：'.$workspace->name);
        } catch (\Exception $e) {
            Log::error('切换工作空间失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('切换工作空间失败');
        }
    }

    /**
     * API: 重试创建 namespace
     */
    public function retryNamespace(Workspace $workspace)
    {
        $this->authorize('update', $workspace);

        try {
            if ($workspace->status !== Workspace::STATUS_FAILED) {
                return $this->error('只能重试创建失败的工作空间');
            }

            $this->workspaceService->retryCreateNamespace($workspace);

            return $this->success('已重新开始创建 Namespace，请稍后刷新查看状态');
        } catch (\Exception $e) {
            Log::error('重试创建失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('重试创建失败');
        }
    }

    /**
     * API: 获取当前工作空间中的应用资源（Deployment/StatefulSet）和其端口信息
     */
    public function getCurrentWorkloadResources(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return $this->getWorkloadResources($workspace);
    }

    /**
     * 获取工作空间中的应用资源（Deployment/StatefulSet）和其端口信息
     */
    public function getWorkloadResources(Workspace $workspace)
    {
        $this->authorize('view', $workspace);

        try {
            $resources = [];

            // 获取 Deployment 列表
            $deploymentService = new \App\Service\DeploymentService($workspace);
            $deployments = $deploymentService->getDeployments();

            foreach ($deployments as $deployment) {
                $workloadPorts = [];

                // 提取所有容器的端口
                foreach ($deployment->containers as $container) {
                    foreach ($container['ports'] as $port) {
                        $workloadPorts[] = [
                            'container_name' => $container['name'],
                            'port_name' => $port['name'],
                            'port' => $port['container_port'],
                            'protocol' => $port['protocol'],
                        ];
                    }
                }

                if (! empty($workloadPorts)) {
                    $resources[] = [
                        'type' => 'Deployment',
                        'name' => $deployment->name,
                        'ports' => $workloadPorts,
                    ];
                }
            }

            // 获取 StatefulSet 列表
            $statefulSetService = new \App\Service\StatefulSetService($workspace);
            $statefulSets = $statefulSetService->getStatefulSets();

            foreach ($statefulSets as $statefulSet) {
                $workloadPorts = [];

                // 提取所有容器的端口
                foreach ($statefulSet->containers as $container) {
                    foreach ($container['ports'] as $port) {
                        $workloadPorts[] = [
                            'container_name' => $container['name'],
                            'port_name' => $port['name'],
                            'port' => $port['container_port'],
                            'protocol' => $port['protocol'],
                        ];
                    }
                }

                if (! empty($workloadPorts)) {
                    $resources[] = [
                        'type' => 'StatefulSet',
                        'name' => $statefulSet->name,
                        'ports' => $workloadPorts,
                    ];
                }
            }

            return $this->success($resources);

        } catch (\Exception $e) {
            Log::error('获取工作负载资源失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作负载资源失败');
        }
    }

    /**
     * API: 获取当前工作空间的所有事件
     */
    public function getWorkspaceEvents(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $eventService = new \App\Service\EventService($workspace);
            $events = $eventService->getNamespaceEvents();

            return response()->json($events->toArray());
        } catch (\Exception $e) {
            Log::error('获取工作空间事件失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作空间事件失败');
        }
    }

    /**
     * API: 获取当前工作空间的所有Pod指标
     */
    public function getWorkspaceMetrics(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $metricsService = new \App\Service\MetricsService($workspace);
            $metrics = $metricsService->getNamespaceMetrics();

            return response()->json($metrics->toArray());
        } catch (\Exception $e) {
            Log::error('获取工作空间指标失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取工作空间指标失败');
        }
    }
}
