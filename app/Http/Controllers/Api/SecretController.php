<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreBasicAuthSecretRequest;
use App\Http\Requests\StoreDockerRegistrySecretRequest;
use App\Http\Requests\StoreGenericSecretRequest;
use App\Http\Requests\StoreSshAuthSecretRequest;
use App\Http\Requests\StoreTlsSecretRequest;
use App\Service\SecretService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SecretController extends Controller
{
    /**
     * 获取 Secret 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secrets = $secretService->getSecrets();

            return $this->dto($secrets);
        } catch (\Exception $e) {
            Log::error('获取 Secret 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Secret 列表失败');
        }
    }

    /**
     * 获取单个 Secret
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->getSecret($name);

            return $this->dto($secret);
        } catch (\Exception $e) {
            Log::error('获取 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Secret 失败');
        }
    }

    /**
     * 创建 Generic Secret
     */
    public function storeGeneric(StoreGenericSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createGenericSecret(
                $request->input('name'),
                $request->input('data')
            );

            return $this->dto($secret, 201);
        } catch (\Exception $e) {
            Log::error('创建 Generic Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Generic Secret 失败');
        }
    }

    /**
     * 创建 Docker Registry Secret
     */
    public function storeDockerRegistry(StoreDockerRegistrySecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createDockerRegistrySecret(
                $request->input('name'),
                $request->input('server'),
                $request->input('username'),
                $request->input('password'),
                $request->input('email')
            );

            return $this->dto($secret, 201);
        } catch (\Exception $e) {
            Log::error('创建 Docker Registry Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Docker Registry Secret 失败');
        }
    }

    /**
     * 创建 TLS Secret
     */
    public function storeTls(StoreTlsSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createTlsSecret(
                $request->input('name'),
                $request->input('cert'),
                $request->input('key')
            );

            return $this->dto($secret, 201);
        } catch (\Exception $e) {
            Log::error('创建 TLS Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 TLS Secret 失败');
        }
    }

    /**
     * 创建 Basic Auth Secret
     */
    public function storeBasicAuth(StoreBasicAuthSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createBasicAuthSecret(
                $request->input('name'),
                $request->input('username'),
                $request->input('password')
            );

            return $this->dto($secret, 201);
        } catch (\Exception $e) {
            Log::error('创建 Basic Auth Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Basic Auth Secret 失败');
        }
    }

    /**
     * 创建 SSH Auth Secret
     */
    public function storeSshAuth(StoreSshAuthSecretRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->createSshAuthSecret(
                $request->input('name'),
                $request->input('ssh_private_key')
            );

            return $this->dto($secret, 201);
        } catch (\Exception $e) {
            Log::error('创建 SSH Auth Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 SSH Auth Secret 失败');
        }
    }

    /**
     * 删除 Secret
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $secretService->deleteSecret($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 Secret 失败');
        }
    }

    /**
     * 获取 Secret 数据
     */
    public function data(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $secretService = new SecretService($workspace);
            $data = $secretService->getSecretData($name);

            // 如果请求包含 edit=true 参数，返回实际数据用于编辑
            if ($request->query('edit') === 'true') {
                return $this->success($data);
            }

            // 默认只返回键名，不返回实际值（安全考虑）
            return $this->success([
                'keys' => array_keys($data),
                'count' => count($data),
            ]);
        } catch (\Exception $e) {
            Log::error('获取 Secret 数据失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Secret 数据失败');
        }
    }

    /**
     * 更新 Secret（通用方法，支持所有类型）
     */
    public function update(Request $request, string $name)
    {
        $request->validate([
            'data' => 'required|array',
            'data.*' => 'required|string',
        ]);

        $workspace = $request->user()->getWorkspace();

        try {
            $secretService = new SecretService($workspace);
            $secret = $secretService->updateSecretData($name, $request->input('data'));

            return $this->dto($secret);
        } catch (\Exception $e) {
            Log::error('更新 Secret 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新 Secret 失败');
        }
    }

    /**
     * 获取支持的 Secret 类型
     */
    public function types(Request $request)
    {
        return $this->success([
            'types' => [
                'generic',
                'docker-registry',
                'tls',
                'basic-auth',
                'ssh-auth',
            ],
        ]);
    }
}
