<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ExpandStorageRequest;
use App\Http\Requests\StoreStorageRequest;
use App\Service\StorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StorageController extends Controller
{
    /**
     * 获取工作空间的所有 Storage
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $storageService = new StorageService($workspace);
            $storages = $storageService->getStorages();

            return $this->dto($storages);
        } catch (\Exception $e) {
            Log::error('获取 Storage 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Storage 列表失败');
        }
    }

    /**
     * 获取单个 Storage 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $storageService = new StorageService($workspace);
            $storage = $storageService->getStorage($name);

            return $this->dto($storage);
        } catch (\Exception $e) {
            Log::error('获取 Storage 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Storage 详情失败');
        }
    }

    /**
     * 创建 Storage
     */
    public function store(StoreStorageRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $validated = $request->validated();

        try {
            $storageService = new StorageService($workspace);
            $storage = $storageService->createStorage($validated['name'], $validated['size']);

            return $this->dto($storage, 201);
        } catch (\Exception $e) {
            Log::error('创建 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Storage 失败');
        }
    }

    /**
     * 扩容 Storage
     */
    public function expand(ExpandStorageRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $validated = $request->validated();

        try {
            $storageService = new StorageService($workspace);
            $storage = $storageService->expandStorage($name, $validated['size']);

            return $this->dto($storage);
        } catch (\Exception $e) {
            Log::error('扩容 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('扩容 Storage 失败');
        }
    }

    /**
     * 删除 Storage
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $storageService = new StorageService($workspace);
            $storageService->deleteStorage($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Storage 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 Storage 失败');
        }
    }
}
