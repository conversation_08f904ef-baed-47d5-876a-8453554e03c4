<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ScaleWorkloadRequest;
use App\Http\Requests\StoreDeploymentRequest;
use App\Http\Requests\UpdateDeploymentRequest;
use App\Service\DeploymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeploymentController extends Controller
{
    /**
     * 获取 Deployment 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployments = $deploymentService->getDeployments();

            return $this->dto($deployments);
        } catch (\Exception $e) {
            Log::error('获取 Deployment 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 Deployment 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployment = $deploymentService->getDeployment($name);

            return $this->dto($deployment);
        } catch (\Exception $e) {
            Log::error('获取 Deployment 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->notFound();
        }
    }

    /**
     * 创建 Deployment
     */
    public function store(StoreDeploymentRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployment = $deploymentService->createDeployment($request->validated());

            return $this->dto($deployment, 201);
        } catch (\Exception $e) {
            Log::error('创建 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 Deployment
     */
    public function update(UpdateDeploymentRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            $deployment = $deploymentService->updateDeployment($name, $data);

            return $this->dto($deployment);
        } catch (\Exception $e) {
            Log::error('更新 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 Deployment
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deploymentService->deleteDeployment($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 扩容/缩容 Deployment
     */
    public function scale(ScaleWorkloadRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $deploymentService = new DeploymentService($workspace);
            $deployment = $deploymentService->scaleDeployment($name, $request->input('replicas'));

            return $this->dto($deployment);
        } catch (\Exception $e) {
            Log::error('扩容/缩容 Deployment 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
