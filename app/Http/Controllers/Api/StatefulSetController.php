<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ScaleWorkloadRequest;
use App\Http\Requests\StoreStatefulSetRequest;
use App\Http\Requests\UpdateStatefulSetRequest;
use App\Service\StatefulSetService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StatefulSetController extends Controller
{
    /**
     * 获取 StatefulSet 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSets = $statefulSetService->getStatefulSets();

            return $this->dto($statefulSets);
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 获取单个 StatefulSet 详情
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSet = $statefulSetService->getStatefulSet($name);

            return $this->dto($statefulSet);
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->notFound();
        }
    }

    /**
     * 创建 StatefulSet
     */
    public function store(StoreStatefulSetRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSet = $statefulSetService->createStatefulSet($request->validated());

            return $this->dto($statefulSet, 201);
        } catch (\Exception $e) {
            Log::error('创建 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 更新 StatefulSet
     */
    public function update(UpdateStatefulSetRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $data = $request->validated();
            $data['name'] = $name; // 确保名称正确

            $statefulSet = $statefulSetService->updateStatefulSet($name, $data);

            return $this->dto($statefulSet);
        } catch (\Exception $e) {
            Log::error('更新 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 删除 StatefulSet
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSetService->deleteStatefulSet($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }

    /**
     * 扩容/缩容 StatefulSet
     */
    public function scale(ScaleWorkloadRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $statefulSetService = new StatefulSetService($workspace);
            $statefulSet = $statefulSetService->scaleStatefulSet($name, $request->input('replicas'));

            return $this->dto($statefulSet);
        } catch (\Exception $e) {
            Log::error('扩容/缩容 StatefulSet 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError();
        }
    }
}
