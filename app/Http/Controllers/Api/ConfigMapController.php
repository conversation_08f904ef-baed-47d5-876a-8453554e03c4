<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreConfigMapFromFilesRequest;
use App\Http\Requests\StoreConfigMapRequest;
use App\Http\Requests\UpdateConfigMapRequest;
use App\Service\ConfigMapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ConfigMapController extends Controller
{
    /**
     * 获取 ConfigMap 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMaps = $configMapService->getConfigMaps();

            return $this->dto($configMaps);
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 ConfigMap 列表失败');
        }
    }

    /**
     * 获取单个 ConfigMap
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        $this->authorize('view', $workspace);

        // 如果 name 为 kube-root-ca.crt 则返回 404
        if ($name === 'kube-root-ca.crt') {
            return $this->notFound('ConfigMap not found');
        }

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->getConfigMap($name);

            return $this->dto($configMap);
        } catch (\Exception $e) {
            Log::error('获取 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 ConfigMap 失败');
        }
    }

    /**
     * 创建 ConfigMap
     */
    public function store(StoreConfigMapRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->createConfigMap(
                $request->input('name'),
                $request->input('data', []),
                $request->input('binary_data', [])
            );

            return $this->success($configMap->toArray(), 201);
        } catch (\Exception $e) {
            Log::error('创建 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 ConfigMap 失败');
        }
    }

    /**
     * 更新 ConfigMap
     */
    public function update(UpdateConfigMapRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->updateConfigMap(
                $name,
                $request->input('data', []),
                $request->input('binary_data', [])
            );

            return $this->success($configMap->toArray());
        } catch (\Exception $e) {
            Log::error('更新 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新 ConfigMap 失败');
        }
    }

    /**
     * 删除 ConfigMap
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMapService->deleteConfigMap($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 ConfigMap 失败');
        }
    }

    /**
     * 从文件创建 ConfigMap
     */
    public function storeFromFiles(StoreConfigMapFromFilesRequest $request)
    {
        $workspace = $request->user()->getWorkspace();

        try {
            $configMapService = new ConfigMapService($workspace);
            $configMap = $configMapService->createConfigMapFromFiles(
                $request->input('name'),
                $request->input('files')
            );

            return $this->success($configMap->toArray(), 201);
        } catch (\Exception $e) {
            Log::error('从文件创建 ConfigMap 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('从文件创建 ConfigMap 失败');
        }
    }
}
