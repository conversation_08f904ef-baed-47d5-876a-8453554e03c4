<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreServiceRequest;
use App\Http\Requests\UpdateServiceRequest;
use App\Service\IpPoolService;
use App\Service\ServiceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ServiceController extends Controller
{
    /**
     * 获取 Service 列表
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $services = $serviceService->getServices();

            return $this->dto($services);
        } catch (\Exception $e) {
            Log::error('获取 Service 列表失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Service 列表失败');
        }
    }

    /**
     * 获取单个 Service
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $service = $serviceService->getService($name);

            return $this->dto($service);
        } catch (\Exception $e) {
            Log::error('获取 Service 详情失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('获取 Service 详情失败');
        }
    }

    /**
     * 创建 Service
     */
    public function store(StoreServiceRequest $request)
    {
        $workspace = $request->user()->getWorkspace();
        $data = $request->validated();

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $service = $serviceService->createService($data);

            return $this->dto($service, 201);
        } catch (\Exception $e) {
            Log::error('创建 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('创建 Service 失败');
        }
    }

    /**
     * 更新 Service
     */
    public function update(UpdateServiceRequest $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $data = $request->validated();

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $service = $serviceService->updateService($name, $data);

            return $this->dto($service);
        } catch (\Exception $e) {
            Log::error('更新 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('更新 Service 失败');
        }
    }

    /**
     * 删除 Service
     */
    public function destroy(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        try {
            $serviceService = new ServiceService($workspace, app(IpPoolService::class));
            $serviceService->deleteService($name);

            return $this->deleted();
        } catch (\Exception $e) {
            Log::error('删除 Service 失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->error('删除 Service 失败');
        }
    }
}
