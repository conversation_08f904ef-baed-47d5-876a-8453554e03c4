<?php

namespace App\Http\Controllers;

use App\Service\PodService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PodController extends Controller
{
    /**
     * Pod 列表页面
     */
    public function index(Request $request): Response
    {
        return Inertia::render('Pods/Index', [
            'workspace' => $request->user()->getWorkspace(),
        ]);
    }

    /**
     * Pod 详情页面
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            // Check if pod exists to return 404 from web route if not found
            $podService->getPod($name);
        } catch (\Exception $e) {
            return redirect()->route('pods.index')
                ->with('error', "Pod '{$name}' not found.");
        }

        return Inertia::render('Pods/Show', [
            'podName' => $name,
            'workspace' => $workspace,
        ]);
    }

    /**
     * Pod 日志页面
     */
    public function logs(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            $pod = $podService->getPod($name);

            $containerName = $request->query('container');
            if (! $containerName && ! empty($pod->containers)) {
                $containerName = $pod->containers[0]['name'];
            }

            return Inertia::render('Pods/Logs', [
                'podName' => $name,
                'currentContainer' => $containerName,
                'workspace' => $workspace,
            ]);
        } catch (\Exception $e) {
            return redirect()->route('pods.show', $name)
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Pod 终端页面
     */
    public function terminal(Request $request, string $name): Response|RedirectResponse
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        try {
            // Check if pod exists to return 404 from web route if not found
            $podService->getPod($name);
        } catch (\Exception) {
            // In a real app, you'd probably have a proper 404 page
            // For now, redirecting to index.
            return redirect()->route('pods.index')
                ->with('error', "Pod '{$name}' not found.");
        }

        return Inertia::render('Pods/Terminal', [
            'podName' => $name,
            'workspace' => $workspace,
            'containerName' => $request->query('container'),
        ]);
    }
}
