<?php

namespace App\Http\Controllers;

use App\Service\IngressService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class IngressController extends Controller
{
    /**
     * Display a listing of the ingresses.
     */
    public function index(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 获取集群 Ingress 外部 IP 配置
        $cluster = $workspace->cluster;
        $ingressExternalIps = $cluster ? $cluster->getSetting('ingress_external_ips', []) : [];

        return Inertia::render('Ingresses/Index', [
            'workspace' => $workspace,
            'cluster' => $cluster ? [
                'id' => $cluster->id,
                'name' => $cluster->name,
                'ingress_external_ips' => $ingressExternalIps,
            ] : null,
        ]);
    }

    /**
     * Show the form for creating a new ingress.
     */
    public function create(Request $request)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        $ingressService = new IngressService($workspace);
        $availableClasses = $ingressService->getAvailableIngressClasses();

        return Inertia::render('Ingresses/Create', [
            'ingressClasses' => $availableClasses,
        ]);
    }

    /**
     * Show the form for editing the specified ingress.
     */
    public function edit(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('update', $workspace);

        $ingressService = new IngressService($workspace);
        $availableClasses = $ingressService->getAvailableIngressClasses();

        try {
            $ingress = $ingressService->getIngress($name);
        } catch (\Exception $e) {
            return redirect()->route('ingresses.index')->with('error', '获取 Ingress 失败：'.$e->getMessage());
        }

        return Inertia::render('Ingresses/Edit', [
            'ingress' => $ingress->toArray(),
            'ingressClasses' => $availableClasses,
            'workspace' => $workspace,
        ]);
    }

    /**
     * Display the specified ingress.
     */
    public function show(Request $request, string $name)
    {
        $workspace = $request->user()->getWorkspace();
        $this->authorize('view', $workspace);

        // 获取集群 Ingress 外部 IP 配置
        $cluster = $workspace->cluster;
        $ingressExternalIps = $cluster ? $cluster->getSetting('ingress_external_ips', []) : [];

        return Inertia::render('Ingresses/Show', [
            'ingressName' => $name,
            'workspace' => $workspace,
            'cluster' => $cluster ? [
                'id' => $cluster->id,
                'name' => $cluster->name,
                'ingress_external_ips' => $ingressExternalIps,
            ] : null,
        ]);
    }
}
