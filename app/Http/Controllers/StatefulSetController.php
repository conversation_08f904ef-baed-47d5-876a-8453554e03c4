<?php

namespace App\Http\Controllers;

use Inertia\Inertia;

class StatefulSetController extends Controller
{
    /**
     * 显示 StatefulSet 列表页面
     */
    public function index()
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('StatefulSets/Index', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示创建 StatefulSet 页面
     */
    public function create()
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('StatefulSets/Create', [
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示 StatefulSet 详情页面
     */
    public function show(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('StatefulSets/Show', [
            'statefulSetName' => $name,
            'workspace' => $workspace,
        ]);
    }

    /**
     * 显示编辑 StatefulSet 页面
     */
    public function edit(string $name)
    {
        $workspace = auth()->user()->getWorkspace();
        $this->authorize('view', $workspace);

        return Inertia::render('StatefulSets/Edit', [
            'statefulSetName' => $name,
            'workspace' => $workspace,
        ]);
    }
}
