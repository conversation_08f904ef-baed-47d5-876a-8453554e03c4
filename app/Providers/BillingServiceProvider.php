<?php

namespace App\Providers;

use App\Service\BalanceService;
use App\Service\Billing\BillingPipeline;
use App\Service\Billing\BillingRecordService;
use App\Service\Billing\CostCalculationService;
use App\Service\Billing\LoadBalancerBillingService;
use App\Service\Billing\OverdueManagementService;
use App\Service\Billing\PodResourceBillingService;
use App\Service\Billing\StorageBillingService;
use App\Service\BillingService;
use App\Service\ResourceCollectionService;
use Illuminate\Support\ServiceProvider;

class BillingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册专门的计费服务
        $this->app->singleton(PodResourceBillingService::class);
        $this->app->singleton(StorageBillingService::class);
        $this->app->singleton(LoadBalancerBillingService::class);
        $this->app->singleton(CostCalculationService::class);

        // 注册计费记录服务
        $this->app->singleton(BillingRecordService::class, function ($app) {
            return new BillingRecordService(
                $app->make(BalanceService::class)
            );
        });

        // 注册欠费管理服务
        $this->app->singleton(OverdueManagementService::class, function ($app) {
            return new OverdueManagementService(
                $app->make(BalanceService::class)
            );
        });

        // 注册计费流水线
        $this->app->singleton(BillingPipeline::class, function ($app) {
            return new BillingPipeline(
                $app->make(ResourceCollectionService::class),
                $app->make(CostCalculationService::class),
                $app->make(BillingRecordService::class),
                $app->make(OverdueManagementService::class)
            );
        });

        // 注册主要的计费服务
        $this->app->singleton(BillingService::class, function ($app) {
            return new BillingService(
                $app->make(BillingPipeline::class),
                $app->make(OverdueManagementService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
