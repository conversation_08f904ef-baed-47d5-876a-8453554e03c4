<?php

namespace App\Console\Commands\System;

use App\Service\CertificateCacheService;
use Illuminate\Console\Command;

class CertificateCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificate:cache {action} {--max-age=604800}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理 Kubernetes 证书缓存文件';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'cleanup':
                return $this->cleanupExpired();
            case 'clear':
                return $this->clearAll();
            case 'stats':
                return $this->showStats();
            default:
                $this->error("Unknown action: {$action}");
                $this->line('Available actions: cleanup, clear, stats');

                return 1;
        }
    }

    /**
     * 清理过期的证书缓存
     */
    protected function cleanupExpired(): int
    {
        $maxAge = (int) $this->option('max-age');

        $this->info("清理超过 {$maxAge} 秒的证书缓存文件...");

        $cleaned = CertificateCacheService::cleanupExpiredCertificates($maxAge);

        $this->info("清理完成，共删除 {$cleaned} 个文件。");

        return 0;
    }

    /**
     * 清理所有证书缓存
     */
    protected function clearAll(): int
    {
        if (! $this->confirm('确定要清理所有证书缓存文件吗？')) {
            $this->info('操作已取消。');

            return 0;
        }

        $this->info('清理所有证书缓存文件...');

        $cleaned = CertificateCacheService::clearAllCertificates();

        $this->info("清理完成，共删除 {$cleaned} 个文件。");

        return 0;
    }

    /**
     * 显示证书缓存统计信息
     */
    protected function showStats(): int
    {
        $stats = CertificateCacheService::getCacheStats();

        $this->info('证书缓存统计信息：');
        $this->table(
            ['类型', '数量'],
            [
                ['总文件数', $stats['total_files']],
                ['总大小', $this->formatBytes($stats['total_size'])],
                ['CA 证书', $stats['ca_files']],
                ['客户端证书', $stats['cert_files']],
                ['私钥文件', $stats['key_files']],
            ]
        );

        return 0;
    }

    /**
     * 格式化字节数为可读格式
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2).' '.$units[$i];
    }
}
