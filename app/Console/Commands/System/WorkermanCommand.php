<?php

namespace App\Console\Commands\System;

use App\Service\JwtService;
use App\Service\PodTerminalService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\Request;
use Workerman\Protocols\Http\Response;
use Workerman\Worker;

class WorkermanCommand extends Command
{
    protected $signature = 'workerman {action} {--host=} {--port=} {--daemon}';

    protected $description = 'Start WebSocket server for Pod terminal connections';

    protected JwtService $jwtService;

    protected PodTerminalService $terminalService;

    public function __construct()
    {
        parent::__construct();
        $this->jwtService = new JwtService;
        $this->terminalService = new PodTerminalService($this->jwtService);
    }

    public function handle(): int
    {
        global $argv;

        $action = $this->argument('action');
        $host = $this->option('host') ?: config('websocket.server.host');
        $port = $this->option('port') ?: config('websocket.server.port');
        $daemon = $this->option('daemon');

        // 设置 Workerman 的命令行参数
        $argv[0] = 'websocket-server';
        $argv[1] = $action;
        if ($daemon) {
            $argv[2] = '-d';
        }

        if ($action === 'start') {
            $this->info("Starting WebSocket server on {$host}:{$port}");
        }

        $this->startWebSocketServer($host, $port);

        return 0;
    }

    protected function startWebSocketServer(string $host, int $port): void
    {
        // 创建 WebSocket 服务器
        $websocketWorker = new Worker("websocket://{$host}:{$port}");
        $websocketWorker->name = 'PodTerminalWebSocket';
        $websocketWorker->count = config('websocket.server.worker_count', 4);

        // 设置 WebSocket 协议
        $websocketWorker->protocol = 'Workerman\\Protocols\\Websocket';

        // 连接建立时的回调
        $websocketWorker->onConnect = function (TcpConnection $connection) {
            Log::info("New WebSocket connection: {$connection->id}");
        };

        // 收到消息时的回调
        $websocketWorker->onMessage = function (TcpConnection $connection, $data) {
            $this->handleWebSocketMessage($connection, $data);
        };

        // 连接关闭时的回调
        $websocketWorker->onClose = function (TcpConnection $connection) {
            Log::info("WebSocket connection closed: {$connection->id}");
            $this->terminalService->cleanup($connection);
        };

        // 创建 HTTP 服务器用于健康检查和 token 生成
        $httpWorker = new Worker("http://{$host}:".($port + 1));
        $httpWorker->name = 'PodTerminalHTTP';
        $httpWorker->count = 1;

        $httpWorker->onMessage = function (TcpConnection $connection, Request $request) {
            $this->handleHttpRequest($connection, $request);
        };

        // 启动进程时的回调
        $websocketWorker->onWorkerStart = function () {
            Log::info('WebSocket worker started');
        };

        $httpWorker->onWorkerStart = function () {
            Log::info('HTTP worker started');
        };

        // 运行所有 Worker
        Worker::$pidFile = storage_path('logs/websocket.pid');
        Worker::$logFile = storage_path('logs/websocket.log');
        Worker::runAll();
    }

    protected function handleWebSocketMessage(TcpConnection $connection, string $data): void
    {
        try {
            // Log::debug('WebSocket message received', [
            //     'connection_id' => $connection->id,
            //     'data' => $data,
            //     'authenticated' => $connection->authenticated ?? false,
            // ]);

            $message = json_decode($data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Invalid JSON received from WebSocket', [
                    'connection_id' => $connection->id,
                    'data' => $data,
                    'error' => json_last_error_msg(),
                ]);
                $this->sendError($connection, 'Invalid JSON format');

                return;
            }

            $type = $message['type'] ?? '';
            // Log::debug('Handling WebSocket message', [
            //     'connection_id' => $connection->id,
            //     'type' => $type,
            // ]);

            switch ($type) {
                case 'auth':
                    $this->handleAuth($connection, $message);
                    break;

                case 'input':
                    $this->handleInput($connection, $message);
                    break;

                case 'resize':
                    $this->handleResize($connection, $message);
                    break;

                case 'ping':
                    // Client heartbeat, can be ignored or responded to with pong
                    $this->sendMessage($connection, 'pong');
                    break;

                default:
                    Log::warning('Unknown WebSocket message type', [
                        'connection_id' => $connection->id,
                        'type' => $type,
                        'message' => $message,
                    ]);
                    $this->sendError($connection, 'Unknown message type');
            }

        } catch (\Exception $e) {
            Log::error('WebSocket message handling error', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
            $this->sendError($connection, 'Message processing failed');
        }
    }

    protected function handleAuth(TcpConnection $connection, array $message): void
    {
        $token = $message['token'] ?? '';
        if (empty($token)) {
            $this->sendError($connection, 'Token is required');

            return;
        }

        try {
            // 验证 JWT token
            $tokenData = $this->jwtService->verifyToken($token);

            // 建立 Pod 连接
            if ($this->terminalService->connectToPod($connection, $tokenData)) {
                $connection->authenticated = true;
                $connection->tokenData = $tokenData;

                $this->sendMessage($connection, 'auth_success', [
                    'message' => 'Authentication successful',
                ]);
            }

        } catch (\Exception $e) {
            $this->sendError($connection, 'Authentication failed: '.$e->getMessage());
        }
    }

    protected function handleInput(TcpConnection $connection, array $message): void
    {
        if (! ($connection->authenticated ?? false)) {
            $this->sendError($connection, '认证失败');

            return;
        }

        $input = $message['data'] ?? '';
        $this->terminalService->handleInput($connection, $input);
    }

    protected function handleResize(TcpConnection $connection, array $message): void
    {
        if (! ($connection->authenticated ?? false)) {
            $this->sendError($connection, 'Resize 失败：没有认证');

            return;
        }

        $cols = $message['cols'] ?? 80;
        $rows = $message['rows'] ?? 24;

        // 这里应该实现终端大小调整逻辑
        Log::debug('Terminal resize', [
            'connection_id' => $connection->id,
            'cols' => $cols,
            'rows' => $rows,
        ]);
    }

    protected function handleHttpRequest(TcpConnection $connection, Request $request): void
    {
        $path = $request->path();
        $method = $request->method();

        if ($path === '/health' && $method === 'GET') {
            $response = new Response(200, [], json_encode([
                'status' => 'healthy',
                'timestamp' => time(),
            ]));
            $connection->send($response);

            return;
        }

        if ($path === '/token' && $method === 'POST') {
            $this->handleTokenGeneration($connection, $request);

            return;
        }

        // 404 响应
        $response = new Response(404, [], json_encode([
            'error' => 'Not found',
        ]));
        $connection->send($response);
    }

    protected function handleTokenGeneration(TcpConnection $connection, Request $request): void
    {
        try {
            $data = json_decode($request->rawBody(), true);

            $userId = $data['user_id'] ?? null;
            $workspaceId = $data['workspace_id'] ?? null;
            $podName = $data['pod_name'] ?? null;
            $containerName = $data['container_name'] ?? null;
            $mode = $data['mode'] ?? 'shell';

            if (! $userId || ! $workspaceId || ! $podName) {
                $response = new Response(400, [], json_encode([
                    'error' => 'Missing required parameters',
                ]));
                $connection->send($response);

                return;
            }

            $token = $this->jwtService->generatePodTerminalToken(
                $userId,
                $workspaceId,
                $podName,
                $containerName,
                $mode
            );

            $response = new Response(200, [], json_encode([
                'token' => $token,
                'expires_in' => config('websocket.jwt.ttl'),
            ]));
            $connection->send($response);

        } catch (\Exception $e) {
            $response = new Response(500, [], json_encode([
                'error' => 'Token generation failed: '.$e->getMessage(),
            ]));
            $connection->send($response);
        }
    }

    protected function sendMessage(TcpConnection $connection, string $type, array $data = []): void
    {
        $message = json_encode([
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true),
        ]);

        $connection->send($message);
    }

    protected function sendError(TcpConnection $connection, string $message): void
    {
        $this->sendMessage($connection, 'error', ['message' => $message]);
    }
}
