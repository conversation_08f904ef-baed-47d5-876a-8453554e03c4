<?php

namespace App\Console\Commands\System;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateJwtKeysCommand extends Command
{
    protected $signature = 'jwt:generate {--force : Force overwrite existing keys}';

    protected $description = 'Generate JWT private and public keys for WebSocket authentication';

    public function handle(): int
    {
        $privatePath = config('websocket.jwt.private_key_path');
        $publicPath = config('websocket.jwt.public_key_path');

        // 确保目录存在
        $directory = dirname($privatePath);
        if (! File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // 检查文件是否已存在
        if (! $this->option('force') && (File::exists($privatePath) || File::exists($publicPath))) {
            if (! $this->confirm('JWT keys already exist. Do you want to overwrite them?')) {
                $this->info('Key generation cancelled.');

                return 0;
            }
        }

        try {
            // 生成私钥
            $privateKey = openssl_pkey_new([
                'digest_alg' => 'sha256',
                'private_key_bits' => 2048,
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ]);

            if (! $privateKey) {
                $this->error('Failed to generate private key: '.openssl_error_string());

                return 1;
            }

            // 导出私钥
            openssl_pkey_export($privateKey, $privateKeyPem);

            // 获取公钥
            $publicKeyDetails = openssl_pkey_get_details($privateKey);
            $publicKeyPem = $publicKeyDetails['key'];

            // 保存密钥文件
            File::put($privatePath, $privateKeyPem);
            File::put($publicPath, $publicKeyPem);

            // 设置适当的权限
            chmod($privatePath, 0600);
            chmod($publicPath, 0644);

            $this->info('JWT keys generated successfully!');
            $this->line("Private key: {$privatePath}");
            $this->line("Public key: {$publicPath}");

            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to generate JWT keys: '.$e->getMessage());

            return 1;
        }
    }
}
