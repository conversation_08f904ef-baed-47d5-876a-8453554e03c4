<?php

namespace App\Console\Commands\Billing;

use App\Events\WorkspacePendingDeletion;
use App\Models\Workspace;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendOverdueWarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:send-overdue-warnings 
                          {--dry-run : 仅预览，不实际发送通知}
                          {--force : 强制发送，跳过频率限制}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送欠费工作空间删除预警通知';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTime = now();
        $this->info("开始发送欠费预警通知任务：{$startTime->format('Y-m-d H:i:s')}");

        // 检查通知功能是否启用
        if (! config('billing.cleanup.send_notification', true) && ! $this->option('force')) {
            $this->warn('欠费预警通知功能已禁用');

            return 1;
        }

        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        if ($isDryRun) {
            $this->warn("\n--- 预览模式 ---");
            $this->info('这是预览模式，不会实际发送通知');
        }

        try {
            // 获取配置
            $deletionDays = config('billing.overdue.deletion_days', 7);
            $firstWarningDays = config('billing.overdue.notification.first_warning_days', 3);
            $finalWarningDays = config('billing.overdue.notification.final_warning_days', 6);

            $this->info('通知配置：');
            $this->info("- 删除天数：{$deletionDays} 天");
            $this->info("- 首次警告：欠费 {$firstWarningDays} 天后");
            $this->info("- 最终警告：欠费 {$finalWarningDays} 天后");

            $results = [
                'first_warnings' => 0,
                'final_warnings' => 0,
                'total_sent' => 0,
                'errors' => [],
            ];

            // 发送首次警告
            $firstWarningResults = $this->sendFirstWarnings($firstWarningDays, $deletionDays, $isDryRun);
            $results['first_warnings'] = $firstWarningResults['sent'];
            $results['errors'] = array_merge($results['errors'], $firstWarningResults['errors']);

            // 发送最终警告
            $finalWarningResults = $this->sendFinalWarnings($finalWarningDays, $deletionDays, $isDryRun);
            $results['final_warnings'] = $finalWarningResults['sent'];
            $results['errors'] = array_merge($results['errors'], $finalWarningResults['errors']);

            $results['total_sent'] = $results['first_warnings'] + $results['final_warnings'];

            // 显示结果
            $this->displayResults($results, $isDryRun);

            $endTime = now();
            $duration = $endTime->diffInSeconds($startTime);

            $this->info("\n预警通知任务完成");
            $this->info("开始时间：{$startTime->format('Y-m-d H:i:s')}");
            $this->info("结束时间：{$endTime->format('Y-m-d H:i:s')}");
            $this->info("耗时：{$duration} 秒");

            return 0;
        } catch (\Exception $e) {
            $this->error("预警通知任务执行失败：{$e->getMessage()}");
            Log::error('发送欠费预警通知失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * 发送首次警告
     */
    protected function sendFirstWarnings(int $warningDays, int $deletionDays, bool $isDryRun): array
    {
        $this->info("\n=== 首次删除警告 ===");

        // 查找需要首次警告的工作空间（暂停 warningDays 天的）
        $warningDate = Carbon::now()->subDays($warningDays);
        $startOfDay = $warningDate->copy()->startOfDay();
        $endOfDay = $warningDate->copy()->endOfDay();

        $workspaces = Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->whereBetween('suspended_at', [$startOfDay, $endOfDay])
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user'])
            ->get();

        $sent = 0;
        $errors = [];

        if ($workspaces->isEmpty()) {
            $this->info('没有需要发送首次警告的工作空间');

            return ['sent' => $sent, 'errors' => $errors];
        }

        $this->info("发现 {$workspaces->count()} 个工作空间需要发送首次警告：");

        foreach ($workspaces as $workspace) {
            try {
                $daysUntilDeletion = $deletionDays - $warningDays;

                if ($isDryRun) {
                    $this->info("  [预览] 将发送首次警告：{$workspace->name} (用户: {$workspace->user->name}) - 还有 {$daysUntilDeletion} 天被删除");
                } else {
                    $this->info("  发送首次警告：{$workspace->name} (用户: {$workspace->user->name})");

                    // 触发警告事件
                    WorkspacePendingDeletion::dispatch(
                        $workspace->user,
                        $workspace,
                        $daysUntilDeletion,
                        "工作空间已欠费 {$warningDays} 天，如不及时支付将在 {$daysUntilDeletion} 天后被删除"
                    );

                    Log::info('发送工作空间删除首次警告', [
                        'workspace_id' => $workspace->id,
                        'workspace_name' => $workspace->name,
                        'user_id' => $workspace->user_id,
                        'user_name' => $workspace->user->name,
                        'overdue_amount' => $workspace->overdue_amount,
                        'suspended_days' => $warningDays,
                        'days_until_deletion' => $daysUntilDeletion,
                        'warning_type' => 'first',
                    ]);
                }

                $sent++;
            } catch (\Exception $e) {
                $error = "首次警告发送失败 - 工作空间 {$workspace->name}：{$e->getMessage()}";
                $errors[] = $error;
                $this->error("  ✗ {$error}");

                Log::error('发送首次警告失败', [
                    'workspace_id' => $workspace->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return ['sent' => $sent, 'errors' => $errors];
    }

    /**
     * 发送最终警告
     */
    protected function sendFinalWarnings(int $warningDays, int $deletionDays, bool $isDryRun): array
    {
        $this->info("\n=== 最终删除警告 ===");

        // 查找需要最终警告的工作空间（暂停 warningDays 天的）
        $warningDate = Carbon::now()->subDays($warningDays);
        $startOfDay = $warningDate->copy()->startOfDay();
        $endOfDay = $warningDate->copy()->endOfDay();

        $workspaces = Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->whereBetween('suspended_at', [$startOfDay, $endOfDay])
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user'])
            ->get();

        $sent = 0;
        $errors = [];

        if ($workspaces->isEmpty()) {
            $this->info('没有需要发送最终警告的工作空间');

            return ['sent' => $sent, 'errors' => $errors];
        }

        $this->info("发现 {$workspaces->count()} 个工作空间需要发送最终警告：");

        foreach ($workspaces as $workspace) {
            try {
                $daysUntilDeletion = $deletionDays - $warningDays;

                if ($isDryRun) {
                    $this->info("  [预览] 将发送最终警告：{$workspace->name} (用户: {$workspace->user->name}) - 还有 {$daysUntilDeletion} 天被删除");
                } else {
                    $this->warn("  发送最终警告：{$workspace->name} (用户: {$workspace->user->name})");

                    // 触发警告事件
                    WorkspacePendingDeletion::dispatch(
                        $workspace->user,
                        $workspace,
                        $daysUntilDeletion,
                        "⚠️ 最终警告：工作空间已欠费 {$warningDays} 天，将在 {$daysUntilDeletion} 天后被永久删除，请立即支付欠费"
                    );

                    Log::warning('发送工作空间删除最终警告', [
                        'workspace_id' => $workspace->id,
                        'workspace_name' => $workspace->name,
                        'user_id' => $workspace->user_id,
                        'user_name' => $workspace->user->name,
                        'overdue_amount' => $workspace->overdue_amount,
                        'suspended_days' => $warningDays,
                        'days_until_deletion' => $daysUntilDeletion,
                        'warning_type' => 'final',
                    ]);
                }

                $sent++;
            } catch (\Exception $e) {
                $error = "最终警告发送失败 - 工作空间 {$workspace->name}：{$e->getMessage()}";
                $errors[] = $error;
                $this->error("  ✗ {$error}");

                Log::error('发送最终警告失败', [
                    'workspace_id' => $workspace->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return ['sent' => $sent, 'errors' => $errors];
    }

    /**
     * 显示执行结果
     */
    protected function displayResults(array $results, bool $isDryRun): void
    {
        $this->info("\n=== ".($isDryRun ? '预览' : '通知').'结果统计 ===');

        $this->table(
            ['项目', '数量'],
            [
                ['首次警告', $results['first_warnings']],
                ['最终警告', $results['final_warnings']],
                ['总发送数', $results['total_sent']],
                ['发送失败', count($results['errors'])],
            ]
        );

        // 显示错误信息
        if (! empty($results['errors'])) {
            $this->error("\n错误详情：");
            foreach ($results['errors'] as $error) {
                $this->error("  - {$error}");
            }
        }

        if (! $isDryRun && $results['total_sent'] > 0) {
            $this->info("\n💡 提示：通知事件已触发，实际发送取决于事件监听器的配置");
        }
    }
}
