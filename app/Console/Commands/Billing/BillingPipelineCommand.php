<?php

namespace App\Console\Commands\Billing;

use App\Service\BillingService;
use Illuminate\Console\Command;

class BillingPipelineCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:pipeline 
                            {--dry-run : 只显示流水线说明，不执行实际操作}
                            {--force : 强制执行，不询问确认}
                            {--cluster= : 只处理指定集群}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '执行完整的计费流水线：资源收集、成本计算、计费记录、余额扣费、欠费恢复、欠费处理';

    /**
     * 构造函数
     */
    public function __construct(
        protected BillingService $billingService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('dry-run')) {
            $this->showPipelineDescription();

            return 0;
        }

        $this->showPipelineDescription();

        // 如果不是强制执行，则询问确认
        if (! $this->option('force') && ! $this->confirm('是否要执行完整的计费流水线?', false)) {
            $this->info('操作取消');

            return 0;
        }

        $this->info('开始执行完整计费流水线...');
        $this->newLine();

        $startTime = microtime(true);

        try {
            if ($cluster = $this->option('cluster')) {
                $result = $this->processSingleCluster($cluster);
            } else {
                $result = $this->processAllClusters();
            }

            $totalTime = microtime(true) - $startTime;
            $this->displayResults($result, $totalTime);

            return 0;

        } catch (\Exception $e) {
            $this->error('计费流水线执行失败: '.$e->getMessage());
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());

            return 1;
        }
    }

    /**
     * 显示流水线描述
     */
    private function showPipelineDescription(): void
    {
        $this->info('完整流水线执行顺序:');
        $this->info('1. 资源收集阶段 (Resource Collection Phase)');
        $this->info('   - 从K8s API收集各工作空间的资源使用情况');
        $this->info('   - Pod资源使用 (CPU, Memory)');
        $this->info('   - 存储使用 (PVC)');
        $this->info('   - 负载均衡器使用 (LoadBalancer类型Service)');

        $this->info("\n2. 成本计算阶段 (Cost Calculation Phase)");
        $this->info('   - 根据资源使用量和定价配置计算费用');
        $this->info('   - 应用折扣和优惠');
        $this->info('   - 生成成本明细');

        $this->info("\n3. 计费记录阶段 (Billing Record Phase)");
        $this->info('   - 创建计费记录');
        $this->info('   - 记录详细的资源使用信息');
        $this->info('   - 记录计算得出的费用');

        $this->info("\n4. 余额扣费阶段 (Balance Deduction Phase)");
        $this->info('   - 从用户余额中扣除费用');
        $this->info('   - 更新计费记录状态');
        $this->info('   - 触发余额变更事件');

        $this->info("\n5. 欠费恢复阶段 (Overdue Recovery Phase)");
        $this->info('   - 扫描因欠费暂停的工作空间');
        $this->info('   - 检查用户余额是否足够支付欠费');
        $this->info('   - 自动恢复余额充足的工作空间');
        $this->info('   - 重新启动暂停的资源');

        $this->info("\n6. 欠费处理阶段 (Overdue Handling Phase)");
        $this->info('   - 处理余额不足的工作空间');
        $this->info('   - 暂停资源');
        $this->info('   - 记录欠费金额');
        $this->info('   - 发送通知');

        $this->info("\n========================================");
    }

    /**
     * 处理单个集群
     */
    private function processSingleCluster(string $clusterName): array
    {
        $cluster = \App\Models\Cluster::where('name', $clusterName)->first();

        if (! $cluster) {
            throw new \Exception("集群 '{$clusterName}' 不存在");
        }

        return $this->billingService->processClusterBilling($cluster);
    }

    /**
     * 处理所有集群
     */
    private function processAllClusters(): array
    {
        return $this->billingService->processAllClustersBilling();
    }

    /**
     * 显示结果
     */
    private function displayResults(array $results, float $totalTime): void
    {
        if (empty($results['clusters'])) {
            $this->warn('没有启用计费的集群');

            return;
        }

        $this->info('📊 流水线执行详情:');

        foreach ($results['clusters'] as $clusterName => $clusterResult) {
            $this->line('');
            $this->line("🏗️  集群: {$clusterName}");

            if (! $clusterResult['success']) {
                $this->error("   ❌ 执行失败: {$clusterResult['error']}");

                continue;
            }

            $summary = $clusterResult['summary'];
            $executionTime = $clusterResult['processing_time_seconds'] ?? 0;
            $this->info("   ✅ 执行成功 (耗时: {$executionTime}s)");
            $this->line('   📋 工作空间统计:');
            $this->line("      • 有资源使用: {$summary['workspaces_with_resources']} 个");
            $this->line("      • 需要计费: {$summary['billable_workspaces']} 个");
            $this->line("      • 成功扣费: {$summary['successful_charges']} 个");
            $this->line("      • 恢复工作空间: {$summary['recovered_workspaces']} 个");
            $this->line("      • 欠费暂停: {$summary['overdue_workspaces']} 个");
            $this->info('   💰 总收入: '.formatAmountForCli($summary['total_revenue']));

            // 详细显示各阶段信息
            if ($this->output->isVerbose()) {
                $phases = $clusterResult['phases'] ?? [];
                $this->line('   🔄 流水线阶段详情:');

                $phaseNames = [
                    'resource_collection' => '资源收集',
                    'cost_calculation' => '成本计算',
                    'billing_record' => '计费记录',
                    'balance_deduction' => '余额扣费',
                    'overdue_recovery' => '欠费恢复',
                    'overdue_handling' => '欠费处理',
                ];

                foreach ($phaseNames as $phaseKey => $phaseName) {
                    if (isset($phases[$phaseKey])) {
                        $phase = $phases[$phaseKey];
                        $status = $phase['success'] ? '✅' : '❌';
                        $executionTimeMs = $phase['execution_time_ms'] ?? 0;
                        $this->line("      {$status} {$phaseName}: {$executionTimeMs}ms");

                        if (! $phase['success'] && isset($phase['error'])) {
                            $this->error("         错误: {$phase['error']}");
                        }
                    }
                }
            }
        }

        // 显示总体摘要
        $batchSummary = $results['summary'];
        $this->info('');
        $this->info('📈 批处理摘要:');

        $summaryData = [
            ['计费工作空间总数', $batchSummary['total_billable_workspaces']],
            ['总收入', formatAmountForCli($batchSummary['total_revenue'])],
            ['恢复工作空间', $batchSummary['total_recovered_workspaces']],
            ['欠费工作空间', $batchSummary['total_overdue_workspaces']],
            ['成功集群', $batchSummary['successful_clusters']],
            ['失败集群', $batchSummary['failed_clusters']],
            ['总处理时间', number_format($totalTime, 6).' 秒'],
        ];

        $this->table(['指标', '数值'], $summaryData);

        // 额外统计信息
        if ($batchSummary['total_billable_workspaces'] > 0) {
            $avgRevenue = bcdiv($batchSummary['total_revenue'], (string) $batchSummary['total_billable_workspaces'], 8);
            $this->info('');
            $this->info('💡 统计信息:');
            $this->info('   平均每工作空间费用: '.formatAmountForCli($avgRevenue));

            if ($batchSummary['total_overdue_workspaces'] > 0) {
                $overdueRate = round(($batchSummary['total_overdue_workspaces'] / $batchSummary['total_billable_workspaces']) * 100, 2);
                $this->warn("   欠费率: {$overdueRate}%");

                if ($overdueRate > 10) {
                    $this->error('   ⚠️  欠费率较高，建议关注用户余额管理');
                }
            }
        }

        // 性能分析
        if ($totalTime > 0 && $batchSummary['total_billable_workspaces'] > 0) {
            $avgProcessingTime = round($totalTime / $batchSummary['total_billable_workspaces'], 3);
            $this->info("   平均每工作空间处理时间: {$avgProcessingTime} 秒");

            if ($avgProcessingTime > 0.5) {
                $this->warn('   ⚠️  处理时间较长，可能需要优化');
            }
        }

        $this->newLine();
        $this->info('✅ 计费流水线执行完成');
        $this->info('总耗时: '.number_format($totalTime, 6).' 秒');
    }
}
