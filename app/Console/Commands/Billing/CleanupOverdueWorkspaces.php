<?php

namespace App\Console\Commands\Billing;

use App\Models\Workspace;
use App\Service\WorkspaceService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupOverdueWorkspaces extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:cleanup-overdue-workspaces 
                          {--dry-run : 仅预览，不实际删除工作空间}
                          {--days= : 指定删除天数，覆盖配置文件}
                          {--batch-size= : 每批处理的数量，覆盖配置文件}
                          {--force : 强制删除，跳过确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理暂停超过指定天数的欠费工作空间';

    /**
     * Execute the console command.
     */
    public function handle(WorkspaceService $workspaceService)
    {
        $startTime = now();
        $this->info("开始清理过期工作空间任务：{$startTime->format('Y-m-d H:i:s')}");

        // 检查清理功能是否启用
        if (! config('billing.cleanup.enabled', true) && ! $this->option('force')) {
            $this->warn('工作空间清理功能已禁用，使用 --force 参数强制执行');

            return 1;
        }

        // 获取配置参数
        $deletionDays = $this->option('days') ?: config('billing.overdue.deletion_days', 7);
        $batchSize = $this->option('batch-size') ?: config('billing.cleanup.batch_size', 50);
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        if ($isDryRun) {
            $this->warn("\n--- 预览模式 ---");
            $this->info('这是预览模式，不会实际删除工作空间');
        }

        $this->info('清理配置：');
        $this->info("- 删除天数：{$deletionDays} 天");
        $this->info("- 批处理大小：{$batchSize}");
        $this->info('- 干运行模式：'.($isDryRun ? '是' : '否'));

        // 计算截止时间
        $cutoffDate = Carbon::now()->subDays($deletionDays);
        $this->info("- 截止时间：{$cutoffDate->format('Y-m-d H:i:s')}");

        try {
            // 查找需要删除的工作空间
            $workspacesToDelete = $this->findWorkspacesToDelete($cutoffDate, $batchSize);

            if ($workspacesToDelete->isEmpty()) {
                $this->info("\n没有找到需要清理的工作空间");

                return 0;
            }

            $this->info("\n发现 {$workspacesToDelete->count()} 个工作空间需要清理：");

            // 显示详细信息
            $this->displayWorkspacesToDelete($workspacesToDelete);

            if (! $isDryRun && ! $isForced) {
                if (! $this->confirm("\n确定要删除这些工作空间吗？此操作不可逆！")) {
                    $this->info('操作已取消');

                    return 0;
                }
            }

            // 执行删除操作
            $results = $this->deleteWorkspaces($workspacesToDelete, $workspaceService, $isDryRun);

            // 显示结果
            $this->displayResults($results, $isDryRun);

            $endTime = now();
            $duration = $endTime->diffInSeconds($startTime);

            $this->info("\n清理任务完成");
            $this->info("开始时间：{$startTime->format('Y-m-d H:i:s')}");
            $this->info("结束时间：{$endTime->format('Y-m-d H:i:s')}");
            $this->info("耗时：{$duration} 秒");

            return 0;
        } catch (\Exception $e) {
            $this->error("清理任务执行失败：{$e->getMessage()}");
            Log::error('清理过期工作空间失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * 查找需要删除的工作空间
     */
    protected function findWorkspacesToDelete(Carbon $cutoffDate, int $batchSize)
    {
        return Workspace::where('status', Workspace::STATUS_SUSPENDED)
            ->where('suspension_reason', 'overdue')
            ->where('suspended_at', '<=', $cutoffDate)
            ->whereNotNull('suspended_at')
            ->whereNotNull('overdue_amount')
            ->where('overdue_amount', '>', 0)
            ->with(['user', 'cluster'])
            ->limit($batchSize)
            ->get();
    }

    /**
     * 显示要删除的工作空间列表
     */
    protected function displayWorkspacesToDelete($workspaces): void
    {
        $headers = ['ID', '名称', '用户', '集群', '暂停时间', '欠费金额', '暂停天数'];
        $rows = [];

        foreach ($workspaces as $workspace) {
            $suspendedDays = Carbon::parse($workspace->suspended_at)->diffInDays(now());

            $rows[] = [
                $workspace->id,
                $workspace->name,
                $workspace->user->name ?? 'N/A',
                $workspace->cluster->name ?? 'N/A',
                $workspace->suspended_at ? $workspace->suspended_at->format('Y-m-d H:i:s') : 'N/A',
                $workspace->overdue_amount ? "¥{$workspace->overdue_amount}" : 'N/A',
                "{$suspendedDays} 天",
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * 删除工作空间
     */
    protected function deleteWorkspaces($workspaces, WorkspaceService $workspaceService, bool $isDryRun): array
    {
        $results = [
            'total' => $workspaces->count(),
            'deleted' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($workspaces as $workspace) {
            try {
                if ($isDryRun) {
                    $this->info("  [预览] 将删除工作空间：{$workspace->name} (ID: {$workspace->id})");
                    $results['deleted']++;
                } else {
                    $this->info("  正在删除工作空间：{$workspace->name} (ID: {$workspace->id})");

                    // 记录删除前的信息用于日志
                    $deleteInfo = [
                        'workspace_id' => $workspace->id,
                        'workspace_name' => $workspace->name,
                        'user_id' => $workspace->user_id,
                        'user_name' => $workspace->user->name ?? 'N/A',
                        'cluster_id' => $workspace->cluster_id,
                        'cluster_name' => $workspace->cluster->name ?? 'N/A',
                        'suspended_at' => $workspace->suspended_at?->toISOString(),
                        'overdue_amount' => $workspace->overdue_amount,
                        'suspended_days' => Carbon::parse($workspace->suspended_at)->diffInDays(now()),
                        'deleted_at' => now()->toISOString(),
                        'deletion_reason' => 'overdue_cleanup',
                    ];

                    // 使用WorkspaceService启动删除流程
                    $workspaceService->startWorkspaceDeletion($workspace);
                    $success = true;

                    if ($success) {
                        $results['deleted']++;

                        Log::info('自动清理过期工作空间成功', $deleteInfo);

                        // 触发工作空间自动删除事件
                        \App\Events\WorkspaceAutoDeleted::dispatch(
                            $workspace->user,
                            $deleteInfo,
                            '工作空间因欠费超过'.config('billing.overdue.deletion_days', 7).'天已被自动删除'
                        );

                        $this->info('    ✓ 删除成功');
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "工作空间 {$workspace->name} (ID: {$workspace->id}) 删除失败";

                        Log::error('自动清理过期工作空间失败', array_merge($deleteInfo, [
                            'error' => '服务返回删除失败',
                        ]));

                        $this->error('    ✗ 删除失败');
                    }
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "工作空间 {$workspace->name} (ID: {$workspace->id}) 删除异常：{$e->getMessage()}";

                Log::error('自动清理过期工作空间异常', [
                    'workspace_id' => $workspace->id,
                    'workspace_name' => $workspace->name,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                $this->error("    ✗ 删除异常：{$e->getMessage()}");
            }
        }

        return $results;
    }

    /**
     * 显示执行结果
     */
    protected function displayResults(array $results, bool $isDryRun): void
    {
        $this->info("\n=== ".($isDryRun ? '预览' : '清理').'结果统计 ===');

        $this->table(
            ['项目', '数量'],
            [
                ['总工作空间数', $results['total']],
                [($isDryRun ? '预计删除' : '成功删除'), $results['deleted']],
                ['删除失败', $results['failed']],
                ['成功率', $results['total'] > 0 ? round(($results['deleted'] / $results['total']) * 100, 2).'%' : '0%'],
            ]
        );

        // 显示错误信息
        if (! empty($results['errors'])) {
            $this->error("\n错误详情：");
            foreach ($results['errors'] as $error) {
                $this->error("  - {$error}");
            }
        }

        if (! $isDryRun && $results['deleted'] > 0) {
            $this->info("\n💡 提示：删除任务已启动，Kubernetes资源清理可能需要一些时间完成");
            $this->comment('   可以通过日志查看详细的清理进度');
        }
    }
}
