<?php

namespace App\Console\Commands\RedeemCode;

use App\Models\RedeemCode;
use Illuminate\Console\Command;

class ListRedeemCodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'redeem-code:list 
                            {--active : 只显示启用的兑换码}
                            {--available : 只显示可用的兑换码}
                            {--expired : 只显示过期的兑换码}
                            {--exhausted : 只显示已用完的兑换码}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '列出兑换码';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $activeOnly = $this->option('active');
        $availableOnly = $this->option('available');
        $expiredOnly = $this->option('expired');
        $exhaustedOnly = $this->option('exhausted');

        $query = RedeemCode::with('usages');

        // 应用过滤条件
        if ($activeOnly) {
            $query->where('is_active', true);
        }

        if ($availableOnly) {
            $query->where('is_active', true)
                ->where(function ($q) {
                    $q->whereNull('expires_at')
                        ->orWhere('expires_at', '>', now());
                })
                ->whereColumn('used_count', '<', 'max_uses');
        }

        if ($expiredOnly) {
            $query->where('expires_at', '<=', now());
        }

        if ($exhaustedOnly) {
            $query->whereColumn('used_count', '>=', 'max_uses');
        }

        $codes = $query->orderBy('created_at', 'desc')->get();

        if ($codes->isEmpty()) {
            $this->info('没有找到兑换码');

            return Command::SUCCESS;
        }

        // 显示统计信息
        $this->info("找到 {$codes->count()} 个兑换码");

        $totalAmount = $codes->sum('amount');
        $totalUsedAmount = $codes->sum(function ($code) {
            return $code->usages->sum('amount');
        });

        $this->table(
            ['统计项', '值'],
            [
                ['总数量', $codes->count()],
                ['总金额', '¥'.number_format($totalAmount, 2)],
                ['已使用金额', '¥'.number_format($totalUsedAmount, 2)],
                ['剩余金额', '¥'.number_format($totalAmount - $totalUsedAmount, 2)],
            ]
        );

        $this->newLine();

        // 显示兑换码列表
        $tableData = [];
        foreach ($codes as $code) {
            $status = [];

            if (! $code->is_active) {
                $status[] = '已禁用';
            } elseif ($code->isExpired()) {
                $status[] = '已过期';
            } elseif ($code->isExhausted()) {
                $status[] = '已用完';
            } else {
                $status[] = '可用';
            }

            $tableData[] = [
                $code->code,
                '¥'.number_format($code->amount, 2),
                $code->used_count.'/'.$code->max_uses,
                $code->expires_at ? $code->expires_at->format('Y-m-d H:i:s') : '无限制',
                implode(', ', $status),
                $code->description ?: '-',
                $code->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table(
            ['兑换码', '金额', '使用次数', '过期时间', '状态', '描述', '创建时间'],
            $tableData
        );

        return Command::SUCCESS;
    }
}
