<?php

namespace App\Console\Commands\RedeemCode;

use App\Models\RedeemCode;
use Illuminate\Console\Command;

class CreateRedeemCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'redeem-code:create 
                            {amount : 兑换金额}
                            {--count=1 : 生成数量}
                            {--max-uses=1 : 最大使用次数}
                            {--expires-hours= : 有效期（小时）}
                            {--description= : 描述}
                            {--length=16 : 兑换码长度}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建兑换码';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $amount = (float) $this->argument('amount');
        $count = (int) $this->option('count');
        $maxUses = (int) $this->option('max-uses');
        $expiresHours = $this->option('expires-hours');
        $description = $this->option('description');
        $length = (int) $this->option('length');

        // 验证参数
        if ($amount <= 0) {
            $this->error('兑换金额必须大于0');

            return Command::FAILURE;
        }

        if ($count <= 0 || $count > 100) {
            $this->error('生成数量必须在1-100之间');

            return Command::FAILURE;
        }

        if ($maxUses <= 0) {
            $this->error('最大使用次数必须大于0');

            return Command::FAILURE;
        }

        if ($length < 8 || $length > 32) {
            $this->error('兑换码长度必须在8-32之间');

            return Command::FAILURE;
        }

        // 计算过期时间
        $expiresAt = null;
        if ($expiresHours) {
            $hours = (int) $expiresHours;
            if ($hours <= 0) {
                $this->error('有效期必须大于0小时');

                return Command::FAILURE;
            }
            $expiresAt = now()->addHours($hours);
        }

        // 显示确认信息
        $this->info('即将创建兑换码:');
        $this->table(
            ['参数', '值'],
            [
                ['兑换金额', '¥'.number_format($amount, 2)],
                ['生成数量', $count],
                ['最大使用次数', $maxUses],
                ['有效期', $expiresAt ? $expiresAt->format('Y-m-d H:i:s') : '无限制'],
                ['描述', $description ?: '无'],
                ['兑换码长度', $length],
            ]
        );

        if (! $this->confirm('确认创建？')) {
            $this->info('操作已取消');

            return Command::SUCCESS;
        }

        // 创建兑换码
        $codes = [];
        $bar = $this->output->createProgressBar($count);
        $bar->start();

        for ($i = 0; $i < $count; $i++) {
            $code = RedeemCode::create([
                'code' => RedeemCode::generateCode($length),
                'amount' => $amount,
                'max_uses' => $maxUses,
                'expires_at' => $expiresAt,
                'description' => $description,
            ]);

            $codes[] = $code;
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        // 显示创建结果
        $this->info('✅ 兑换码创建成功！');

        $tableData = [];
        foreach ($codes as $code) {
            $tableData[] = [
                $code->code,
                '¥'.number_format($code->amount, 2),
                $code->max_uses,
                $code->expires_at ? $code->expires_at->format('Y-m-d H:i:s') : '无限制',
            ];
        }

        $this->table(
            ['兑换码', '金额', '最大使用次数', '过期时间'],
            $tableData
        );

        return Command::SUCCESS;
    }
}
