<?php

namespace App\Console\Commands\Network;

use App\Models\PoolIp;
use App\Models\PortAllocation;
use Illuminate\Console\Command;

class PortManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'port:manage {ip_address : The IP address to manage ports for}
                            {--block= : The port number to block (disable)}
                            {--unblock= : The port number to unblock (enable)}
                            {--reason= : A reason for blocking the port}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable or enable a specific port on a given IP address';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $ipAddress = $this->argument('ip_address');
        $blockPort = $this->option('block');
        $unblockPort = $this->option('unblock');
        $reason = $this->option('reason');

        $poolIp = PoolIp::where('ip_address', $ipAddress)->first();

        if (! $poolIp) {
            $this->error("IP address {$ipAddress} not found in any IP pool.");

            return;
        }

        if ($blockPort) {
            $this->blockPort($poolIp, (int) $blockPort, $reason);
        }

        if ($unblockPort) {
            $this->unblockPort($poolIp, (int) $unblockPort);
        }

        if (! $blockPort && ! $unblockPort) {
            $this->error('Please provide a port to block or unblock using --block or --unblock options.');
        }
    }

    private function blockPort(PoolIp $poolIp, int $port, ?string $reason)
    {
        if (! $poolIp->isPortInRange($port)) {
            $this->error("Port {$port} is not in the valid range for IP {$poolIp->ip_address}.");

            return;
        }

        $allocation = PortAllocation::firstOrNew([
            'pool_ip_id' => $poolIp->id,
            'port' => $port,
        ]);

        if ($allocation->exists && $allocation->is_disabled) {
            $this->warn("Port {$port} on IP {$poolIp->ip_address} is already disabled.");

            return;
        }

        // 如果端口是新创建的，给它一个 'released' 状态
        if (! $allocation->exists) {
            $allocation->status = PortAllocation::STATUS_RELEASED;
        }

        $allocation->is_disabled = true;
        $allocation->reason = $reason;
        $allocation->save();

        $this->info("Successfully disabled port {$port} on IP {$poolIp->ip_address}.");
    }

    private function unblockPort(PoolIp $poolIp, int $port)
    {
        $allocation = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('port', $port)
            ->first();

        if (! $allocation || ! $allocation->is_disabled) {
            $this->error("Port {$port} on IP {$poolIp->ip_address} is not disabled or does not exist.");

            return;
        }

        $allocation->is_disabled = false;
        $allocation->reason = null;
        $allocation->save();

        // 如果这个端口记录只用于禁用（没有关联服务），则在解除屏蔽后删除它
        if ($allocation->status === PortAllocation::STATUS_RELEASED && is_null($allocation->service_name)) {
            $allocation->delete();
        }

        $this->info("Successfully enabled port {$port} on IP {$poolIp->ip_address}.");
    }
}
