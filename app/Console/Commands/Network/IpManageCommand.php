<?php

namespace App\Console\Commands\Network;

use App\Models\PoolIp;
use Illuminate\Console\Command;

class IpManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ip:manage 
                            {--disable= : The IP address to disable}
                            {--enable= : The IP address to enable}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable or enable a specific IP address from being scheduled';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $disableIp = $this->option('disable');
        $enableIp = $this->option('enable');

        if ($disableIp) {
            $this->disableIp($disableIp);
        }

        if ($enableIp) {
            $this->enableIp($enableIp);
        }

        if (! $disableIp && ! $enableIp) {
            $this->error('Please provide an IP to disable or enable using --disable or --enable options.');
        }
    }

    private function disableIp(string $ipAddress)
    {
        $poolIp = PoolIp::where('ip_address', $ipAddress)->first();

        if (! $poolIp) {
            $this->error("IP address {$ipAddress} not found in any IP pool.");

            return;
        }

        if ($poolIp->is_disabled) {
            $this->warn("IP address {$ipAddress} is already disabled.");

            return;
        }

        $poolIp->update(['is_disabled' => true]);
        $this->info("Successfully disabled IP address: {$ipAddress}");
    }

    private function enableIp(string $ipAddress)
    {
        $poolIp = PoolIp::where('ip_address', $ipAddress)->first();

        if (! $poolIp) {
            $this->error("IP address {$ipAddress} not found in any IP pool.");

            return;
        }

        if (! $poolIp->is_disabled) {
            $this->warn("IP address {$ipAddress} is already enabled.");

            return;
        }

        $poolIp->update(['is_disabled' => false]);
        $this->info("Successfully enabled IP address: {$ipAddress}");
    }
}
