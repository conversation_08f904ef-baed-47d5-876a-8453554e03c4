<?php

namespace App\Console\Commands\Registry;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class RegistryImageInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'registry:image-info {image : The full image name (e.g., nginx:latest)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get information about a container image from a registry';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $image = $this->argument('image');
        $this->info("Fetching information for image: {$image}");

        try {
            [$repository, $tag] = $this->parseImageName($image);

            $token = $this->getAuthToken($repository);

            $manifest = $this->getManifest($repository, $tag, $token);

            if (isset($manifest['manifests'])) {
                $this->info('Image has multiple architectures, selecting amd64.');
                $digest = collect($manifest['manifests'])->firstWhere('platform.architecture', 'amd64')['digest'];
                $manifest = $this->getManifest($repository, $digest, $token);
            }

            $layers = $manifest['layers'];
            $configSize = $manifest['config']['size'];
            $layersSize = collect($layers)->sum('size');
            $totalSize = $configSize + $layersSize;

            $this->displayImageInfo($layers, $totalSize);

        } catch (\Exception $e) {
            $this->error('Error fetching image information: '.$e->getMessage());

            return 1;
        }

        return 0;
    }

    private function parseImageName(string $image): array
    {
        if (! Str::contains($image, ':')) {
            $image .= ':latest';
        }

        if (Str::contains($image, '/')) {
            [$repository, $tag] = explode(':', $image, 2);
        } else {
            $repository = 'library/'.$image;
            [$repository, $tag] = explode(':', $repository, 2);
        }

        return [$repository, $tag];
    }

    private function getAuthToken(string $repository): string
    {
        $authUrl = "https://auth.docker.io/token?service=registry.docker.io&scope=repository:{$repository}:pull";

        $timeout = config('k8s.imageValidation.timeout_seconds', 30);
        $httpClient = Http::retry(3, 2000)->timeout($timeout);

        // 配置代理
        if (config('app.proxy.enabled')) {
            $httpClient = $httpClient->withOptions([
                'proxy' => [
                    'http' => config('app.proxy.http'),
                    'https' => config('app.proxy.https'),
                ],
                'verify' => false,
            ]);
        }

        $response = $httpClient->get($authUrl);
        $response->throw();

        return $response->json('token');
    }

    private function getManifest(string $repository, string $tagOrDigest, string $token): array
    {
        $manifestUrl = "https://registry-1.docker.io/v2/{$repository}/manifests/{$tagOrDigest}";

        $timeout = config('k8s.imageValidation.timeout_seconds', 30);
        $httpClient = Http::withToken($token)
            ->withHeaders([
                'Accept' => 'application/vnd.docker.distribution.manifest.v2+json, application/vnd.docker.distribution.manifest.list.v2+json',
            ])
            ->retry(3, 2000)
            ->timeout($timeout);

        // 配置代理
        if (config('app.proxy.enabled')) {
            $httpClient = $httpClient->withOptions([
                'proxy' => [
                    'http' => config('app.proxy.http'),
                    'https' => config('app.proxy.https'),
                ],
                'verify' => false,
            ]);
        }

        $response = $httpClient->get($manifestUrl);
        $response->throw();

        return $response->json();
    }

    private function displayImageInfo(array $layers, int $totalSize): void
    {
        $this->info('Image Layers:');
        $this->table(
            ['Digest', 'Size (Bytes)'],
            collect($layers)->map(fn ($layer) => [
                $layer['digest'],
                $layer['size'],
            ])->toArray()
        );

        $this->info('Total Image Size: '.round($totalSize / 1024 / 1024, 2).' MB');
    }
}
