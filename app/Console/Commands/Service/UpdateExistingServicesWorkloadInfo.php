<?php

namespace App\Console\Commands\Service;

use App\Models\Service;
use App\Models\Workspace;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateExistingServicesWorkloadInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'services:update-workload-info 
                            {--dry-run : 只显示将要更新的内容，不实际执行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新现有 Service 记录的 workload 信息，从 Kubernetes Service 标签中提取';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - 不会实际更新数据');
        }

        $services = Service::whereNull('target_workload_type')
            ->orWhereNull('target_workload_name')
            ->with('workspace.cluster')
            ->get();

        if ($services->isEmpty()) {
            $this->info('✅ 没有需要更新的 Service 记录');

            return 0;
        }

        $this->info("📝 找到 {$services->count()} 个需要更新的 Service 记录");

        $updatedCount = 0;
        $failedCount = 0;

        foreach ($services as $service) {
            try {
                $workspace = $service->workspace;

                if (! $workspace || ! $workspace->cluster) {
                    $this->warn("⚠️  Service '{$service->name}' 的工作空间或集群信息不完整，跳过");
                    $failedCount++;

                    continue;
                }

                // 从 Kubernetes 获取 Service 信息
                $response = $workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$workspace->namespace}/services/{$service->name}");

                if (! $response->successful()) {
                    $this->warn("⚠️  无法获取 Kubernetes Service '{$service->name}'，跳过");
                    $failedCount++;

                    continue;
                }

                $k8sService = $response->json();
                $labels = $k8sService['metadata']['labels'] ?? [];

                // 从标签中提取 workload 信息
                $targetWorkload = $labels['target_workload'] ?? null;

                if ($targetWorkload && strpos($targetWorkload, '/') !== false) {
                    [$workloadType, $workloadName] = explode('/', $targetWorkload, 2);

                    $this->line("📄 Service '{$service->name}': {$workloadType}/{$workloadName}");

                    if (! $isDryRun) {
                        $service->update([
                            'target_workload_type' => $workloadType,
                            'target_workload_name' => $workloadName,
                        ]);
                    }

                    $updatedCount++;
                } else {
                    // 尝试从选择器推断 workload
                    $selector = $k8sService['spec']['selector'] ?? [];
                    $appLabel = $selector['app'] ?? null;

                    if ($appLabel) {
                        // 检查是否存在对应的 Deployment 或 StatefulSet
                        $workloadType = $this->findWorkloadType($workspace, $appLabel);

                        if ($workloadType) {
                            $this->line("📄 Service '{$service->name}': {$workloadType}/{$appLabel} (从选择器推断)");

                            if (! $isDryRun) {
                                $service->update([
                                    'target_workload_type' => $workloadType,
                                    'target_workload_name' => $appLabel,
                                ]);
                            }

                            $updatedCount++;
                        } else {
                            $this->warn("⚠️  Service '{$service->name}' 无法找到对应的工作负载，跳过");
                            $failedCount++;
                        }
                    } else {
                        $this->warn("⚠️  Service '{$service->name}' 没有标准的选择器标签，跳过");
                        $failedCount++;
                    }
                }

            } catch (\Exception $e) {
                $this->error("❌ 更新 Service '{$service->name}' 失败: {$e->getMessage()}");
                Log::error('更新 Service workload 信息失败', [
                    'service_id' => $service->id,
                    'service_name' => $service->name,
                    'error' => $e->getMessage(),
                ]);
                $failedCount++;
            }
        }

        if ($isDryRun) {
            $this->info("🔍 DRY RUN 完成: 可更新 {$updatedCount} 个，失败 {$failedCount} 个");
        } else {
            $this->info("✅ 更新完成: 成功 {$updatedCount} 个，失败 {$failedCount} 个");
        }

        return 0;
    }

    /**
     * 查找工作负载类型
     */
    private function findWorkloadType(Workspace $workspace, string $name): ?string
    {
        // 检查 Deployment
        try {
            $deploymentResponse = $workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$workspace->namespace}/deployments/{$name}");

            if ($deploymentResponse->successful()) {
                return 'Deployment';
            }
        } catch (\Exception $e) {
            // 404 错误是正常的，继续检查 StatefulSet
        }

        // 检查 StatefulSet
        try {
            $statefulSetResponse = $workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$workspace->namespace}/statefulsets/{$name}");

            if ($statefulSetResponse->successful()) {
                return 'StatefulSet';
            }
        } catch (\Exception $e) {
            // 404 错误是正常的
        }

        return null;
    }
}
