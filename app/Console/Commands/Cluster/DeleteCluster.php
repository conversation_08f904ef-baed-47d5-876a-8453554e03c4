<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class DeleteCluster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:delete {name} {--force : 强制删除，不显示确认提示}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除一个 Kubernetes 集群';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $force = $this->option('force');

        // 查找集群
        $cluster = Cluster::where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 显示集群信息
        $this->info('集群信息：');
        $this->table(
            ['字段', '值'],
            [
                ['ID', $cluster->id],
                ['名称', $cluster->name],
                ['服务器地址', $cluster->server_url],
                ['认证类型', $cluster->auth_type],
                ['创建时间', $cluster->created_at->format('Y-m-d H:i:s')],
                ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
            ]
        );

        // 检查是否有关联的工作空间
        $workspaceCount = $cluster->workspaces()->count();
        if ($workspaceCount > 0) {
            $this->warn("警告：该集群有 {$workspaceCount} 个关联的工作空间");
            $this->warn('删除集群将会影响这些工作空间的正常运行');
        }

        // 确认删除
        if (! $force) {
            if (! $this->confirm("确定要删除集群 {$name} 吗？")) {
                $this->info('操作已取消');

                return 0;
            }
        }

        try {
            $cluster->delete();
            $this->info("集群 {$name} 删除成功！");

            return 0;
        } catch (\Exception $e) {
            $this->error("删除集群失败：{$e->getMessage()}");

            return 1;
        }
    }
}
