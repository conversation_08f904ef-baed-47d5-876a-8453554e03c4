<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class SetClusterIngressExternalIps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:set-ingress-external-ips {cluster_name} {external_ips?*} {--clear : 清空所有 Ingress 外部 IP}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置集群的 Ingress 外部 IP 地址';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $clusterName = $this->argument('cluster_name');
        $externalIps = $this->argument('external_ips');
        $clear = $this->option('clear');

        try {
            // 查找集群
            $cluster = Cluster::where('name', $clusterName)->first();
            if (! $cluster) {
                $this->error("集群 '{$clusterName}' 不存在");

                return 1;
            }

            if ($clear) {
                // 清空 Ingress 外部 IP
                $cluster->setSetting('ingress_external_ips', []);
                $this->info("已清空集群 '{$clusterName}' 的 Ingress 外部 IP 配置");

                return 0;
            }

            if (empty($externalIps)) {
                $this->error('请提供至少一个外部 IP 地址');

                return 1;
            }

            // 验证 IP 地址格式
            foreach ($externalIps as $ip) {
                if (! filter_var($ip, FILTER_VALIDATE_IP)) {
                    $this->error("无效的 IP 地址: {$ip}");

                    return 1;
                }
            }

            // 设置 Ingress 外部 IP
            $cluster->setSetting('ingress_external_ips', array_values(array_unique($externalIps)));

            $this->info("已设置集群 '{$clusterName}' 的 Ingress 外部 IP:");
            foreach ($externalIps as $ip) {
                $this->line("  - {$ip}");
            }

            $this->info("\n现在可以通过以下端口访问 Ingress:");
            $this->line('  - HTTP: 80');
            $this->line('  - HTTPS: 443');

            return 0;
        } catch (\Exception $e) {
            $this->error('设置 Ingress 外部 IP 失败: '.$e->getMessage());

            return 1;
        }
    }
}
