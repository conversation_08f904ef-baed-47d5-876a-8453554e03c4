<?php

namespace App\Console\Commands\Cluster;

use App\DTOs\KubeconfigDTO;
use App\Models\Cluster;
use Illuminate\Console\Command;

class UpdateCluster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:update {name} {--kubeconfig-file= : 新的 kubeconfig 文件路径} {--new-name= : 新的集群名称}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新一个 Kubernetes 集群';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $kubeconfigFile = $this->option('kubeconfig-file');
        $newName = $this->option('new-name');

        // 查找集群
        $cluster = Cluster::where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 如果没有提供任何更新选项，显示帮助信息
        if (! $kubeconfigFile && ! $newName) {
            $this->error('请提供至少一个更新选项');
            $this->info('可用选项：');
            $this->info('  --kubeconfig-file=PATH  更新集群配置');
            $this->info('  --new-name=NAME         更新集群名称');

            return 1;
        }

        // 显示当前集群信息
        $this->info('当前集群信息：');
        $this->table(
            ['字段', '值'],
            [
                ['ID', $cluster->id],
                ['名称', $cluster->name],
                ['服务器地址', $cluster->server_url],
                ['认证类型', $cluster->auth_type],
                ['创建时间', $cluster->created_at->format('Y-m-d H:i:s')],
                ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
            ]
        );

        $updateData = [];

        // 处理名称更新
        if ($newName) {
            // 检查新名称是否已存在
            if (Cluster::where('name', $newName)->where('id', '!=', $cluster->id)->exists()) {
                $this->error("集群名称 {$newName} 已存在");

                return 1;
            }
            $updateData['name'] = $newName;
            $this->info("将更新集群名称为：{$newName}");
        }

        // 处理 kubeconfig 更新
        if ($kubeconfigFile) {
            // 检查 kubeconfig 文件是否存在
            if (! file_exists($kubeconfigFile)) {
                $this->error("kubeconfig 文件不存在: {$kubeconfigFile}");

                return 1;
            }

            // 检查文件是否可读
            if (! is_readable($kubeconfigFile)) {
                $this->error("kubeconfig 文件不可读: {$kubeconfigFile}");

                return 1;
            }

            try {
                // 读取 kubeconfig 文件内容
                $kubeconfig = file_get_contents($kubeconfigFile);

                if (empty($kubeconfig)) {
                    $this->error('kubeconfig 文件内容为空');

                    return 1;
                }

                // 解析 kubeconfig
                $kubeconfigDTO = KubeconfigDTO::fromKubeconfig($kubeconfig);
                $updateData = array_merge($updateData, $kubeconfigDTO->toArray());

                $this->info('将更新集群配置：');
                $this->info("  服务器地址: {$kubeconfigDTO->serverUrl}");
                $this->info("  认证类型: {$kubeconfigDTO->authType}");
            } catch (\Exception $e) {
                $this->error("解析 kubeconfig 失败：{$e->getMessage()}");

                return 1;
            }
        }

        // 确认更新
        if (! $this->confirm("确定要更新集群 {$name} 吗？")) {
            $this->info('操作已取消');

            return 0;
        }

        try {
            $cluster->update($updateData);
            $this->info("集群 {$name} 更新成功！");

            // 显示更新后的信息
            $cluster->refresh();
            $this->info('更新后的集群信息：');
            $this->table(
                ['字段', '值'],
                [
                    ['ID', $cluster->id],
                    ['名称', $cluster->name],
                    ['服务器地址', $cluster->server_url],
                    ['认证类型', $cluster->auth_type],
                    ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
                ]
            );

            return 0;
        } catch (\Exception $e) {
            $this->error("更新集群失败：{$e->getMessage()}");

            return 1;
        }
    }
}
