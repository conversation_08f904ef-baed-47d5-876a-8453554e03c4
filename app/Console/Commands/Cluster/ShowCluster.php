<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class ShowCluster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:show {name} {--format=table : 输出格式 (table|json)} {--show-sensitive : 显示敏感信息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '显示指定集群的详细信息';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $format = $this->option('format');
        $showSensitive = $this->option('show-sensitive');

        // 查找集群
        $cluster = Cluster::with('workspaces')->where('name', $name)->first();

        if (! $cluster) {
            $this->error("集群 {$name} 不存在");

            return 1;
        }

        // 根据格式输出
        switch ($format) {
            case 'json':
                $this->outputJson($cluster, $showSensitive);
                break;
            case 'table':
            default:
                $this->outputTable($cluster, $showSensitive);
                break;
        }

        return 0;
    }

    /**
     * 以表格格式输出集群详细信息
     */
    private function outputTable($cluster, $showSensitive)
    {
        $this->info('集群详细信息：');

        $basicInfo = [
            ['ID', $cluster->id],
            ['名称', $cluster->name],
            ['服务器地址', $cluster->server_url],
            ['认证类型', $cluster->auth_type],
            ['跳过 TLS 验证', $cluster->insecure_skip_tls_verify ? '是' : '否'],
            ['创建时间', $cluster->created_at->format('Y-m-d H:i:s')],
            ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
        ];

        $this->table(['字段', '值'], $basicInfo);

        // 显示认证信息（敏感信息）
        if ($showSensitive) {
            $this->warn("\n敏感信息：");
            $sensitiveInfo = [];

            if ($cluster->certificate_authority_data) {
                $sensitiveInfo[] = ['CA 证书', $this->truncateString($cluster->certificate_authority_data, 50)];
            }

            switch ($cluster->auth_type) {
                case 'token':
                    if ($cluster->token) {
                        $sensitiveInfo[] = ['Token', $this->truncateString($cluster->token, 50)];
                    }
                    break;
                case 'certificate':
                    if ($cluster->client_certificate_data) {
                        $sensitiveInfo[] = ['客户端证书', $this->truncateString($cluster->client_certificate_data, 50)];
                    }
                    if ($cluster->client_key_data) {
                        $sensitiveInfo[] = ['客户端密钥', $this->truncateString($cluster->client_key_data, 50)];
                    }
                    break;
                case 'username_password':
                    if ($cluster->username) {
                        $sensitiveInfo[] = ['用户名', $cluster->username];
                    }
                    if ($cluster->password) {
                        $sensitiveInfo[] = ['密码', str_repeat('*', strlen($cluster->password))];
                    }
                    break;
            }

            if (! empty($sensitiveInfo)) {
                $this->table(['字段', '值'], $sensitiveInfo);
            }
        } else {
            $this->info("\n使用 --show-sensitive 选项查看敏感信息");
        }

        // 显示关联的工作空间
        $workspaces = $cluster->workspaces;
        if ($workspaces->count() > 0) {
            $this->info("\n关联的工作空间 ({$workspaces->count()} 个)：");
            $workspaceRows = [];
            foreach ($workspaces as $workspace) {
                $workspaceRows[] = [
                    $workspace->id,
                    $workspace->name,
                    $workspace->namespace,
                    $workspace->status,
                    $workspace->user->name ?? 'N/A',
                    $workspace->created_at->format('Y-m-d H:i:s'),
                ];
            }
            $this->table(
                ['ID', '名称', 'Namespace', '状态', '用户', '创建时间'],
                $workspaceRows
            );
        } else {
            $this->info("\n该集群没有关联的工作空间");
        }

        // 测试一下集群的连接
        // 获取 Pods
        $pods = $cluster->http()->get('/api/v1/namespaces/default/pods');
        $this->info("\n获取 Pods 成功：");
        $this->info($pods->body());
    }

    /**
     * 以 JSON 格式输出集群详细信息
     */
    private function outputJson($cluster, $showSensitive)
    {
        $data = [
            'id' => $cluster->id,
            'name' => $cluster->name,
            'server_url' => $cluster->server_url,
            'auth_type' => $cluster->auth_type,
            'insecure_skip_tls_verify' => $cluster->insecure_skip_tls_verify,
            'created_at' => $cluster->created_at->toISOString(),
            'updated_at' => $cluster->updated_at->toISOString(),
            'workspaces' => $cluster->workspaces->map(function ($workspace) {
                return [
                    'id' => $workspace->id,
                    'name' => $workspace->name,
                    'namespace' => $workspace->namespace,
                    'status' => $workspace->status,
                    'user_name' => $workspace->user->name ?? null,
                    'created_at' => $workspace->created_at->toISOString(),
                ];
            }),
        ];

        if ($showSensitive) {
            $data['sensitive'] = [
                'certificate_authority_data' => $cluster->certificate_authority_data,
                'token' => $cluster->token,
                'client_certificate_data' => $cluster->client_certificate_data,
                'client_key_data' => $cluster->client_key_data,
                'username' => $cluster->username,
                'password' => $cluster->password,
            ];
        }

        $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 截断字符串并添加省略号
     */
    private function truncateString($string, $length)
    {
        if (strlen($string) <= $length) {
            return $string;
        }

        return substr($string, 0, $length).'...';
    }
}
