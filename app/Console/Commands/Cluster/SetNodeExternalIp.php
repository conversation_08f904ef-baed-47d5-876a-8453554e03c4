<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SetNodeExternalIp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:set-node-external-ip {cluster_name} {node_name} {external_ip} {--remove : 移除节点的外部 IP 标签}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置集群节点的外部 IP 地址（通过标签）';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $clusterName = $this->argument('cluster_name');
        $nodeName = $this->argument('node_name');
        $externalIp = $this->argument('external_ip');
        $remove = $this->option('remove');

        // 查找集群
        $cluster = Cluster::where('name', $clusterName)->first();

        if (! $cluster) {
            $this->error("集群 {$clusterName} 不存在");

            return 1;
        }

        try {
            if ($remove) {
                $this->info("正在移除节点 {$nodeName} 的外部 IP 标签...");
                $this->removeNodeExternalIpLabel($cluster, $nodeName);
                $this->info("成功移除节点 {$nodeName} 的外部 IP 标签");
            } else {
                // 验证 IP 地址格式
                if (! filter_var($externalIp, FILTER_VALIDATE_IP)) {
                    $this->error("无效的 IP 地址格式: {$externalIp}");

                    return 1;
                }

                $this->info("正在为节点 {$nodeName} 设置外部 IP 地址: {$externalIp}");
                $this->setNodeExternalIpLabel($cluster, $nodeName, $externalIp);
                $this->info("成功为节点 {$nodeName} 设置外部 IP 地址: {$externalIp}");
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("操作失败：{$e->getMessage()}");
            Log::error('设置节点外部 IP 失败', [
                'cluster' => $clusterName,
                'node' => $nodeName,
                'external_ip' => $externalIp,
                'error' => $e->getMessage(),
            ]);

            return 1;
        }
    }

    /**
     * 为节点设置外部 IP 标签
     */
    private function setNodeExternalIpLabel(Cluster $cluster, string $nodeName, string $externalIp): void
    {
        // 首先获取节点信息以验证节点是否存在
        $response = $cluster->http()->get("/api/v1/nodes/{$nodeName}");

        if ($response->status() === 404) {
            throw new \Exception("节点 {$nodeName} 不存在");
        }

        if (! $response->successful()) {
            throw new \Exception("获取节点信息失败：HTTP {$response->status()}");
        }

        $nodeData = $response->json();

        // 显示当前节点信息
        $this->displayNodeInfo($nodeData);

        // 准备标签数据
        $labels = $nodeData['metadata']['labels'] ?? [];
        $labels['external-ip'] = $externalIp;

        // 构建 PATCH 请求数据
        $patchData = [
            'metadata' => [
                'labels' => $labels,
            ],
        ];

        // 发送 PATCH 请求更新节点标签
        $response = $cluster->http()
            ->withHeaders(['Content-Type' => 'application/merge-patch+json'])
            ->patch("/api/v1/nodes/{$nodeName}", $patchData);

        if (! $response->successful()) {
            throw new \Exception("更新节点标签失败：HTTP {$response->status()} - {$response->body()}");
        }

        // 验证标签是否设置成功
        $this->verifyLabelSet($cluster, $nodeName, $externalIp);
    }

    /**
     * 移除节点的外部 IP 标签
     */
    private function removeNodeExternalIpLabel(Cluster $cluster, string $nodeName): void
    {
        // 首先获取节点信息
        $response = $cluster->http()->get("/api/v1/nodes/{$nodeName}");

        if ($response->status() === 404) {
            throw new \Exception("节点 {$nodeName} 不存在");
        }

        if (! $response->successful()) {
            throw new \Exception("获取节点信息失败：HTTP {$response->status()}");
        }

        $nodeData = $response->json();

        // 检查是否存在外部 IP 标签
        $labels = $nodeData['metadata']['labels'] ?? [];
        if (! isset($labels['external-ip'])) {
            $this->warn("节点 {$nodeName} 没有设置外部 IP 标签");

            return;
        }

        // 使用 JSON Patch 格式移除标签
        $patchData = [
            [
                'op' => 'remove',
                'path' => '/metadata/labels/external-ip',
            ],
        ];

        // 发送 PATCH 请求移除节点标签
        $response = $cluster->http()
            ->withHeaders(['Content-Type' => 'application/json-patch+json'])
            ->patch("/api/v1/nodes/{$nodeName}", $patchData);

        if (! $response->successful()) {
            throw new \Exception("移除节点标签失败：HTTP {$response->status()} - {$response->body()}");
        }
    }

    /**
     * 显示节点信息
     */
    private function displayNodeInfo(array $nodeData): void
    {
        $nodeName = $nodeData['metadata']['name'];
        $nodeInfo = [
            ['字段', '值'],
            ['节点名称', $nodeName],
            ['Ready 状态', $this->getNodeReadyStatus($nodeData)],
            ['Kubernetes 版本', $nodeData['status']['nodeInfo']['kubeletVersion'] ?? 'Unknown'],
            ['操作系统', $nodeData['status']['nodeInfo']['osImage'] ?? 'Unknown'],
            ['容器运行时', $nodeData['status']['nodeInfo']['containerRuntimeVersion'] ?? 'Unknown'],
        ];

        // 显示当前外部 IP 标签
        $currentExternalIp = $nodeData['metadata']['labels']['external-ip'] ?? '未设置';
        $nodeInfo[] = ['当前外部 IP', $currentExternalIp];

        // 显示内部 IP 地址
        $addresses = $nodeData['status']['addresses'] ?? [];
        foreach ($addresses as $address) {
            if ($address['type'] === 'InternalIP') {
                $nodeInfo[] = ['内部 IP', $address['address']];
                break;
            }
        }

        $this->info('节点信息：');
        $this->table($nodeInfo[0], array_slice($nodeInfo, 1));
    }

    /**
     * 获取节点 Ready 状态
     */
    private function getNodeReadyStatus(array $nodeData): string
    {
        $conditions = $nodeData['status']['conditions'] ?? [];
        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Ready') {
                return $condition['status'] === 'True' ? 'Ready' : 'NotReady';
            }
        }

        return 'Unknown';
    }

    /**
     * 验证标签是否设置成功
     */
    private function verifyLabelSet(Cluster $cluster, string $nodeName, string $externalIp): void
    {
        // 重新获取节点信息验证
        $response = $cluster->http()->get("/api/v1/nodes/{$nodeName}");

        if (! $response->successful()) {
            throw new \Exception('验证标签设置失败：无法获取更新后的节点信息');
        }

        $nodeData = $response->json();
        $labels = $nodeData['metadata']['labels'] ?? [];

        if (! isset($labels['external-ip']) || $labels['external-ip'] !== $externalIp) {
            throw new \Exception('标签设置验证失败：外部 IP 标签未正确设置');
        }

        $this->info('标签设置验证成功');
    }
}
