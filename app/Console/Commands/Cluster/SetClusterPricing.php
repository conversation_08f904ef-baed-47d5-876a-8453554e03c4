<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use App\Models\ClusterPricing;
use Illuminate\Console\Command;

class SetClusterPricing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:pricing {cluster_name} 
                          {--memory-price= : 内存价格/GB/月}
                          {--cpu-price= : CPU价格/核/月}
                          {--storage-price= : 存储价格/GB/月}
                          {--loadbalancer-price= : LoadBalancer服务价格/个/月}
                          {--enable : 启用计费}
                          {--disable : 禁用计费}
                          {--description= : 定价说明}
                          {--preview : 预览每分钟价格}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置集群资源定价';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $clusterName = $this->argument('cluster_name');

        // 查找集群
        $cluster = Cluster::where('name', $clusterName)->first();
        if (! $cluster) {
            $this->error("集群 {$clusterName} 不存在");

            return 1;
        }

        // 获取或创建定价配置
        $pricing = $cluster->pricing ?? new ClusterPricing(['cluster_id' => $cluster->id]);

        // 更新价格
        if ($this->option('memory-price')) {
            $pricing->memory_price_per_gb = $this->option('memory-price');
        }
        if ($this->option('cpu-price')) {
            $pricing->cpu_price_per_core = $this->option('cpu-price');
        }
        if ($this->option('storage-price')) {
            $pricing->storage_price_per_gb = $this->option('storage-price');
        }
        if ($this->option('loadbalancer-price')) {
            $pricing->loadbalancer_price_per_service = $this->option('loadbalancer-price');
        }

        // 更新计费状态
        if ($this->option('enable')) {
            $pricing->billing_enabled = true;
        }
        if ($this->option('disable')) {
            $pricing->billing_enabled = false;
        }

        // 更新说明
        if ($this->option('description')) {
            $pricing->description = $this->option('description');
        }

        // 保存配置
        if (! $pricing->exists) {
            $pricing->cluster_id = $cluster->id;
        }
        $pricing->save();

        // 显示当前配置
        $this->info("集群 {$clusterName} 的定价配置：");
        $this->table(
            ['项目', '值'],
            [
                ['内存价格/GB/月', formatAmountForCli($pricing->memory_price_per_gb)],
                ['CPU价格/核/月', formatAmountForCli($pricing->cpu_price_per_core)],
                ['存储价格/GB/月', formatAmountForCli($pricing->storage_price_per_gb)],
                ['LoadBalancer价格/个/月', formatAmountForCli($pricing->loadbalancer_price_per_service)],
                ['计费状态', $pricing->billing_enabled ? '已启用' : '已禁用'],
                ['说明', $pricing->description ?: '无'],
            ]
        );

        // 显示预览
        if ($this->option('preview')) {
            $this->showPricePreview($pricing);
        }

        $this->info('定价配置已保存');

        return 0;
    }

    /**
     * 显示价格预览
     */
    private function showPricePreview(ClusterPricing $pricing): void
    {
        $this->info("\n每分钟价格预览：");

        // 示例资源配置
        $examples = [
            [
                'name' => '小型应用 (512Mi内存, 500m CPU)',
                'memory_mi' => 512,
                'cpu_m' => 500,
                'storage_gi' => 0,
                'loadbalancer_count' => 0,
            ],
            [
                'name' => '中型应用 (1Gi内存, 1000m CPU, 10Gi存储)',
                'memory_mi' => 1024,
                'cpu_m' => 1000,
                'storage_gi' => 10,
                'loadbalancer_count' => 0,
            ],
            [
                'name' => '大型应用 (2Gi内存, 2000m CPU, 50Gi存储, 1个LoadBalancer)',
                'memory_mi' => 2048,
                'cpu_m' => 2000,
                'storage_gi' => 50,
                'loadbalancer_count' => 1,
            ],
        ];

        foreach ($examples as $example) {
            $this->info("\n{$example['name']}:");

            $preview = $pricing->calculatePricePreview($example);

            $rows = [];
            if ($example['memory_mi'] > 0) {
                $rows[] = ['内存费用', formatAmountForCli($preview['memory_price_per_minute']).'/分钟'];
            }
            if ($example['cpu_m'] > 0) {
                $rows[] = ['CPU费用', formatAmountForCli($preview['cpu_price_per_minute']).'/分钟'];
            }
            if ($example['storage_gi'] > 0) {
                $rows[] = ['存储费用', formatAmountForCli($preview['storage_price_per_minute']).'/分钟'];
            }
            if ($example['loadbalancer_count'] > 0) {
                $rows[] = ['LoadBalancer费用', formatAmountForCli($preview['loadbalancer_price_per_minute']).'/分钟'];
            }

            $rows[] = ['合计', formatAmountForCli($preview['total_price_per_minute']).'/分钟'];

            // 计算小时和日费用
            $hourlyPrice = bcmul($preview['total_price_per_minute'], '60', 8);
            $dailyPrice = bcmul($hourlyPrice, '24', 8);

            $rows[] = ['', ''];
            $rows[] = ['每小时费用', formatAmountForCli($hourlyPrice)];
            $rows[] = ['每日费用', formatAmountForCli($dailyPrice)];

            $this->table(['项目', '价格'], $rows);
        }
    }
}
