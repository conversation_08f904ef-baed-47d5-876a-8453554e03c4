<?php

namespace App\Console\Commands\Cluster;

use App\DTOs\KubeconfigDTO;
use App\Models\Cluster;
use Illuminate\Console\Command;

class AddCluster extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:add {name} {kubeconfig-file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '添加一个新的 Kubernetes 集群';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $name = $this->argument('name');
        $kubeconfigFile = $this->argument('kubeconfig-file');

        // 检查集群名称是否已存在
        if (Cluster::where('name', $name)->exists()) {
            $this->error("集群名称 {$name} 已存在");

            return 1;
        }

        // 检查 kubeconfig 文件是否存在
        if (! file_exists($kubeconfigFile)) {
            $this->error("kubeconfig 文件不存在: {$kubeconfigFile}");

            return 1;
        }

        // 检查文件是否可读
        if (! is_readable($kubeconfigFile)) {
            $this->error("kubeconfig 文件不可读: {$kubeconfigFile}");

            return 1;
        }

        try {
            // 读取 kubeconfig 文件内容
            $kubeconfig = file_get_contents($kubeconfigFile);

            if (empty($kubeconfig)) {
                $this->error('kubeconfig 文件内容为空');

                return 1;
            }

            // 解析 kubeconfig
            $kubeconfigDTO = KubeconfigDTO::fromKubeconfig($kubeconfig);

            // 创建集群
            $clusterData = array_merge(
                ['name' => $name],
                $kubeconfigDTO->toArray()
            );

            Cluster::create($clusterData);

            $this->info("集群 {$name} 添加成功！");
            $this->info("服务器地址: {$kubeconfigDTO->serverUrl}");
            $this->info("认证类型: {$kubeconfigDTO->authType}");

            return 0;
        } catch (\Exception $e) {
            $this->error("添加集群失败：{$e->getMessage()}");

            return 1;
        }
    }
}
