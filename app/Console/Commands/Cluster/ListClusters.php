<?php

namespace App\Console\Commands\Cluster;

use App\Models\Cluster;
use Illuminate\Console\Command;

class ListClusters extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cluster:list {--name= : 按名称过滤} {--auth-type= : 按认证类型过滤} {--format=table : 输出格式 (table|json)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '列出所有 Kubernetes 集群';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $nameFilter = $this->option('name');
        $authTypeFilter = $this->option('auth-type');
        $format = $this->option('format');

        // 构建查询
        $query = Cluster::query();

        if ($nameFilter) {
            $query->where('name', 'like', "%{$nameFilter}%");
        }

        if ($authTypeFilter) {
            $query->where('auth_type', $authTypeFilter);
        }

        $clusters = $query->orderBy('name')->get();

        if ($clusters->isEmpty()) {
            $this->info('没有找到匹配的集群');

            return 0;
        }

        // 根据格式输出
        switch ($format) {
            case 'json':
                $this->outputJson($clusters);
                break;
            case 'table':
            default:
                $this->outputTable($clusters);
                break;
        }

        return 0;
    }

    /**
     * 以表格格式输出集群列表
     */
    private function outputTable($clusters)
    {
        $this->info("找到 {$clusters->count()} 个集群：");

        $headers = ['ID', '名称', '服务器地址', '认证类型', '工作空间数量', '创建时间', '更新时间'];
        $rows = [];

        foreach ($clusters as $cluster) {
            $workspaceCount = $cluster->workspaces()->count();
            $rows[] = [
                $cluster->id,
                $cluster->name,
                $cluster->server_url,
                $cluster->auth_type,
                $workspaceCount,
                $cluster->created_at->format('Y-m-d H:i:s'),
                $cluster->updated_at->format('Y-m-d H:i:s'),
            ];
        }

        $this->table($headers, $rows);

        // 显示统计信息
        $this->info("\n统计信息：");
        $authTypeStats = $clusters->groupBy('auth_type')->map->count();
        foreach ($authTypeStats as $authType => $count) {
            $this->info("  {$authType}: {$count} 个");
        }
    }

    /**
     * 以 JSON 格式输出集群列表
     */
    private function outputJson($clusters)
    {
        $data = $clusters->map(function ($cluster) {
            return [
                'id' => $cluster->id,
                'name' => $cluster->name,
                'server_url' => $cluster->server_url,
                'auth_type' => $cluster->auth_type,
                'workspace_count' => $cluster->workspaces()->count(),
                'created_at' => $cluster->created_at->toISOString(),
                'updated_at' => $cluster->updated_at->toISOString(),
            ];
        });

        $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
