<?php

namespace App\Console\Commands\User;

use App\Models\User;
use App\Service\BalanceService;
use App\Service\PaymentManagerService;
use Illuminate\Console\Command;

class AddUserBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:add-balance 
                            {user : 用户ID或邮箱} 
                            {amount : 充值金额} 
                            {--method=manual : 支付方式} 
                            {--remark= : 备注信息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '给用户添加余额';

    protected BalanceService $balanceService;

    protected PaymentManagerService $paymentManager;

    public function __construct(BalanceService $balanceService, PaymentManagerService $paymentManager)
    {
        parent::__construct();
        $this->balanceService = $balanceService;
        $this->paymentManager = $paymentManager;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userIdentifier = $this->argument('user');
        $amount = (float) $this->argument('amount');
        $paymentMethod = $this->option('method') ?: 'manual';
        $remark = $this->option('remark');

        // 验证金额
        if ($amount <= 0) {
            $this->error('充值金额必须大于0');

            return Command::FAILURE;
        }

        // 查找用户
        $user = $this->findUser($userIdentifier);
        if (! $user) {
            $this->error('用户不存在');

            return Command::FAILURE;
        }

        // 验证支付方式
        $availableGateways = $this->paymentManager->getAllGateways();
        $validMethods = array_keys($availableGateways);

        if (! in_array($paymentMethod, $validMethods)) {
            $this->error('无效的支付方式，支持：'.implode(', ', $validMethods));

            return Command::FAILURE;
        }

        // 检查支付方式是否启用
        $gateway = $this->paymentManager->getGateway($paymentMethod);
        if (! $gateway) {
            $this->error("支付方式 {$paymentMethod} 未启用");

            return Command::FAILURE;
        }

        // 显示确认信息
        $this->info('即将给用户充值:');
        $this->table(
            ['项目', '值'],
            [
                ['用户ID', $user->id],
                ['用户邮箱', $user->email],
                ['用户姓名', $user->name],
                ['当前余额', formatAmountForCli($user->current_balance)],
                ['充值金额', formatAmountForCli($amount)],
                ['充值后余额', formatAmountForCli($user->current_balance + $amount)],
                ['支付方式', $paymentMethod],
                ['备注', $remark ?: '无'],
            ]
        );

        if (! $this->confirm('确认执行充值操作？')) {
            $this->info('操作已取消');

            return Command::SUCCESS;
        }

        try {
            // 执行充值
            $record = $this->balanceService->addBalance(
                $user,
                $amount,
                $paymentMethod,
                null,
                ['remark' => $remark]
            );

            $this->info('充值成功！');
            $this->table(
                ['项目', '值'],
                [
                    ['流水号', $record->transaction_number],
                    ['充值金额', formatAmountForCli($record->amount)],
                    ['用户新余额', formatAmountForCli($user->fresh()->current_balance)],
                ]
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('充值失败：'.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 查找用户
     */
    private function findUser(string $identifier): ?User
    {
        // 如果是数字，按ID查找
        if (is_numeric($identifier)) {
            return User::find((int) $identifier);
        }

        // 否则按邮箱查找
        return User::where('email', $identifier)->first();
    }
}
