<?php

namespace App\Console\Commands\User;

use App\Models\User;
use App\Service\BalanceService;
use Illuminate\Console\Command;

class SyncUserBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:sync-balance 
                            {--user= : 指定用户ID或邮箱，留空同步所有用户} 
                            {--dry-run : 试运行，不实际修改数据}
                            {--force : 强制执行，不需要确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步用户余额，修复余额不一致问题';

    protected BalanceService $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        parent::__construct();
        $this->balanceService = $balanceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userIdentifier = $this->option('user');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if ($userIdentifier) {
            // 同步指定用户
            $user = $this->findUser($userIdentifier);
            if (! $user) {
                $this->error('用户不存在');

                return Command::FAILURE;
            }

            $this->syncSingleUser($user, $dryRun);
        } else {
            // 同步所有用户
            $this->syncAllUsers($dryRun, $force);
        }

        return Command::SUCCESS;
    }

    /**
     * 同步单个用户
     */
    private function syncSingleUser(User $user, bool $dryRun): void
    {
        $actualBalance = $this->balanceService->calculateActualBalance($user);
        $currentBalance = $user->current_balance;
        $difference = $actualBalance - $currentBalance;

        $this->info("用户: {$user->name} ({$user->email})");
        $this->table(
            ['项目', '值'],
            [
                ['当前余额', formatAmountForCli($currentBalance)],
                ['实际余额', formatAmountForCli($actualBalance)],
                ['差异', ($difference >= 0 ? '+' : '').formatAmountForCli($difference)],
                ['状态', $difference == 0 ? '一致' : '不一致'],
            ]
        );

        if ($difference == 0) {
            $this->info('余额一致，无需同步');

            return;
        }

        if ($dryRun) {
            $this->warn('试运行模式，不会实际修改数据');

            return;
        }

        if ($this->confirm('确认同步此用户的余额？')) {
            $updated = $this->balanceService->syncUserBalance($user);
            if ($updated) {
                $this->info('✅ 余额同步成功');
            } else {
                $this->warn('余额已经一致，无需更新');
            }
        } else {
            $this->info('已跳过');
        }
    }

    /**
     * 同步所有用户
     */
    private function syncAllUsers(bool $dryRun, bool $force): void
    {
        $this->info('正在查找需要同步的用户...');

        $inconsistentUsers = collect();
        $totalUsers = 0;

        User::chunk(100, function ($users) use (&$inconsistentUsers, &$totalUsers) {
            foreach ($users as $user) {
                $totalUsers++;
                $actualBalance = $this->balanceService->calculateActualBalance($user);

                if ($user->current_balance != $actualBalance) {
                    $inconsistentUsers->push([
                        'user' => $user,
                        'current_balance' => $user->current_balance,
                        'actual_balance' => $actualBalance,
                        'difference' => $actualBalance - $user->current_balance,
                    ]);
                }
            }
        });

        $this->info("扫描完成: 总用户数 {$totalUsers}，发现不一致用户 {$inconsistentUsers->count()}");

        if ($inconsistentUsers->isEmpty()) {
            $this->info('✅ 所有用户余额都是一致的');

            return;
        }

        // 显示不一致的用户
        $tableData = $inconsistentUsers->map(function ($item) {
            return [
                $item['user']->id,
                $item['user']->name,
                $item['user']->email,
                formatAmount($item['current_balance']),
                formatAmount($item['actual_balance']),
                ($item['difference'] >= 0 ? '+' : '').formatAmount($item['difference']),
            ];
        })->toArray();

        $this->table(
            ['用户ID', '姓名', '邮箱', '当前余额', '实际余额', '差异'],
            $tableData
        );

        if ($dryRun) {
            $this->warn('试运行模式，不会实际修改数据');

            return;
        }

        if (! $force && ! $this->confirm('确认同步所有不一致用户的余额？')) {
            $this->info('操作已取消');

            return;
        }

        // 执行同步
        $successCount = 0;
        $bar = $this->output->createProgressBar($inconsistentUsers->count());
        $bar->start();

        foreach ($inconsistentUsers as $item) {
            $updated = $this->balanceService->syncUserBalance($item['user']);
            if ($updated) {
                $successCount++;
            }
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("✅ 同步完成: {$successCount}/{$inconsistentUsers->count()} 个用户");
    }

    /**
     * 查找用户
     */
    private function findUser(string $identifier): ?User
    {
        // 如果是数字，按ID查找
        if (is_numeric($identifier)) {
            return User::find((int) $identifier);
        }

        // 否则按邮箱查找
        return User::where('email', $identifier)->first();
    }
}
