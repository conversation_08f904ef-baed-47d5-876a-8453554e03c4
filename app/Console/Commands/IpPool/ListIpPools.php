<?php

namespace App\Console\Commands\IpPool;

use App\Models\Cluster;
use App\Models\IpPool;
use Illuminate\Console\Command;

class ListIpPools extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ippool:list 
                            {cluster? : 集群 ID 或名称 (可选)}
                            {--details : 显示详细信息}
                            {--allocations : 显示端口分配情况}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '列出 IP 池及其分配情况';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $clusterArg = $this->argument('cluster');
            $showDetails = $this->option('details');
            $showAllocations = $this->option('allocations');

            // 如果指定了集群，只显示该集群的 IP 池
            if ($clusterArg) {
                $cluster = $this->getCluster($clusterArg);
                if (! $cluster) {
                    $this->error('找不到指定的集群');

                    return Command::FAILURE;
                }

                $this->info("集群 '{$cluster->name}' 的 IP 池列表：");
                $ipPools = IpPool::where('cluster_id', $cluster->id)->with(['cluster', 'poolIps'])->get();
            } else {
                $this->info('所有 IP 池列表：');
                $ipPools = IpPool::with(['cluster', 'poolIps'])->get();
            }

            if ($ipPools->isEmpty()) {
                $this->warn('没有找到任何 IP 池');

                return Command::SUCCESS;
            }

            // 基本列表
            $headers = ['ID', '名称', '集群', '策略', 'IP版本', 'IP数量', '总使用数', 'K8s同步', '状态'];
            $rows = [];

            foreach ($ipPools as $pool) {
                $stats = $pool->stats;
                $rows[] = [
                    $pool->id,
                    $pool->name,
                    $pool->cluster->name,
                    $this->translateStrategy($pool->allocation_strategy),
                    $this->translateIpVersion($pool->ip_version),
                    $stats['total_ips'],
                    $stats['total_usage'],
                    $pool->synced_to_k8s ? '✅' : '❌',
                    $pool->is_active ? '✅ 启用' : '❌ 禁用',
                ];
            }

            $this->table($headers, $rows);

            // 详细信息
            if ($showDetails) {
                $this->newLine();
                $this->info('📊 详细统计信息：');

                foreach ($ipPools as $pool) {
                    $this->showPoolDetails($pool);
                }
            }

            // 端口分配情况
            if ($showAllocations) {
                $this->newLine();
                $this->info('🔌 端口分配情况：');

                foreach ($ipPools as $pool) {
                    $this->showPoolAllocations($pool);
                }
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('查询 IP 池失败: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 显示 IP 池详细信息
     */
    private function showPoolDetails(IpPool $pool): void
    {
        $stats = $pool->stats;

        $this->newLine();
        $this->info("🏊 IP 池: {$pool->name} (ID: {$pool->id})");

        $this->table(
            ['属性', '值'],
            [
                ['集群', $pool->cluster->name],
                ['描述', $pool->description ?? '无'],
                ['分配策略', $this->translateStrategy($pool->allocation_strategy)],
                ['IP 版本', $this->translateIpVersion($pool->ip_version)],
                ['IPv4 子网', $pool->subnet_v4 ?? '无'],
                ['IPv6 子网', $pool->subnet_v6 ?? '无'],
                ['IPv4 网关', $pool->gateway_v4 ?? '无'],
                ['IPv6 网关', $pool->gateway_v6 ?? '无'],
                ['ServiceGroup', $pool->service_group_name],
                ['ServiceGroup 命名空间', $pool->service_group_namespace],
                ['聚合策略', $pool->aggregation],
                ['总 IP 数', $stats['total_ips']],
                ['活跃 IP 数', $stats['active_ips']],
                ['停用 IP 数', $stats['inactive_ips']],
                ['总使用次数', $stats['total_usage']],
                ['平均使用次数', $stats['average_usage']],
                ['K8s 同步状态', $pool->synced_to_k8s ? '已同步' : '未同步'],
                ['最后同步时间', $pool->last_sync_at ? $pool->last_sync_at->format('Y-m-d H:i:s') : '从未同步'],
                ['状态', $pool->is_active ? '启用' : '禁用'],
                ['创建时间', $pool->created_at->format('Y-m-d H:i:s')],
            ]
        );

        // 显示前 10 个 IP 的使用情况
        $topIps = $pool->poolIps()
            ->orderBy('usage_count', 'desc')
            ->limit(10)
            ->get();

        if ($topIps->isNotEmpty()) {
            $this->info('使用最多的 IP (前10):');
            $ipHeaders = ['IP地址', '端口范围', '使用次数', '端口利用率', '状态'];
            $ipRows = [];

            foreach ($topIps as $poolIp) {
                $ipRows[] = [
                    $poolIp->ip_address,
                    "{$poolIp->port_range_start}-{$poolIp->port_range_end}",
                    $poolIp->usage_count,
                    "{$poolIp->port_utilization}%",
                    $poolIp->is_active ? '✅' : '❌',
                ];
            }

            $this->table($ipHeaders, $ipRows);
        }
    }

    /**
     * 显示端口分配情况
     */
    private function showPoolAllocations(IpPool $pool): void
    {
        $this->newLine();
        $this->info("🔌 IP 池 '{$pool->name}' 的端口分配:");

        $allocations = \App\Models\PortAllocation::whereHas('poolIp', function ($query) use ($pool) {
            $query->where('ip_pool_id', $pool->id);
        })->with('poolIp')
            ->where('status', 'allocated')
            ->orderBy('allocated_at', 'desc')
            ->limit(20)
            ->get();

        if ($allocations->isEmpty()) {
            $this->warn('没有活跃的端口分配');

            return;
        }

        $headers = ['IP地址', '端口', 'Service', 'Namespace', '分配时间'];
        $rows = [];

        foreach ($allocations as $allocation) {
            $rows[] = [
                $allocation->poolIp->ip_address,
                $allocation->port,
                $allocation->service_name,
                $allocation->namespace,
                $allocation->allocated_at ? $allocation->allocated_at->format('Y-m-d H:i:s') : '未知',
            ];
        }

        $this->table($headers, $rows);

        $totalAllocated = \App\Models\PortAllocation::whereHas('poolIp', function ($query) use ($pool) {
            $query->where('ip_pool_id', $pool->id);
        })->where('status', 'allocated')->count();

        $this->info("总共分配端口数: {$totalAllocated}");
    }

    /**
     * 获取集群
     */
    private function getCluster(string $identifier): ?Cluster
    {
        if (is_numeric($identifier)) {
            $cluster = Cluster::find($identifier);
            if ($cluster) {
                return $cluster;
            }
        }

        return Cluster::where('name', $identifier)->first();
    }

    /**
     * 翻译策略名称
     */
    private function translateStrategy(string $strategy): string
    {
        return match ($strategy) {
            'least_used' => '最少使用',
            'round_robin' => '轮询分配',
            'random' => '随机分配',
            default => $strategy,
        };
    }

    /**
     * 翻译 IP 版本名称
     */
    private function translateIpVersion(string $version): string
    {
        return match ($version) {
            'ipv4' => 'IPv4',
            'ipv6' => 'IPv6',
            'dual' => '双栈',
            default => $version,
        };
    }
}
