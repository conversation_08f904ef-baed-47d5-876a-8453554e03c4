<?php

namespace App\Console\Commands\IpPool;

use App\Models\IpPool;
use App\Service\IpPoolService;
use Illuminate\Console\Command;

class DeleteIpPool extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ippool:delete 
                            {pool : IP 池 ID 或名称}
                            {--force : 强制删除，即使有活跃分配}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除 IP 池并从 K8s 集群中清理相关资源';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $poolIdentifier = $this->argument('pool');
            $force = $this->option('force');

            // 查找 IP 池
            $ipPool = $this->findIpPool($poolIdentifier);
            if (! $ipPool) {
                $this->error('找不到指定的 IP 池');

                return Command::FAILURE;
            }

            $this->info("找到 IP 池: {$ipPool->name} (集群: {$ipPool->cluster->name})");

            // 检查是否有活跃的分配
            $activeAllocations = \App\Models\PortAllocation::whereHas('poolIp', function ($query) use ($ipPool) {
                $query->where('ip_pool_id', $ipPool->id);
            })->where('status', 'allocated')->count();

            if ($activeAllocations > 0 && ! $force) {
                $this->error("IP 池中有 {$activeAllocations} 个活跃的端口分配，无法删除");
                $this->info('使用 --force 选项强制删除');

                return Command::FAILURE;
            }

            if ($activeAllocations > 0) {
                $this->warn("强制删除模式：将清理 {$activeAllocations} 个活跃分配");
            }

            // 显示删除信息
            $stats = $ipPool->stats;
            $this->table(
                ['属性', '值'],
                [
                    ['名称', $ipPool->name],
                    ['集群', $ipPool->cluster->name],
                    ['总 IP 数', $stats['total_ips']],
                    ['总使用次数', $stats['total_usage']],
                    ['活跃分配', $activeAllocations],
                ]
            );

            // 确认删除
            if (! $this->confirm('确定要删除这个 IP 池吗？此操作不可逆转！')) {
                $this->info('操作已取消');

                return Command::SUCCESS;
            }

            // 从 K8s 集群中删除相关资源
            $this->info('正在从 K8s 集群中清理相关资源...');

            try {
                $ipPoolService = app(IpPoolService::class);
                $ipPoolService->deleteFromKubernetes($ipPool);
                $this->info('✅ 已从 K8s 集群中清理相关资源');
            } catch (\Exception $e) {
                $this->warn('⚠️  从 K8s 清理失败: '.$e->getMessage());
                $this->warn('将继续删除数据库中的记录');
            }

            // 删除数据库记录
            $this->info('正在删除数据库记录...');

            // 如果是强制删除，先释放所有端口分配
            if ($force && $activeAllocations > 0) {
                \App\Models\PortAllocation::whereHas('poolIp', function ($query) use ($ipPool) {
                    $query->where('ip_pool_id', $ipPool->id);
                })->where('status', 'allocated')->update([
                    'status' => 'released',
                    'released_at' => now(),
                ]);
                $this->info("已释放 {$activeAllocations} 个端口分配");
            }

            // 删除 IP 池（级联删除相关记录）
            $ipPool->delete();

            $this->info('🎉 IP 池删除完成！');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('删除 IP 池失败: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 查找 IP 池
     */
    private function findIpPool(string $identifier): ?IpPool
    {
        // 首先尝试按 ID 查找
        if (is_numeric($identifier)) {
            $pool = IpPool::with('cluster')->find($identifier);
            if ($pool) {
                return $pool;
            }
        }

        // 然后按名称查找
        return IpPool::with('cluster')->where('name', $identifier)->first();
    }
}
