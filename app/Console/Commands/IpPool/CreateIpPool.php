<?php

namespace App\Console\Commands\IpPool;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Service\IpPoolService;
use Illuminate\Console\Command;
use PhpIP\IP;
use PhpIP\IPBlock;

class CreateIpPool extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ippool:create
                            {range : IP 范围 (例如: ***********-************ 或 ***********/24)}
                            {--name= : IP 池名称 (必需)}
                            {--cluster-id= : 集群 ID (必需)}
                            {--desc= : IP 池描述}
                            {--ip-version=ipv4 : IP 版本 (ipv4, ipv6, dual)}
                            {--subnet-v4= : IPv4 子网 CIDR (IPv4 池必需, 例如: ***********/24)}
                            {--gateway-v4= : IPv4 网关}
                            {--subnet-v6= : IPv6 子网 CIDR (IPv6 池必需, 例如: fd53:9ef0:8683::/120)}
                            {--gateway-v6= : IPv6 网关}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建 IP 池并同步到 K8s 集群';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            // 验证集群
            $cluster = $this->getCluster($this->option('cluster-id'));
            if (! $cluster) {
                $this->error('找不到指定的集群');

                return Command::FAILURE;
            }

            $this->info("正在为集群 '{$cluster->name}' 创建 IP 池...");

            // 验证参数
            $name = $this->option('name');
            $ipVersion = $this->option('ip-version');
            $subnetV4 = $this->option('subnet-v4');
            $gatewayV4 = $this->option('gateway-v4');
            $subnetV6 = $this->option('subnet-v6');
            $gatewayV6 = $this->option('gateway-v6');
            $description = $this->option('desc') ?? "IP 池 {$name}";

            // 验证策略
            if (! in_array($ipVersion, ['ipv4', 'ipv6', 'dual'])) {
                $this->error('无效的 IP 版本');

                return Command::FAILURE;
            }

            // 验证子网配置
            if ($ipVersion === 'ipv4' && ! $subnetV4) {
                $this->error('IPv4 IP 池必须提供 --subnet-v4 参数');

                return Command::FAILURE;
            }
            if ($ipVersion === 'ipv6' && ! $subnetV6) {
                $this->error('IPv6 IP 池必须提供 --subnet-v6 参数');

                return Command::FAILURE;
            }
            if ($ipVersion === 'dual' && (! $subnetV4 || ! $subnetV6)) {
                $this->error('双栈 IP 池必须同时提供 --subnet-v4 和 --subnet-v6 参数');

                return Command::FAILURE;
            }

            // 检查 IP 池名称是否已存在
            if (IpPool::where('cluster_id', $cluster->id)->where('name', $name)->exists()) {
                $this->error("IP 池名称 '{$name}' 在此集群中已存在");

                return Command::FAILURE;
            }

            // 解析 IP 范围
            $ips = $this->parseIpRange($this->argument('range'));
            if (empty($ips)) {
                $this->error('无效的 IP 范围格式');

                return Command::FAILURE;
            }

            // 检测 IP 版本
            $ipVersionResult = $this->detectIpVersion($ips);
            $this->info('解析到 '.count($ips)." 个 IP 地址 (版本: {$ipVersionResult})");

            // 验证子网配置
            if ($ipVersionResult === IpPool::IP_VERSION_IPV4 && ! $subnetV4) {
                $this->error('IPv4 IP 池必须提供 --subnet-v4 参数');

                return Command::FAILURE;
            }
            if ($ipVersionResult === IpPool::IP_VERSION_IPV6 && ! $subnetV6) {
                $this->error('IPv6 IP 池必须提供 --subnet-v6 参数');

                return Command::FAILURE;
            }
            if ($ipVersionResult === IpPool::IP_VERSION_DUAL && (! $subnetV4 || ! $subnetV6)) {
                $this->error('双栈 IP 池必须同时提供 --subnet-v4 和 --subnet-v6 参数');

                return Command::FAILURE;
            }

            // 创建 IP 池
            $ipPool = IpPool::create([
                'cluster_id' => $cluster->id,
                'name' => $name,
                'description' => $description,
                'ip_version' => $ipVersionResult,
                'subnet_v4' => $subnetV4,
                'gateway_v4' => $gatewayV4,
                'subnet_v6' => $subnetV6,
                'gateway_v6' => $gatewayV6,
            ]);

            $this->info("IP 池 '{$name}' 创建成功");

            // 创建 IP 地址
            $progressBar = $this->output->createProgressBar(count($ips));
            $progressBar->start();

            foreach ($ips as $ip) {
                PoolIp::create([
                    'ip_pool_id' => $ipPool->id,
                    'ip_address' => $ip,
                    'usage_count' => 0,
                    'is_active' => true,
                ]);
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            // 同步到 K8s 集群
            $this->info('正在同步到 K8s 集群...');

            try {
                $ipPoolService = app(IpPoolService::class);
                $ipPoolService->syncToKubernetes($ipPool);
                $this->info('✅ 已成功同步到 K8s 集群');
            } catch (\Exception $e) {
                $this->error('⚠️  同步到 K8s 失败: '.$e->getMessage());
                $this->warn('由于同步失败，将回滚并删除数据库中的 IP 池...');
                $ipPool->delete();
                $this->info('✅ IP 池已从数据库中删除。');
                throw $e; // Re-throw the exception to fail the command
            }

            $this->newLine();
            $this->info('🎉 IP 池创建完成！');
            $this->table(
                ['属性', '值'],
                [
                    ['集群', $cluster->name],
                    ['名称', $ipPool->name],
                    ['IP 版本', $ipPool->ip_version],
                    ['IP 数量', count($ips)],
                    ['状态', $ipPool->is_active ? '启用' : '禁用'],
                ]
            );

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('创建 IP 池失败: '.$e->getMessage());

            return Command::FAILURE;
        }
    }

    /**
     * 获取集群
     */
    private function getCluster(string $identifier): ?Cluster
    {
        // 首先尝试按 ID 查找
        if (is_numeric($identifier)) {
            $cluster = Cluster::find($identifier);
            if ($cluster) {
                return $cluster;
            }
        }

        // 然后按名称查找
        return Cluster::where('name', $identifier)->first();
    }

    /**
     * 解析 IP 范围
     */
    private function parseIpRange(string $range): array
    {
        $ips = [];

        try {
            // 检查是否是逗号分隔的多个 IP
            if (str_contains($range, ',')) {
                $ipList = explode(',', $range);
                foreach ($ipList as $singleIp) {
                    $singleIp = trim($singleIp);
                    $parsed = $this->parseSingleIpOrRange($singleIp);
                    $ips = array_merge($ips, $parsed);
                }
            } else {
                $ips = $this->parseSingleIpOrRange($range);
            }
        } catch (\Exception $e) {
            $this->error('解析 IP 范围失败: '.$e->getMessage());

            return [];
        }

        return array_unique($ips);
    }

    /**
     * 解析单个 IP 或 IP 范围
     */
    private function parseSingleIpOrRange(string $range): array
    {
        $ips = [];

        if (str_contains($range, '-')) {
            // 范围格式: ***********-************
            [$start, $end] = explode('-', $range, 2);
            $start = trim($start);
            $end = trim($end);

            $startIp = IP::create($start);
            $endIp = IP::create($end);

            if ($startIp->getVersion() !== $endIp->getVersion()) {
                throw new \Exception('起始和结束 IP 必须是同一版本');
            }

            // 对于 IPv4，生成范围内的所有 IP
            if ($startIp->getVersion() === 4) {
                $startLong = ip2long($start);
                $endLong = ip2long($end);

                if ($startLong > $endLong) {
                    throw new \Exception('起始 IP 不能大于结束 IP');
                }

                if ($endLong - $startLong > 1000) {
                    throw new \Exception('IP 范围不能超过 1000 个地址');
                }

                for ($i = $startLong; $i <= $endLong; $i++) {
                    $ips[] = long2ip($i);
                }
            } else {
                // 对于 IPv6，只添加起始和结束地址
                $ips[] = $start;
                if ($start !== $end) {
                    $ips[] = $end;
                }
            }
        } elseif (str_contains($range, '/')) {
            // CIDR 格式: ***********/24
            $block = IPBlock::create($range);

            if ($block->getVersion() === 4) {
                $iterator = $block->getIterator();
                $count = 0;
                foreach ($iterator as $ip) {
                    if ($count >= 1000) {
                        break; // 限制最大数量
                    }
                    $ips[] = $ip->humanReadable();
                    $count++;
                }
            } else {
                // IPv6 CIDR，只添加网络地址
                $ips[] = $block->getFirstIP()->humanReadable();
            }
        } else {
            // 单个 IP
            $ip = IP::create($range);
            $ips[] = $ip->humanReadable();
        }

        return $ips;
    }

    /**
     * 检测 IP 版本
     */
    private function detectIpVersion(array $ips): string
    {
        $hasIpv4 = false;
        $hasIpv6 = false;

        foreach ($ips as $ip) {
            try {
                $ipObj = IP::create($ip);
                if ($ipObj->getVersion() === 4) {
                    $hasIpv4 = true;
                } elseif ($ipObj->getVersion() === 6) {
                    $hasIpv6 = true;
                }
            } catch (\Exception $e) {
                // 忽略无效 IP
                continue;
            }
        }

        if ($hasIpv4 && $hasIpv6) {
            return IpPool::IP_VERSION_DUAL;
        } elseif ($hasIpv6) {
            return IpPool::IP_VERSION_IPV6;
        } else {
            return IpPool::IP_VERSION_IPV4;
        }
    }
}
