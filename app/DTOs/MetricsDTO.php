<?php

namespace App\DTOs;

/**
 * Metrics DTO
 */
class MetricsDTO
{
    public function __construct(
        public ?string $timestamp,
        public ?string $window,
        public array $containers
    ) {}

    /**
     * 从 Kubernetes Metrics 资源创建 DTO
     * 并统一单位：CPU 转为毫核(m)，内存转为 Mi
     */
    public static function fromK8sResource(array $metrics): static
    {
        return new static(
            timestamp: $metrics['timestamp'] ?? null,
            window: $metrics['window'] ?? null,
            containers: array_map(function ($container) {
                $cpuRaw = $container['usage']['cpu'] ?? '0';
                $memoryRaw = $container['usage']['memory'] ?? '0';

                // 统一 CPU 单位为毫核(m)
                $cpuInMillicores = self::normalizeCpuToMillicores($cpuRaw);

                // 统一内存单位为 Mi
                $memoryInMi = self::normalizeMemoryToMi($memoryRaw);

                return [
                    'name' => $container['name'] ?? '',
                    'usage' => [
                        'cpu' => $cpuInMillicores,
                        'memory' => $memoryInMi,
                    ],
                    'raw' => [
                        'cpu' => $cpuRaw,
                        'memory' => $memoryRaw,
                    ],
                ];
            }, $metrics['containers'] ?? [])
        );
    }

    /**
     * 将 CPU 值统一转换为毫核(m)
     */
    private static function normalizeCpuToMillicores(string $cpuValue): string
    {
        // 处理空值或0
        if (empty($cpuValue) || $cpuValue === '0') {
            return '0m';
        }

        // 已经是毫核格式
        if (str_ends_with($cpuValue, 'm')) {
            return $cpuValue;
        }

        // 处理纳核格式 (n)
        if (str_ends_with($cpuValue, 'n')) {
            $nanoCores = (float) str_replace('n', '', $cpuValue);
            $milliCores = $nanoCores / 1000000; // 1m = 1000000n

            return round($milliCores, 2).'m';
        }

        // 处理核格式 (无单位，表示核心数)
        $cores = (float) $cpuValue;

        return ($cores * 1000).'m';
    }

    /**
     * 将内存值统一转换为 Mi
     */
    private static function normalizeMemoryToMi(string $memoryValue): string
    {
        // 处理空值或0
        if (empty($memoryValue) || $memoryValue === '0') {
            return '0M';
        }

        // 已经是 Mi 格式
        if (str_ends_with($memoryValue, 'Mi')) {
            return $memoryValue;
        }

        // 处理 Ki 格式
        if (str_ends_with($memoryValue, 'Ki')) {
            $kilobytes = (float) str_replace('Ki', '', $memoryValue);
            $megabytes = $kilobytes / 1024;

            return round($megabytes, 2).'M';
        }

        // 处理 Gi 格式
        if (str_ends_with($memoryValue, 'Gi')) {
            $gigabytes = (float) str_replace('Gi', '', $memoryValue);
            $megabytes = $gigabytes * 1024;

            return round($megabytes, 2).'M';
        }

        // 处理 bytes 格式 (无单位)
        $bytes = (float) $memoryValue;
        $megabytes = $bytes / (1024 * 1024);

        return round($megabytes, 2).'M';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'timestamp' => $this->timestamp,
            'window' => $this->window,
            'containers' => $this->containers,
        ];
    }

    /**
     * 获取 CPU 总使用量（毫核）
     * 由于已经统一单位为毫核(m)，直接计算总和
     */
    public function getTotalCpuUsage(): string
    {
        $totalCpu = 0;

        foreach ($this->containers as $container) {
            $cpuStr = $container['usage']['cpu'];
            $totalCpu += (float) str_replace('m', '', $cpuStr);
        }

        return round($totalCpu, 2).'m';
    }

    /**
     * 获取内存总使用量（Mi 或 Gi）
     * 由于已经统一单位为 Mi，直接计算总和
     */
    public function getTotalMemoryUsage(): string
    {
        $totalMemoryMi = 0;

        foreach ($this->containers as $container) {
            $memStr = $container['usage']['memory'];
            $totalMemoryMi += (float) str_replace('Mi', '', $memStr);
        }

        if ($totalMemoryMi >= 1024) {
            return round($totalMemoryMi / 1024, 2).'Gi';
        }

        return round($totalMemoryMi, 2).'M';
    }
}
