<?php

namespace App\DTOs;

/**
 * Namespace Metrics DTO
 */
class NamespaceMetricsDTO
{
    /**
     * @var array<string, MetricsDTO> Pod名称到指标的映射
     */
    protected array $podMetrics = [];

    public function __construct(array $podMetrics = [])
    {
        $this->podMetrics = $podMetrics;
    }

    /**
     * 从 Kubernetes 命名空间指标响应创建 DTO
     */
    public static function fromK8sResource(array $items): static
    {
        $podMetrics = [];

        foreach ($items as $item) {
            $podName = $item['metadata']['name'] ?? '';
            if (! empty($podName)) {
                $podMetrics[$podName] = MetricsDTO::fromK8sResource([
                    'timestamp' => $item['timestamp'] ?? null,
                    'window' => $item['window'] ?? null,
                    'containers' => $item['containers'] ?? [],
                ]);
            }
        }

        return new static($podMetrics);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        $result = [];

        foreach ($this->podMetrics as $podName => $metrics) {
            $result[$podName] = $metrics->toArray();
        }

        return $result;
    }

    /**
     * 获取特定 Pod 的指标
     */
    public function getPodMetrics(string $podName): ?MetricsDTO
    {
        return $this->podMetrics[$podName] ?? null;
    }

    /**
     * 获取所有 Pod 指标
     */
    public function getAllPodMetrics(): array
    {
        return $this->podMetrics;
    }

    /**
     * 获取 Pod 数量
     */
    public function getPodCount(): int
    {
        return count($this->podMetrics);
    }
}
