<?php

namespace App\DTOs;

/**
 * Namespace Events DTO
 */
class NamespaceEventsDTO
{
    /**
     * @var EventDTO[] 事件列表
     */
    protected array $events = [];

    public function __construct(array $events = [])
    {
        $this->events = $events;
    }

    /**
     * 从 Kubernetes 事件列表创建 DTO
     */
    public static function fromK8sResource(array $items): static
    {
        $events = [];

        foreach ($items as $item) {
            $events[] = EventDTO::fromK8sResource($item);
        }

        return new static($events);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_map(function (EventDTO $event) {
            return $event->toArray();
        }, $this->events);
    }

    /**
     * 获取所有事件
     */
    public function getAllEvents(): array
    {
        return $this->events;
    }

    /**
     * 获取警告事件
     */
    public function getWarningEvents(): array
    {
        return array_filter($this->events, function (EventDTO $event) {
            return $event->isWarning();
        });
    }

    /**
     * 获取特定资源的事件
     */
    public function getResourceEvents(string $kind, string $name): array
    {
        return array_filter($this->events, function (EventDTO $event) use ($kind, $name) {
            return $event->involvesResource($kind, $name);
        });
    }

    /**
     * 获取事件数量
     */
    public function getEventCount(): int
    {
        return count($this->events);
    }

    /**
     * 获取警告事件数量
     */
    public function getWarningEventCount(): int
    {
        return count($this->getWarningEvents());
    }
}
