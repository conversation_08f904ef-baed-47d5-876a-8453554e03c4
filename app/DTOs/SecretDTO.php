<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Secret DTO
 */
class SecretDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public string $type = 'Opaque',
        public array $dataKeys = [], // 只存储键名，不存储值
        public int $dataCount = 0
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes Secret 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $data = $resource['data'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
            ? Carbon::parse($metadata['creationTimestamp'])
            : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            type: static::formatType($resource['type']),
            dataKeys: array_keys($data),
            dataCount: count($data)
        );
    }

    /**
     * 获取 Secret 类型选项
     */
    public static function getTypeOptions(): array
    {
        return [
            'Opaque' => '通用 Secret',
            'kubernetes.io/dockerconfigjson' => 'Docker 配置',
            'kubernetes.io/tls' => 'TLS 证书',
            'kubernetes.io/basic-auth' => '基础认证',
            'kubernetes.io/ssh-auth' => 'SSH 认证',
            'kubernetes.io/service-account-token' => '服务账户令牌',
        ];
    }

    /**
     * 格式化 K8s Secret 类型
     */
    public static function formatType(string $type): string
    {
        return match ($type) {
            'Opaque' => 'Opaque',
            'kubernetes.io/dockerconfigjson' => 'DockerConfig',
            'kubernetes.io/tls' => 'TLS',
            'kubernetes.io/basic-auth' => 'BasicAuth',
            'kubernetes.io/ssh-auth' => 'SSH',
            'kubernetes.io/service-account-token' => 'ServiceAccountToken',
            default => $type,
        };
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->type,
            'data_keys' => $this->dataKeys,
            'data_count' => $this->dataCount,
        ]);
    }
}
