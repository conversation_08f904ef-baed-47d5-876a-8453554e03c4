<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Service DTO
 */
class ServiceDTO extends KubernetesResourceDTO
{
    public readonly string $type;

    public readonly ?string $clusterIp;

    public readonly ?array $externalIps;

    public readonly ?string $loadBalancerIp;

    public readonly array $ports;

    public readonly array $selector;

    public readonly ?string $sessionAffinity;

    public readonly ?string $externalTrafficPolicy;

    public readonly ?string $internalTrafficPolicy;

    public readonly string $status;

    public readonly ?string $workloadType;

    public readonly ?string $workloadName;

    public function __construct(
        string $name,
        string $namespace,
        string $type,
        ?string $clusterIp,
        ?array $externalIps,
        ?string $loadBalancerIp,
        array $ports,
        array $selector,
        ?string $sessionAffinity,
        ?string $externalTrafficPolicy,
        ?string $internalTrafficPolicy,
        string $status,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        ?string $workloadType = null,
        ?string $workloadName = null
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);

        $this->type = $type;
        $this->clusterIp = $clusterIp;
        $this->externalIps = $externalIps;
        $this->loadBalancerIp = $loadBalancerIp;
        $this->ports = $ports;
        $this->selector = $selector;
        $this->sessionAffinity = $sessionAffinity;
        $this->externalTrafficPolicy = $externalTrafficPolicy;
        $this->internalTrafficPolicy = $internalTrafficPolicy;
        $this->status = $status;
        $this->workloadType = $workloadType;
        $this->workloadName = $workloadName;
    }

    /**
     * 从 Kubernetes Service 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];
        $metadata = $resource['metadata'] ?? [];

        // 处理端口信息
        $ports = array_map(function ($port) {
            return [
                'name' => $port['name'] ?? null,
                'port' => $port['port'] ?? null,
                'targetPort' => $port['targetPort'] ?? null,
                'protocol' => $port['protocol'] ?? 'TCP',
                'nodePort' => $port['nodePort'] ?? null,
            ];
        }, $spec['ports'] ?? []);

        // 处理负载均衡器状态
        $loadBalancerIp = null;
        if (isset($status['loadBalancer']['ingress'][0]['ip'])) {
            $loadBalancerIp = $status['loadBalancer']['ingress'][0]['ip'];
        }

        // 确定服务状态
        $serviceStatus = 'Active';
        if ($spec['type'] === 'LoadBalancer' && ! $loadBalancerIp) {
            $serviceStatus = 'Pending';
        }

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            type: $spec['type'] ?? 'ClusterIP',
            clusterIp: $spec['clusterIP'] ?? null,
            externalIps: $spec['externalIPs'] ?? null,
            loadBalancerIp: $loadBalancerIp,
            ports: $ports,
            selector: $spec['selector'] ?? [],
            sessionAffinity: $spec['sessionAffinity'] ?? 'None',
            externalTrafficPolicy: $spec['externalTrafficPolicy'] ?? 'Cluster',
            internalTrafficPolicy: $spec['internalTrafficPolicy'] ?? 'Cluster',
            status: $serviceStatus,
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null
        );
    }

    /**
     * 获取外部访问地址
     */
    public function getExternalAddresses(): array
    {
        $addresses = [];

        if ($this->type === 'LoadBalancer' && $this->loadBalancerIp) {
            $addresses[] = $this->loadBalancerIp;
        }

        if (! empty($this->externalIps)) {
            $addresses = array_merge($addresses, $this->externalIps);
        }

        return $addresses;
    }

    /**
     * 检查是否有外部访问
     */
    public function hasExternalAccess(): bool
    {
        return $this->type === 'LoadBalancer' ||
               $this->type === 'NodePort' ||
               ! empty($this->externalIps);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'type' => $this->type,
            'clusterIp' => $this->clusterIp,
            'externalIps' => $this->externalIps,
            'loadBalancerIp' => $this->loadBalancerIp,
            'ports' => $this->ports,
            // 'selector' => $this->selector,
            'sessionAffinity' => $this->sessionAffinity,
            'externalTrafficPolicy' => $this->externalTrafficPolicy,
            'status' => $this->status,
            'external_addresses' => $this->getExternalAddresses(),
            'has_external_access' => $this->hasExternalAccess(),
            'workload_type' => $this->workloadType,
            'workload_name' => $this->workloadName,
        ]);
    }
}
