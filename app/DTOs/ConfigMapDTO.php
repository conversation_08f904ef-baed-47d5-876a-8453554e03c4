<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * ConfigMap DTO
 */
class ConfigMapDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public array $data = [],
        public array $binaryData = []
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes ConfigMap 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            data: $resource['data'] ?? [],
            binaryData: $resource['binaryData'] ?? []
        );
    }

    /**
     * 获取数据键列表
     */
    public function getDataKeys(): array
    {
        return array_keys($this->data);
    }

    /**
     * 获取二进制数据键列表
     */
    public function getBinaryDataKeys(): array
    {
        return array_keys($this->binaryData);
    }

    /**
     * 获取总数据项数量
     */
    public function getDataCount(): int
    {
        return count($this->data) + count($this->binaryData);
    }

    /**
     * 检查是否包含指定键
     */
    public function hasKey(string $key): bool
    {
        return isset($this->data[$key]) || isset($this->binaryData[$key]);
    }

    /**
     * 获取指定键的值
     */
    public function getValue(string $key): ?string
    {
        if (isset($this->data[$key])) {
            return $this->data[$key];
        }

        if (isset($this->binaryData[$key])) {
            return base64_decode($this->binaryData[$key]);
        }

        return null;
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'data' => $this->data,
            'binary_data' => $this->binaryData,
            'data_keys' => $this->getDataKeys(),
            'binary_data_keys' => $this->getBinaryDataKeys(),
            'data_count' => $this->getDataCount(),
        ]);
    }
}
