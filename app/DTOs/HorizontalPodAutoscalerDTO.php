<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * HorizontalPodAutoscaler DTO
 */
class HorizontalPodAutoscalerDTO extends KubernetesResourceDTO
{
    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public array $scaleTargetRef = [],
        public int $minReplicas = 1,
        public int $maxReplicas = 10,
        public array $metrics = [],
        public array $behavior = [],
        public ?int $currentReplicas = null,
        public ?int $desiredReplicas = null,
        public array $currentMetrics = [],
        public ?Carbon $lastScaleTime = null,
        public array $conditions = []
    ) {
        parent::__construct(
            $name,
            $namespace,
            $labels,
            $annotations,
            $createdAt,
            $uid,
            $resourceVersion
        );
    }

    /**
     * 从 Kubernetes API 响应创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            scaleTargetRef: $spec['scaleTargetRef'] ?? [],
            minReplicas: $spec['minReplicas'] ?? 1,
            maxReplicas: $spec['maxReplicas'] ?? 10,
            metrics: $spec['metrics'] ?? [],
            behavior: $spec['behavior'] ?? [],
            currentReplicas: $status['currentReplicas'] ?? null,
            desiredReplicas: $status['desiredReplicas'] ?? null,
            currentMetrics: $status['currentMetrics'] ?? [],
            lastScaleTime: isset($status['lastScaleTime'])
                ? Carbon::parse($status['lastScaleTime'])
                : null,
            conditions: $status['conditions'] ?? []
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'scale_target_ref' => $this->scaleTargetRef,
            'min_replicas' => $this->minReplicas,
            'max_replicas' => $this->maxReplicas,
            'metrics' => $this->processMetrics($this->metrics),
            'behavior' => $this->behavior,
            'current_replicas' => $this->currentReplicas,
            'desired_replicas' => $this->desiredReplicas,
            'current_metrics' => $this->currentMetrics,
            'last_scale_time' => $this->lastScaleTime?->toISOString(),
            'conditions' => $this->conditions,
            'status' => $this->getStatus(),
        ]);
    }

    /**
     * 处理指标数据，去掉单位
     */
    private function processMetrics(array $metrics): array
    {
        return array_map(function ($metric) {
            if ($metric['type'] === 'Resource' && isset($metric['resource']['target']['averageValue'])) {
                $averageValue = $metric['resource']['target']['averageValue'];
                $resourceName = $metric['resource']['name'] ?? '';

                // 去掉单位，只保留数字
                if (is_string($averageValue)) {
                    // 处理内存单位 (Mi, Gi, Ki)
                    if (preg_match('/^(\d+)(Mi|Gi|Ki|M|G|K)$/', $averageValue, $matches)) {
                        $value = (int) $matches[1];
                        $unit = $matches[2];

                        // 统一转换为 Mi 单位的数值
                        switch ($unit) {
                            case 'Gi':
                            case 'G':
                                $metric['resource']['target']['averageValue'] = $value * 1024;
                                break;
                            case 'Ki':
                            case 'K':
                                $metric['resource']['target']['averageValue'] = (int) ($value / 1024);
                                break;
                            case 'Mi':
                            case 'M':
                            default:
                                $metric['resource']['target']['averageValue'] = $value;
                                break;
                        }
                    }
                    // 处理 CPU 单位 (m)
                    elseif (preg_match('/^(\d+)m$/', $averageValue, $matches)) {
                        $metric['resource']['target']['averageValue'] = (int) $matches[1];
                    }
                    // 处理纯数字的 CPU 值（Kubernetes 标准化后）
                    elseif (preg_match('/^(\d+)$/', $averageValue, $matches)) {
                        $value = (int) $matches[1];
                        // 如果是 CPU 资源且值看起来是标准化的核心数，转换为 millicore
                        if ($resourceName === 'cpu' && $value <= 10) { // 假设 <= 10 是核心数而不是 millicore
                            $metric['resource']['target']['averageValue'] = $value * 1000;
                        } else {
                            $metric['resource']['target']['averageValue'] = $value;
                        }
                    }
                }
                // 处理数字类型的值
                elseif (is_numeric($averageValue)) {
                    $value = (int) $averageValue;
                    // 如果是 CPU 资源且值看起来是标准化的核心数，转换为 millicore
                    if ($resourceName === 'cpu' && $value <= 10) {
                        $metric['resource']['target']['averageValue'] = $value * 1000;
                    } else {
                        $metric['resource']['target']['averageValue'] = $value;
                    }
                }
            }

            return $metric;
        }, $metrics);
    }

    /**
     * 获取 HPA 状态
     */
    public function getStatus(): string
    {
        if (empty($this->conditions)) {
            return 'Unknown';
        }

        // 检查各种状态条件
        $ableToScale = $this->getConditionStatus('AbleToScale');
        $scalingActive = $this->getConditionStatus('ScalingActive');
        $scalingLimited = $this->getConditionStatus('ScalingLimited');

        if ($ableToScale === 'False') {
            return 'Unable to Scale';
        }

        if ($scalingActive === 'False') {
            return 'Inactive';
        }

        if ($scalingLimited === 'True') {
            return 'Limited';
        }

        return 'Active';
    }

    /**
     * 获取指定条件的状态
     */
    private function getConditionStatus(string $type): ?string
    {
        foreach ($this->conditions as $condition) {
            if ($condition['type'] === $type) {
                return $condition['status'] ?? null;
            }
        }

        return null;
    }

    /**
     * 获取目标工作负载类型
     */
    public function getTargetKind(): string
    {
        return $this->scaleTargetRef['kind'] ?? 'Unknown';
    }

    /**
     * 获取目标工作负载名称
     */
    public function getTargetName(): string
    {
        return $this->scaleTargetRef['name'] ?? 'Unknown';
    }

    /**
     * 获取当前 CPU 使用率
     */
    public function getCurrentCpuUtilization(): ?int
    {
        foreach ($this->currentMetrics as $metric) {
            if ($metric['type'] === 'Resource' &&
                isset($metric['resource']['name']) &&
                $metric['resource']['name'] === 'cpu') {
                return $metric['resource']['current']['averageUtilization'] ?? null;
            }
        }

        return null;
    }

    /**
     * 获取当前内存使用率
     */
    public function getCurrentMemoryUtilization(): ?int
    {
        foreach ($this->currentMetrics as $metric) {
            if ($metric['type'] === 'Resource' &&
                isset($metric['resource']['name']) &&
                $metric['resource']['name'] === 'memory') {
                return $metric['resource']['current']['averageUtilization'] ?? null;
            }
        }

        return null;
    }

    /**
     * 获取目标 CPU 使用率
     */
    public function getTargetCpuUtilization(): ?int
    {
        foreach ($this->metrics as $metric) {
            if ($metric['type'] === 'Resource' &&
                isset($metric['resource']['name']) &&
                $metric['resource']['name'] === 'cpu') {
                return $metric['resource']['target']['averageUtilization'] ?? null;
            }
        }

        return null;
    }

    /**
     * 获取目标内存使用率
     */
    public function getTargetMemoryUtilization(): ?int
    {
        foreach ($this->metrics as $metric) {
            if ($metric['type'] === 'Resource' &&
                isset($metric['resource']['name']) &&
                $metric['resource']['name'] === 'memory') {
                return $metric['resource']['target']['averageUtilization'] ?? null;
            }
        }

        return null;
    }
}
