<?php

namespace App\DTOs;

/**
 * Event DTO
 */
class EventDTO
{
    public function __construct(
        public string $type,
        public string $reason,
        public string $message,
        public ?string $timestamp,
        public ?string $firstTimestamp,
        public ?string $lastTimestamp,
        public int $count,
        public string $source,
        public ?array $involvedObject = null
    ) {}

    /**
     * 从 Kubernetes Event 资源创建 DTO
     */
    public static function fromK8sResource(array $event): static
    {
        return new static(
            type: $event['type'] ?? 'Normal',
            reason: $event['reason'] ?? '',
            message: $event['message'] ?? '',
            timestamp: $event['lastTimestamp'] ?? $event['firstTimestamp'] ?? null,
            firstTimestamp: $event['firstTimestamp'] ?? null,
            lastTimestamp: $event['lastTimestamp'] ?? null,
            count: $event['count'] ?? 1,
            source: $event['source']['component'] ?? '',
            involvedObject: isset($event['involvedObject']) ? [
                'kind' => $event['involvedObject']['kind'] ?? '',
                'name' => $event['involvedObject']['name'] ?? '',
                'uid' => $event['involvedObject']['uid'] ?? '',
            ] : null
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'reason' => $this->reason,
            'message' => $this->message,
            'timestamp' => $this->timestamp,
            'first_timestamp' => $this->firstTimestamp,
            'last_timestamp' => $this->lastTimestamp,
            'count' => $this->count,
            // 'source' => $this->source,
            'involved_object' => $this->involvedObject,
        ];
    }

    /**
     * 检查是否为警告事件
     */
    public function isWarning(): bool
    {
        return $this->type === 'Warning';
    }

    /**
     * 检查是否涉及特定资源
     */
    public function involvesResource(string $kind, string $name): bool
    {
        if (! $this->involvedObject) {
            return false;
        }

        return $this->involvedObject['kind'] === $kind && $this->involvedObject['name'] === $name;
    }
}
