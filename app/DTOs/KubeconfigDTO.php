<?php

namespace App\DTOs;

class KubeconfigDTO
{
    public string $serverUrl;

    public ?string $certificateAuthorityData;

    public string $authType;

    public ?string $clientCertificateData;

    public ?string $clientKeyData;

    public ?string $token;

    public ?string $username;

    public ?string $password;

    public bool $insecureSkipTlsVerify;

    public function __construct(
        string $serverUrl,
        string $authType,
        ?string $certificateAuthorityData = null,
        ?string $clientCertificateData = null,
        ?string $clientKeyData = null,
        ?string $token = null,
        ?string $username = null,
        ?string $password = null,
        bool $insecureSkipTlsVerify = false
    ) {
        $this->serverUrl = $serverUrl;
        $this->authType = $authType;
        $this->certificateAuthorityData = $certificateAuthorityData;
        $this->clientCertificateData = $clientCertificateData;
        $this->clientKeyData = $clientKeyData;
        $this->token = $token;
        $this->username = $username;
        $this->password = $password;
        $this->insecureSkipTlsVerify = $insecureSkipTlsVerify;
    }

    public static function fromKubeconfig(string $kubeconfigContent): self
    {
        // 尝试使用 yaml_parse，如果不可用则使用 Symfony YAML
        if (function_exists('yaml_parse')) {
            $config = yaml_parse($kubeconfigContent);
        } else {
            // 使用 Symfony YAML 组件作为备用
            $config = \Symfony\Component\Yaml\Yaml::parse($kubeconfigContent);
        }

        if (! $config) {
            throw new \InvalidArgumentException('无效的 kubeconfig 格式');
        }

        $currentContext = $config['current-context'];
        $context = collect($config['contexts'])->firstWhere('name', $currentContext);

        if (! $context) {
            throw new \InvalidArgumentException('找不到当前上下文');
        }

        $clusterName = $context['context']['cluster'];
        $userName = $context['context']['user'];

        $cluster = collect($config['clusters'])->firstWhere('name', $clusterName);
        $user = collect($config['users'])->firstWhere('name', $userName);

        if (! $cluster || ! $user) {
            throw new \InvalidArgumentException('找不到集群或用户配置');
        }

        $serverUrl = $cluster['cluster']['server'];
        $certificateAuthorityData = $cluster['cluster']['certificate-authority-data'] ?? null;
        $insecureSkipTlsVerify = $cluster['cluster']['insecure-skip-tls-verify'] ?? false;

        // 确定认证类型
        $userConfig = $user['user'];
        $authType = 'token';
        $token = null;
        $clientCertificateData = null;
        $clientKeyData = null;
        $username = null;
        $password = null;

        if (isset($userConfig['token'])) {
            $authType = 'token';
            $token = $userConfig['token'];
        } elseif (isset($userConfig['client-certificate-data']) && isset($userConfig['client-key-data'])) {
            $authType = 'certificate';
            $clientCertificateData = $userConfig['client-certificate-data'];
            $clientKeyData = $userConfig['client-key-data'];
        } elseif (isset($userConfig['username']) && isset($userConfig['password'])) {
            $authType = 'username_password';
            $username = $userConfig['username'];
            $password = $userConfig['password'];
        } else {
            throw new \InvalidArgumentException('不支持的认证类型');
        }

        return new self(
            serverUrl: $serverUrl,
            authType: $authType,
            certificateAuthorityData: $certificateAuthorityData,
            clientCertificateData: $clientCertificateData,
            clientKeyData: $clientKeyData,
            token: $token,
            username: $username,
            password: $password,
            insecureSkipTlsVerify: $insecureSkipTlsVerify
        );
    }

    public function toArray(): array
    {
        return [
            'server_url' => $this->serverUrl,
            'certificate_authority_data' => $this->certificateAuthorityData,
            'auth_type' => $this->authType,
            'client_certificate_data' => $this->clientCertificateData,
            'client_key_data' => $this->clientKeyData,
            'token' => $this->token,
            'username' => $this->username,
            'password' => $this->password,
            'insecure_skip_tls_verify' => $this->insecureSkipTlsVerify,
        ];
    }
}
