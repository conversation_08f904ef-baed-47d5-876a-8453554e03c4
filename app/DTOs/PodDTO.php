<?php

namespace App\DTOs;

use Carbon\Carbon;

class PodDTO extends KubernetesResourceDTO
{
    public string $status;

    public string $phase;

    public ?string $podIP;

    public ?string $hostIP;

    public ?string $nodeName;

    public array $containers;

    public array $initContainers;

    public array $conditions;

    public ?Carbon $startTime;

    public ?Carbon $deletionTimestamp;

    public int $restartCount;

    public array $ownerReferences;

    public array $events;

    public ?array $metrics;

    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        // Create instance using parent constructor
        $dto = new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null
        );

        // Set additional properties specific to Pod
        $dto->phase = $status['phase'] ?? 'Unknown';
        $dto->status = self::determineOverallStatus($status);
        $dto->podIP = $status['podIP'] ?? null;
        $dto->hostIP = $status['hostIP'] ?? null;
        $dto->startTime = isset($status['startTime'])
            ? Carbon::parse($status['startTime'])
            : null;
        $dto->deletionTimestamp = isset($metadata['deletionTimestamp'])
            ? Carbon::parse($metadata['deletionTimestamp'])
            : null;
        $dto->nodeName = $spec['nodeName'] ?? null;
        $dto->conditions = $status['conditions'] ?? [];
        $dto->ownerReferences = $metadata['ownerReferences'] ?? [];

        // 处理容器信息
        $dto->containers = self::processContainers($spec['containers'] ?? [], $status['containerStatuses'] ?? []);
        $dto->initContainers = self::processContainers($spec['initContainers'] ?? [], $status['initContainerStatuses'] ?? []);

        // 计算重启次数
        $dto->restartCount = self::calculateRestartCount($status['containerStatuses'] ?? []);

        // 初始化事件和指标为空
        $dto->events = [];
        $dto->metrics = null;

        return $dto;
    }

    private static function determineOverallStatus(array $status): string
    {
        $phase = $status['phase'] ?? 'Unknown';
        $conditions = $status['conditions'] ?? [];
        $containerStatuses = $status['containerStatuses'] ?? [];

        // 如果 Pod 被删除
        if (isset($status['deletionTimestamp'])) {
            return 'Terminating';
        }

        // 根据阶段判断
        switch ($phase) {
            case 'Pending':
                return 'Pending';
            case 'Running':
                // 检查所有容器是否准备就绪
                $allReady = true;
                foreach ($containerStatuses as $containerStatus) {
                    if (! ($containerStatus['ready'] ?? false)) {
                        $allReady = false;
                        break;
                    }
                }

                return $allReady ? 'Running' : 'Not Ready';
            case 'Succeeded':
                return 'Completed';
            case 'Failed':
                return 'Failed';
            default:
                return 'Unknown';
        }
    }

    private static function processContainers(array $containers, array $containerStatuses): array
    {
        $result = [];

        foreach ($containers as $container) {
            $containerInfo = [
                'name' => $container['name'] ?? '',
                'image' => $container['image'] ?? '',
                'ports' => $container['ports'] ?? [],
                'env' => $container['env'] ?? [],
                // 'resources' => $container['resources'] ?? [],
                'volumeMounts' => $container['volumeMounts'] ?? [],
                'ready' => false,
                'restartCount' => 0,
                'state' => 'Unknown',
                'lastState' => null,
            ];

            // 如果 resources 为空，则不显示
            if (empty($container['resources'])) {
                $containerInfo['resources'] = [];
            } else {
                // 不为空的话，只显示 limits
                $containerInfo['resources'] = $container['resources']['limits'] ?? [];
            }

            // 查找对应的状态信息
            foreach ($containerStatuses as $status) {
                if (($status['name'] ?? '') === $containerInfo['name']) {
                    $containerInfo['ready'] = $status['ready'] ?? false;
                    $containerInfo['restartCount'] = $status['restartCount'] ?? 0;
                    $containerInfo['state'] = self::getContainerState($status['state'] ?? []);
                    $containerInfo['lastState'] = $status['lastState'] ?? null;
                    break;
                }
            }

            $result[] = $containerInfo;
        }

        return $result;
    }

    private static function getContainerState(array $state): string
    {
        if (isset($state['running'])) {
            return 'Running';
        } elseif (isset($state['waiting'])) {
            $reason = $state['waiting']['reason'] ?? 'Waiting';

            return "Waiting ($reason)";
        } elseif (isset($state['terminated'])) {
            $reason = $state['terminated']['reason'] ?? 'Terminated';

            return "Terminated ($reason)";
        }

        return 'Unknown';
    }

    private static function calculateRestartCount(array $containerStatuses): int
    {
        $totalRestarts = 0;
        foreach ($containerStatuses as $status) {
            $totalRestarts += $status['restartCount'] ?? 0;
        }

        return $totalRestarts;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'status' => $this->status,
            'phase' => $this->phase,
            'pod_ip' => $this->podIP,
            // 'host_ip' => $this->hostIP,
            // 'node_name' => $this->nodeName,
            'containers' => $this->containers,
            // 'init_containers' => $this->initContainers,
            'conditions' => $this->conditions,
            'start_time' => $this->startTime?->toISOString(),
            // 'deletion_timestamp' => $this->deletionTimestamp?->toISOString(),
            'restart_count' => $this->restartCount,
            // 'owner_references' => $this->ownerReferences,
            'events' => $this->events,
            'metrics' => $this->metrics,
        ]);
    }

    public function isReady(): bool
    {
        $readyCondition = collect($this->conditions)->firstWhere('type', 'Ready');

        return $readyCondition && $readyCondition['status'] === 'True';
    }

    public function getReadyContainersCount(): int
    {
        return collect($this->containers)->where('ready', true)->count();
    }

    public function getTotalContainersCount(): int
    {
        return count($this->containers);
    }

    public function getTotalRestartCount(): int
    {
        return $this->restartCount;
    }

    public function getOwnerKind(): ?string
    {
        return $this->ownerReferences[0]['kind'] ?? null;
    }

    public function getMainContainer(): ?array
    {
        return $this->containers[0] ?? null;
    }

    public function getStatusColor(): string
    {
        return match ($this->status) {
            'Running' => 'green',
            'Pending' => 'yellow',
            'Failed' => 'red',
            'Completed' => 'blue',
            'Terminating' => 'orange',
            default => 'gray',
        };
    }

    public function getAge(): string
    {
        if (! $this->createdAt) {
            return 'Unknown';
        }

        return $this->createdAt->diffForHumans();
    }

    public function hasWarnings(): bool
    {
        // 检查是否有 Warning 类型的事件
        foreach ($this->events as $event) {
            if (($event['type'] ?? '') === 'Warning') {
                return true;
            }
        }

        return false;
    }

    public function getWarningEvents(): array
    {
        return array_filter($this->events, function ($event) {
            return ($event['type'] ?? '') === 'Warning';
        });
    }

    public function getContainerCpuUsage(): string
    {
        if (! $this->metrics || ! isset($this->metrics['containers'])) {
            return '0m';
        }

        $totalCpu = 0;
        foreach ($this->metrics['containers'] as $container) {
            $cpuStr = $container['usage']['cpu'] ?? '0';
            // 解析 CPU 用量，如 "123m" 或 "1.5"
            if (str_ends_with($cpuStr, 'm')) {
                $totalCpu += (float) str_replace('m', '', $cpuStr);
            } else {
                $totalCpu += (float) $cpuStr * 1000; // 转换为毫核
            }
        }

        return round($totalCpu).'m';
    }

    public function getContainerMemoryUsage(): string
    {
        if (! $this->metrics || ! isset($this->metrics['containers'])) {
            return '';
        }

        $totalMemory = 0;
        foreach ($this->metrics['containers'] as $container) {
            $memoryStr = $container['usage']['memory'] ?? '0';
            // 解析内存用量，如 "123Mi" 或 "1Gi"
            if (str_ends_with($memoryStr, 'Ki')) {
                $totalMemory += (float) str_replace('Ki', '', $memoryStr) / 1024;
            } elseif (str_ends_with($memoryStr, 'Mi')) {
                $totalMemory += (float) str_replace('Mi', '', $memoryStr);
            } elseif (str_ends_with($memoryStr, 'Gi')) {
                $totalMemory += (float) str_replace('Gi', '', $memoryStr) * 1024;
            } else {
                // 假设是字节
                $totalMemory += (float) $memoryStr / (1024 * 1024);
            }
        }

        if ($totalMemory >= 1024) {
            return round($totalMemory / 1024, 1).'Gi';
        }

        return round($totalMemory).'M';
    }
}
