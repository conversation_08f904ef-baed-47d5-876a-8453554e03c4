<?php

namespace App\Contracts;

use App\Models\TopUpRecord;
use App\Models\User;

interface PaymentGatewayInterface
{
    /**
     * 获取支付方式显示名称
     */
    public function getName(): string;

    /**
     * 获取支付方式描述
     */
    public function getDescription(): string;

    /**
     * 创建支付订单
     *
     * @param  User  $user  用户
     * @param  float  $amount  金额
     * @param  array  $extra  额外参数
     * @return array 支付数据 [url, order_id, payment_data]
     */
    public function createPayment(User $user, float $amount, array $extra = []): array;

    /**
     * 验证回调数据
     *
     * @param  array  $callbackData  回调数据
     * @return array 验证结果 [success, transaction_id, amount, message]
     */
    public function verifyCallback(array $callbackData): array;

    /**
     * 处理支付成功
     *
     * @param  TopUpRecord  $record  充值记录
     * @param  array  $paymentData  支付数据
     */
    public function handleSuccess(TopUpRecord $record, array $paymentData = []): bool;

    /**
     * 处理支付失败
     *
     * @param  TopUpRecord  $record  充值记录
     * @param  array  $paymentData  支付数据
     */
    public function handleFailure(TopUpRecord $record, array $paymentData = []): bool;

    /**
     * 执行退款
     *
     * @param  TopUpRecord  $record  充值记录
     * @param  float|null  $amount  退款金额，null为全额退款
     * @param  string  $reason  退款原因
     * @return array 退款结果 [success, refund_id, message]
     */
    public function refund(TopUpRecord $record, ?float $amount = null, string $reason = ''): array;

    /**
     * 获取支付状态
     *
     * @param  string  $transactionId  交易ID
     * @return array 状态信息 [status, amount, paid_at]
     */
    public function getPaymentStatus(string $transactionId): array;
}
