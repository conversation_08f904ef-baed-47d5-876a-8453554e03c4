<?php

namespace App\Contracts;

interface IngressClassDriverInterface
{
    /**
     * 获取 IngressClass 名称
     */
    public function getIngressClassName(): string;

    /**
     * 获取默认注解
     */
    public function getDefaultAnnotations(): array;

    /**
     * 获取默认标签
     */
    public function getDefaultLabels(): array;

    /**
     * 处理规则配置
     *
     * @param  array  $rules  原始规则
     * @return array 处理后的规则
     */
    public function processRules(array $rules): array;

    /**
     * 处理 TLS 配置
     *
     * @param  array  $tls  原始 TLS 配置
     * @return array 处理后的 TLS 配置
     */
    public function processTls(array $tls): array;

    /**
     * 验证配置是否有效
     *
     * @param  array  $spec  Ingress spec
     * @return array 验证错误，空数组表示验证通过
     */
    public function validateSpec(array $spec): array;

    /**
     * 获取健康检查配置
     */
    public function getHealthCheckConfig(): array;
}
