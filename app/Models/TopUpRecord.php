<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TopUpRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_number',
        'amount',
        'remaining_amount',
        'status',
        'payment_method',
        'payment_gateway_response',
        'completed_at',
        'refunded_at',
        'refund_amount',
        'remark',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'remaining_amount' => 'decimal:8',
        'refund_amount' => 'decimal:8',
        'payment_gateway_response' => 'array',
        'completed_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    /**
     * 充值状态常量
     */
    const STATUS_PENDING = 'pending';      // 待支付

    const STATUS_COMPLETED = 'completed';  // 已完成

    const STATUS_FAILED = 'failed';       // 失败

    const STATUS_REFUNDED = 'refunded';   // 已退款

    const STATUS_PARTIAL_REFUNDED = 'partial_refunded'; // 部分退款

    // 支付方式常量已移除，现在支付方式由PaymentManagerService动态管理

    /**
     * 获取用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 检查是否还有余额
     */
    public function hasRemainingBalance(): bool
    {
        return $this->remaining_amount > 0;
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查是否可以退款
     */
    public function canRefund(): bool
    {
        return $this->isCompleted() &&
               $this->status !== self::STATUS_REFUNDED &&
               $this->remaining_amount > 0;
    }

    /**
     * 使用余额
     */
    public function useBalance(float $amount): bool
    {
        if ($amount > $this->remaining_amount) {
            return false;
        }

        // 使用BCMath进行精确计算，确保格式正确
        $currentAmount = number_format($this->remaining_amount, 8, '.', '');
        $deductAmount = number_format($amount, 8, '.', '');
        $newRemainingAmount = bcsub($currentAmount, $deductAmount, 8);
        $this->remaining_amount = $newRemainingAmount;

        return $this->save();
    }

    /**
     * 退款
     */
    public function refund(?float $amount = null): bool
    {
        if (! $this->canRefund()) {
            return false;
        }

        $refundAmount = $amount ?? $this->remaining_amount;

        if ($refundAmount > $this->remaining_amount) {
            return false;
        }

        // 使用BCMath进行精确计算，确保格式正确
        $currentRemaining = number_format($this->remaining_amount, 8, '.', '');
        $refundAmountFormatted = number_format($refundAmount, 8, '.', '');
        $currentRefunded = number_format($this->refund_amount ?? 0, 8, '.', '');

        $this->remaining_amount = bcsub($currentRemaining, $refundAmountFormatted, 8);
        $this->refund_amount = bcadd($currentRefunded, $refundAmountFormatted, 8);
        $this->refunded_at = now();

        if ($this->remaining_amount == 0) {
            $this->status = self::STATUS_REFUNDED;
        } else {
            $this->status = self::STATUS_PARTIAL_REFUNDED;
        }

        return $this->save();
    }
}
