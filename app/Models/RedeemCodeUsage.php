<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RedeemCodeUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'redeem_code_id',
        'amount',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
    ];

    /**
     * 用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 兑换码
     */
    public function redeemCode(): BelongsTo
    {
        return $this->belongsTo(RedeemCode::class);
    }
}
