<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class RedeemCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'amount',
        'max_uses',
        'used_count',
        'expires_at',
        'description',
        'is_active',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * 使用记录
     */
    public function usages(): HasMany
    {
        return $this->hasMany(RedeemCodeUsage::class);
    }

    /**
     * 检查是否可以使用
     */
    public function canUse(?User $user = null): bool
    {
        // 检查是否启用
        if (! $this->is_active) {
            return false;
        }

        // 检查是否过期
        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        // 检查使用次数
        if ($this->used_count >= $this->max_uses) {
            return false;
        }

        // 如果提供了用户，检查用户是否已经使用过（针对单次使用的兑换码）
        if ($user && $this->max_uses == 1) {
            return ! $this->usages()->where('user_id', $user->id)->exists();
        }

        return true;
    }

    /**
     * 使用兑换码
     */
    public function use(User $user, ?string $ipAddress = null, ?string $userAgent = null): RedeemCodeUsage
    {
        if (! $this->canUse($user)) {
            throw new \Exception('兑换码不可用');
        }

        // 开启事务
        return \DB::transaction(function () use ($user, $ipAddress, $userAgent) {
            // 增加使用次数
            $this->increment('used_count');

            // 创建使用记录
            return $this->usages()->create([
                'user_id' => $user->id,
                'amount' => $this->amount,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
            ]);
        });
    }

    /**
     * 检查是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 检查是否已用完
     */
    public function isExhausted(): bool
    {
        return $this->used_count >= $this->max_uses;
    }

    /**
     * 生成随机兑换码
     */
    public static function generateCode(int $length = 16): string
    {
        do {
            $code = strtoupper(Str::random($length));
        } while (static::where('code', $code)->exists());

        return $code;
    }

    /**
     * 查找兑换码（无论是否可用）
     */
    public static function findByCode(string $code): ?static
    {
        return static::where('code', $code)->first();
    }

    /**
     * 查找可用的兑换码
     */
    public static function findUsableByCode(string $code): ?static
    {
        return static::where('code', $code)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->whereColumn('used_count', '<', 'max_uses')
            ->first();
    }
}
