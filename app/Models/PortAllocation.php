<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PortAllocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'pool_ip_id',
        'port',
        'service_name',
        'namespace',
        'status',
        'is_disabled',
        'reason',
        'allocated_at',
        'released_at',
    ];

    protected $casts = [
        'port' => 'integer',
        'is_disabled' => 'boolean',
        'allocated_at' => 'datetime',
        'released_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    public const STATUS_ALLOCATED = 'allocated';

    public const STATUS_RELEASED = 'released';

    /**
     * 关联到 IP 池中的 IP
     */
    public function poolIp(): BelongsTo
    {
        return $this->belongsTo(PoolIp::class);
    }

    /**
     * 释放端口
     */
    public function release(): void
    {
        $this->update([
            'status' => self::STATUS_RELEASED,
            'released_at' => now(),
        ]);

        // 减少 IP 的使用次数
        $this->poolIp->decrementUsage();
    }

    /**
     * 检查是否已分配
     */
    public function isAllocated(): bool
    {
        return $this->status === self::STATUS_ALLOCATED;
    }

    /**
     * 检查是否已释放
     */
    public function isReleased(): bool
    {
        return $this->status === self::STATUS_RELEASED;
    }

    /**
     * 获取分配时长（秒）
     */
    public function getAllocationDurationAttribute(): ?int
    {
        if (! $this->allocated_at) {
            return null;
        }

        $endTime = $this->released_at ?? now();

        return $endTime->diffInSeconds($this->allocated_at);
    }

    /**
     * 范围查询：只获取已分配的端口
     */
    public function scopeAllocated($query)
    {
        return $query->where('status', self::STATUS_ALLOCATED);
    }

    /**
     * 范围查询：只获取已释放的端口
     */
    public function scopeReleased($query)
    {
        return $query->where('status', self::STATUS_RELEASED);
    }

    /**
     * 范围查询：根据 Service 查找
     */
    public function scopeForService($query, string $serviceName, string $namespace)
    {
        return $query->where('service_name', $serviceName)
            ->where('namespace', $namespace);
    }
}
