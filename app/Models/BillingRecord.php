<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BillingRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'cluster_id',
        'user_id',
        'billing_start_at',
        'billing_end_at',
        'resource_usage',
        'memory_cost',
        'cpu_cost',
        'storage_cost',
        'loadbalancer_cost',
        'total_cost',
        'status',
        'charged_at',
    ];

    protected $casts = [
        'billing_start_at' => 'datetime',
        'billing_end_at' => 'datetime',
        'charged_at' => 'datetime',
        'resource_usage' => 'array',
        'memory_cost' => 'decimal:8',
        'cpu_cost' => 'decimal:8',
        'storage_cost' => 'decimal:8',
        'loadbalancer_cost' => 'decimal:8',
        'total_cost' => 'decimal:8',
    ];

    // 状态常量
    const STATUS_PENDING = 'pending';

    const STATUS_CHARGED = 'charged';

    const STATUS_FAILED = 'failed';

    /**
     * 关联到工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 关联到集群
     */
    public function cluster(): BelongsTo
    {
        return $this->belongsTo(Cluster::class);
    }

    /**
     * 关联到用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 标记为已扣费
     */
    public function markAsCharged(): bool
    {
        return $this->update([
            'status' => self::STATUS_CHARGED,
            'charged_at' => now(),
        ]);
    }

    /**
     * 标记为扣费失败
     */
    public function markAsFailed(): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
        ]);
    }

    /**
     * 检查是否可以扣费
     */
    public function canCharge(): bool
    {
        return $this->status === self::STATUS_PENDING && $this->total_cost > 0;
    }
}
