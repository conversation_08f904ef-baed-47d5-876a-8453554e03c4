<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Setting extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
    ];

    /**
     * 获取拥有此设置的模型
     */
    public function settingable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 转换值为适当的格式
     *
     * @param  mixed  $value
     * @return mixed
     */
    public function getValueAttribute($value)
    {
        // 尝试将JSON字符串转换为数组或对象
        $decoded = json_decode($value, true);

        // 如果解码成功且不是null，则返回解码后的值
        if (json_last_error() === JSON_ERROR_NONE && $decoded !== null) {
            return $decoded;
        }

        // 否则返回原始值
        return $value;
    }

    /**
     * 设置值时自动转换为JSON
     *
     * @param  mixed  $value
     * @return void
     */
    public function setValueAttribute($value)
    {
        // 如果值是数组或对象，则转换为JSON字符串
        if (is_array($value) || is_object($value)) {
            $this->attributes['value'] = json_encode($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }
}
