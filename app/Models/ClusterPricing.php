<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClusterPricing extends Model
{
    use HasFactory;

    protected $fillable = [
        'cluster_id',
        'memory_price_per_gb',
        'cpu_price_per_core',
        'storage_price_per_gb',
        'loadbalancer_price_per_service',
        'billing_enabled',
        'description',
    ];

    protected $casts = [
        'memory_price_per_gb' => 'decimal:4',
        'cpu_price_per_core' => 'decimal:4',
        'storage_price_per_gb' => 'decimal:4',
        'loadbalancer_price_per_service' => 'decimal:4',
        'billing_enabled' => 'boolean',
    ];

    /**
     * 关联到集群
     */
    public function cluster(): BelongsTo
    {
        return $this->belongsTo(Cluster::class);
    }

    /**
     * 计算内存每分钟价格
     *
     * @param  int  $memoryMi  内存(Mi)
     * @return string BCMath 字符串
     */
    public function calculateMemoryPricePerMinute(int $memoryMi): string
    {
        // 转换 Mi 到 GB (1024 Mi = 1 GB)
        $memoryGb = bcdiv((string) $memoryMi, '1024', 8);

        // 月价格转换为分钟价格 (30天 * 24小时 * 60分钟 = 43200分钟)
        $pricePerMinute = bcdiv((string) $this->memory_price_per_gb, '43200', 8);

        return bcmul($memoryGb, $pricePerMinute, 8);
    }

    /**
     * 计算CPU每分钟价格
     *
     * @param  int  $cpuMillicores  CPU(m)
     * @return string BCMath 字符串
     */
    public function calculateCpuPricePerMinute(int $cpuMillicores): string
    {
        // 转换 m 到 cores (1000m = 1 core)
        $cpuCores = bcdiv((string) $cpuMillicores, '1000', 8);

        // 月价格转换为分钟价格
        $pricePerMinute = bcdiv((string) $this->cpu_price_per_core, '43200', 8);

        return bcmul($cpuCores, $pricePerMinute, 8);
    }

    /**
     * 计算存储每分钟价格
     *
     * @param  int  $storageGi  存储(Gi)
     * @return string BCMath 字符串
     */
    public function calculateStoragePricePerMinute(int $storageGi): string
    {
        // 月价格转换为分钟价格
        $pricePerMinute = bcdiv((string) $this->storage_price_per_gb, '43200', 8);

        return bcmul((string) $storageGi, $pricePerMinute, 8);
    }

    /**
     * 计算LoadBalancer服务每分钟价格
     *
     * @param  int  $serviceCount  服务数量
     * @return string BCMath 字符串
     */
    public function calculateLoadBalancerPricePerMinute(int $serviceCount): string
    {
        // 月价格转换为分钟价格
        $pricePerMinute = bcdiv((string) $this->loadbalancer_price_per_service, '43200', 8);

        return bcmul((string) $serviceCount, $pricePerMinute, 8);
    }

    /**
     * 计算总的每分钟价格预览
     *
     * @param  array  $resources  资源使用情况
     * @return array 价格详情
     */
    public function calculatePricePreview(array $resources): array
    {
        $memoryPrice = '0';
        $cpuPrice = '0';
        $storagePrice = '0';
        $loadBalancerPrice = '0';

        if (isset($resources['memory_mi'])) {
            $memoryPrice = $this->calculateMemoryPricePerMinute($resources['memory_mi']);
        }

        if (isset($resources['cpu_m'])) {
            $cpuPrice = $this->calculateCpuPricePerMinute($resources['cpu_m']);
        }

        if (isset($resources['storage_gi'])) {
            $storagePrice = $this->calculateStoragePricePerMinute($resources['storage_gi']);
        }

        if (isset($resources['loadbalancer_count'])) {
            $loadBalancerPrice = $this->calculateLoadBalancerPricePerMinute($resources['loadbalancer_count']);
        }

        $totalPrice = bcadd(
            bcadd($memoryPrice, $cpuPrice, 8),
            bcadd($storagePrice, $loadBalancerPrice, 8),
            8
        );

        return [
            'memory_price_per_minute' => $memoryPrice,
            'cpu_price_per_minute' => $cpuPrice,
            'storage_price_per_minute' => $storagePrice,
            'loadbalancer_price_per_minute' => $loadBalancerPrice,
            'total_price_per_minute' => $totalPrice,
        ];
    }
}
