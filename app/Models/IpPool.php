<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class IpPool extends Model
{
    use HasFactory;

    protected $fillable = [
        'cluster_id',
        'driver',
        'name',
        'description',
        'allocation_strategy',
        'is_active',
        // 网关配置
        'gateway_v4',
        'gateway_v6',
        // IP 版本和子网信息
        'ip_version',
        'subnet_v4',
        'subnet_v6',
        // PureLB 同步状态
        'synced_to_k8s',
        'last_sync_at',
        'sync_error',
    ];

    /**
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'synced_to_k8s' => 'boolean',
        'last_sync_at' => 'datetime',
    ];

    /**
     * @var array<string, string>
     */
    protected $attributes = [
        'is_active' => true,
        'ip_version' => self::IP_VERSION_IPV4,
        'driver' => 'metallb',
    ];

    /**
     * IP 分配策略常量
     */
    public const STRATEGY_LEAST_USED = 'least_used';

    public const STRATEGY_ROUND_ROBIN = 'round_robin';

    public const STRATEGY_RANDOM = 'random';

    /**
     * IP 版本常量
     */
    public const IP_VERSION_IPV4 = 'ipv4';

    public const IP_VERSION_IPV6 = 'ipv6';

    public const IP_VERSION_DUAL = 'dual';

    /**
     * 获取所有可用的分配策略
     */
    public static function getAvailableStrategies(): array
    {
        return [
            self::STRATEGY_LEAST_USED => '最少使用',
            self::STRATEGY_ROUND_ROBIN => '轮询分配',
            self::STRATEGY_RANDOM => '随机分配',
        ];
    }

    /**
     * 获取所有可用的 IP 版本
     */
    public static function getAvailableIpVersions(): array
    {
        return [
            self::IP_VERSION_IPV4 => 'IPv4',
            self::IP_VERSION_IPV6 => 'IPv6',
            self::IP_VERSION_DUAL => '双栈 (IPv4 + IPv6)',
        ];
    }

    /**
     * 关联到集群
     */
    public function cluster(): BelongsTo
    {
        return $this->belongsTo(Cluster::class);
    }

    /**
     * 关联到 IP 地址
     */
    public function poolIps(): HasMany
    {
        return $this->hasMany(PoolIp::class);
    }

    /**
     * 获取活跃的 IP 地址
     */
    public function activePoolIps(): HasMany
    {
        return $this->poolIps()->active();
    }

    /**
     * 获取 IP 池统计信息
     */
    public function getStatsAttribute(): array
    {
        $totalIps = $this->poolIps()->count();
        $activeIps = $this->activePoolIps()->count();
        $totalUsage = $this->poolIps()->sum('usage_count');

        return [
            'total_ips' => $totalIps,
            'active_ips' => $activeIps,
            'inactive_ips' => $totalIps - $activeIps,
            'total_usage' => $totalUsage,
            'average_usage' => $activeIps > 0 ? round($totalUsage / $activeIps, 2) : 0,
        ];
    }

    /**
     * 获取 ServiceGroup 名称，如果没有设置则使用 IP 池名称
     */
    public function getServiceGroupNameAttribute($value): string
    {
        return $value ?: $this->name;
    }

    /**
     * 检查是否支持 IPv4
     */
    public function supportsIpv4(): bool
    {
        return in_array($this->ip_version, [self::IP_VERSION_IPV4, self::IP_VERSION_DUAL]);
    }

    /**
     * 检查是否支持 IPv6
     */
    public function supportsIpv6(): bool
    {
        return in_array($this->ip_version, [self::IP_VERSION_IPV6, self::IP_VERSION_DUAL]);
    }

    /**
     * 范围查询：只获取活跃的 IP 池
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
