<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use InvalidArgumentException;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'taskable_type',
        'taskable_id',
        'workspace_id',
        'type',
        'status',
        'data',
        'result',
        'error_message',
        'attempts',
        'max_attempts',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'data' => 'array',
        'result' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    protected $hidden = [
        'taskable_type',
        'taskable_id',
    ];

    protected $appends = [
        'resource',
    ];

    // 状态常量
    public const string STATUS_PENDING = 'pending';

    public const string STATUS_RUNNING = 'running';

    public const string STATUS_COMPLETED = 'completed';

    public const string STATUS_FAILED = 'failed';

    // 任务类型常量（注意：不应该在 Task 里面定义，而是各个模型里面定义，命名方式：模型名:任务名）
    // public const string TYPE_CREATE_WORKSPACE = 'workspace:create';

    // public const string TYPE_DELETE_WORKSPACE = 'workspace:delete';

    /**
     * 启动时验证
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($task) {
            // 验证 workspace_id 是否存在，特殊情况下允许为空
            // 当任务类型包含 ':delete' 时，在某些情况下允许 workspace_id 为 null
            if (empty($task->workspace_id) && ! self::isDeleteTaskAllowingNullWorkspace($task)) {
                throw new InvalidArgumentException('Task 必须包含 workspace_id');
            }
        });
    }

    /**
     * 检查是否是允许 workspace_id 为 null 的删除任务
     */
    private static function isDeleteTaskAllowingNullWorkspace($task): bool
    {
        // 如果任务类型包含 ':delete'，在特定情况下允许 workspace_id 为 null
        // 这主要用于 workspace 删除过程中的特殊情况
        return str_contains($task->type ?? '', ':delete');
    }

    /**
     * 获取拥有任务的模型
     */
    public function taskable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 获取所属的工作区
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 获取资源标识符
     * 格式: {模型名}.{ID}，例如: workspace.2
     */
    public function getResourceAttribute(): string
    {
        $modelName = class_basename($this->taskable_type);

        return strtolower($modelName).'.'.$this->taskable_id;
    }

    /**
     * 检查任务是否可以重试
     */
    public function canRetry(): bool
    {
        // 如果 max_attempts 为 0，表示无限重试
        if ($this->max_attempts === 0) {
            return $this->status === self::STATUS_FAILED;
        }

        return $this->status === self::STATUS_FAILED &&
               $this->attempts < $this->max_attempts;
    }

    /**
     * 标记任务为运行中
     */
    public function markAsRunning(): void
    {
        $this->update([
            'status' => self::STATUS_RUNNING,
            'started_at' => now(),
        ]);
    }

    /**
     * 标记任务为完成
     */
    public function markAsCompleted(?array $result = null): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'result' => $result,
            'completed_at' => now(),
        ]);
    }

    /**
     * 标记任务为失败
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'attempts' => $this->attempts + 1,
            'completed_at' => now(),
        ]);
    }

    /**
     * 重置任务状态
     */
    public function reset(): void
    {
        $this->update([
            'status' => self::STATUS_PENDING,
            'error_message' => null,
            'started_at' => null,
            'completed_at' => null,
            // 注意：不重置 attempts，因为重试应该保留尝试次数记录
        ]);
    }
}
