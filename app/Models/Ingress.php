<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Ingress extends Model
{
    use HasFactory;

    protected $fillable = [
        'workspace_id',
        'name',
        'ingress_class',
        'domains',
        'tls_config',
        'annotations',
        'labels',
        'status',
        'status_message',
    ];

    protected $casts = [
        'domains' => 'array',
        'tls_config' => 'array',
        'annotations' => 'array',
        'labels' => 'array',
    ];

    /**
     * 状态常量
     */
    public const STATUS_PENDING = 'pending';

    public const STATUS_ACTIVE = 'active';

    public const STATUS_FAILED = 'failed';

    /**
     * 支持的 Ingress 类型
     */
    public const CLASS_TRAEFIK = 'traefik';

    public const CLASS_NGINX = 'nginx';

    /**
     * 关联到工作空间
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 获取所有域名列表
     */
    public function getAllDomains(): array
    {
        $domains = [];
        foreach ($this->domains ?? [] as $domainConfig) {
            if (isset($domainConfig['host'])) {
                $domains[] = $domainConfig['host'];
            }
        }

        return array_unique($domains);
    }

    /**
     * 获取泛域名列表
     */
    public function getWildcardDomains(): array
    {
        return array_filter($this->getAllDomains(), function ($domain) {
            return str_starts_with($domain, '*.');
        });
    }

    /**
     * 检查是否有 TLS 配置
     */
    public function hasTls(): bool
    {
        return ! empty($this->tls_config);
    }

    /**
     * 检查状态是否为激活
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 检查状态是否为待处理
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 检查状态是否为失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查域名冲突
     */
    public function checkDomainConflicts(array $newDomains): array
    {
        $conflicts = [];
        $clusterId = $this->workspace->cluster_id;

        foreach ($newDomains as $domainConfig) {
            $host = $domainConfig['host'] ?? '';
            if (empty($host)) {
                continue;
            }

            $paths = $domainConfig['http']['paths'] ?? [['path' => '/']];
            foreach ($paths as $pathConfig) {
                $path = $pathConfig['path'] ?? '/';

                // 查找相同集群中的冲突域名
                $conflictingIngress = self::whereHas('workspace', function ($query) use ($clusterId) {
                    $query->where('cluster_id', $clusterId);
                })
                    ->where('id', '!=', $this->id)
                    ->get()
                    ->filter(function ($ingress) use ($host, $path) {
                        return $this->hasConflictingDomain($ingress, $host, $path);
                    })
                    ->first();

                if ($conflictingIngress) {
                    $conflicts[] = [
                        'domain' => $host,
                        'path' => $path,
                        'conflicting_ingress' => $conflictingIngress->name,
                        'message' => "域名 {$host} 路径 {$path} 与 Ingress {$conflictingIngress->name} 冲突",
                    ];
                }
            }
        }

        return $conflicts;
    }

    /**
     * 检查是否与指定 Ingress 有域名冲突
     */
    private function hasConflictingDomain(Ingress $otherIngress, string $host, string $path): bool
    {
        foreach ($otherIngress->domains ?? [] as $domainConfig) {
            $otherHost = $domainConfig['host'] ?? '';
            if (empty($otherHost)) {
                continue;
            }

            $otherPaths = $domainConfig['http']['paths'] ?? [['path' => '/']];
            foreach ($otherPaths as $otherPathConfig) {
                $otherPath = $otherPathConfig['path'] ?? '/';

                // 检查域名和路径是否冲突
                if ($this->domainsConflict($host, $otherHost) && $this->pathsConflict($path, $otherPath)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查两个域名是否冲突
     */
    private function domainsConflict(string $domain1, string $domain2): bool
    {
        // 完全相同
        if ($domain1 === $domain2) {
            return true;
        }

        // 检查泛域名冲突
        if (str_starts_with($domain1, '*.') || str_starts_with($domain2, '*.')) {
            $pattern1 = str_starts_with($domain1, '*.') ? substr($domain1, 2) : null;
            $pattern2 = str_starts_with($domain2, '*.') ? substr($domain2, 2) : null;

            if ($pattern1 && $this->domainMatchesWildcard($domain2, $pattern1)) {
                return true;
            }

            if ($pattern2 && $this->domainMatchesWildcard($domain1, $pattern2)) {
                return true;
            }

            // 两个泛域名重叠
            if ($pattern1 && $pattern2) {
                return $pattern1 === $pattern2 ||
                       str_ends_with($pattern1, '.'.$pattern2) ||
                       str_ends_with($pattern2, '.'.$pattern1);
            }
        }

        return false;
    }

    /**
     * 检查域名是否匹配泛域名模式
     */
    private function domainMatchesWildcard(string $domain, string $wildcardPattern): bool
    {
        return str_ends_with($domain, '.'.$wildcardPattern) || $domain === $wildcardPattern;
    }

    /**
     * 检查两个路径是否冲突
     */
    private function pathsConflict(string $path1, string $path2): bool
    {
        // 完全相同的路径
        if ($path1 === $path2) {
            return true;
        }

        // 都是根路径的变体
        if (($path1 === '/' || $path1 === '') && ($path2 === '/' || $path2 === '')) {
            return true;
        }

        return false;
    }
}
