<?php

namespace App\Models;

use App\ClusterLabel;
use App\Jobs\ProcessTaskJob;
use App\Service\NamespaceService;
use App\Traits\HasTasks;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Workspace 是用户所有资源（包括部署，服务等）的集合
 * Workspace 对应了 Kubernetes 的 namespace
 */
class Workspace extends Model
{
    use HasFactory, HasTasks;

    protected $fillable = [
        'user_id',
        'cluster_id',
        'name',
        'status',
        'namespace',
        'suspended_at',
        'suspension_reason',
        'overdue_amount',
        'last_overdue_at',
    ];

    protected $casts = [
        'suspended_at' => 'datetime',
        'last_overdue_at' => 'datetime',
        'overdue_amount' => 'decimal:4',
    ];

    // 状态常量
    public const string STATUS_PENDING = 'pending';

    public const string STATUS_CREATING = 'creating';

    public const string STATUS_DELETING = 'deleting';

    public const string STATUS_ACTIVE = 'active';

    public const string STATUS_SUSPENDED = 'suspended';

    public const string STATUS_FAILED = 'failed';

    // 任务类型常量
    public const string TASK_WORKSPACE_CREATE = 'workspace:create';

    public const string TASK_WORKSPACE_DELETE = 'workspace:delete';

    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();

        // 创建后事件：启动创建任务
        static::created(function (Workspace $workspace) {
            DB::afterCommit(function () use ($workspace) {
                Log::info('工作空间创建成功，启动创建任务', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $workspace->namespace,
                ]);

                $workspace->startCreationTask();
            });
        });

        // 注意：删除任务现在通过 WorkspaceService::startWorkspaceDeletion 手动启动
        // 这样可以确保在删除任务完成之前，Workspace 记录不会被删除
    }

    public function cluster(): BelongsTo
    {
        return $this->belongsTo(Cluster::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取所有任务
     */
    public function tasks(): MorphMany
    {
        return $this->morphMany(Task::class, 'taskable');
    }

    /**
     * 处理任务
     */
    public function handleTask(Task $task): void
    {
        try {
            $task->markAsRunning();

            switch ($task->type) {
                case self::TASK_WORKSPACE_CREATE:
                    $this->handleCreateWorkspaceTask($task);
                    break;

                case self::TASK_WORKSPACE_DELETE:
                    $this->handleDeleteWorkspaceTask($task);
                    break;

                default:
                    throw new \InvalidArgumentException("未知的任务类型: {$task->type}");
            }

            // 重新加载任务以检查状态，防止在子方法中已经标记为完成
            $task->refresh();

            // 只有任务还未完成时才标记为完成
            if ($task->status !== Task::STATUS_COMPLETED) {
                $task->markAsCompleted();
            }
        } catch (\Exception $e) {
            Log::error('处理任务失败', [
                'task_id' => $task->id,
                'task_type' => $task->type,
                'workspace_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            // 检查是否是需要重试的删除任务
            if (
                $task->type === self::TASK_WORKSPACE_DELETE &&
                str_contains($e->getMessage(), '需要重试检查状态')
            ) {
                // 先标记任务为失败，这样 canRetry() 才能正确工作
                $task->markAsFailed($e->getMessage());

                // 检查是否可以重试（支持无限重试）
                if ($task->canRetry()) {
                    // 重置任务状态为 pending，准备重试
                    $task->reset();

                    Log::info('任务需要重试，重新调度', [
                        'task_id' => $task->id,
                        'attempts' => $task->attempts,
                        'max_attempts' => $task->max_attempts === 0 ? '无限' : $task->max_attempts,
                    ]);

                    // 延迟 30 秒后重试
                    ProcessTaskJob::dispatch($task)->delay(now()->addSeconds(30));

                    return;
                }

                // 如果不能重试，任务已经在上面被标记为失败，直接返回
                return;
            }

            $task->markAsFailed($e->getMessage());

            // 如果是创建任务失败，更新工作空间状态
            if ($task->type === self::TASK_WORKSPACE_CREATE) {
                $this->update([
                    'status' => self::STATUS_FAILED,
                    'suspension_reason' => 'Namespace 创建失败: '.$e->getMessage(),
                ]);
            }
        }
    }

    /**
     * 处理创建工作空间任务
     */
    protected function handleCreateWorkspaceTask(Task $task): void
    {
        $this->update(['status' => self::STATUS_CREATING]);

        Log::info('开始创建工作空间 Namespace', [
            'workspace_id' => $this->id,
            'namespace' => $this->namespace,
        ]);

        // 使用 NamespaceService 创建 namespace
        $namespaceService = app(NamespaceService::class);
        $success = $namespaceService->createNamespace($this);

        if (! $success) {
            throw new \Exception('创建 Namespace 失败');
        }

        Log::info('工作空间创建成功', [
            'workspace_id' => $this->id,
            'namespace' => $this->namespace,
        ]);
    }

    /**
     * 处理删除工作空间任务
     */
    protected function handleDeleteWorkspaceTask(Task $task): void
    {
        // 检查 namespace 删除状态
        $namespaceService = app(NamespaceService::class);
        $workspaceService = app(\App\Service\WorkspaceService::class);
        $namespaceStatus = $workspaceService->checkKubernetesNamespaceStatus($this);

        if ($namespaceStatus === 'not_found') {
            // Namespace 已经不存在，完成删除
            Log::info('工作空间删除成功，namespace 已不存在', [
                'workspace_id' => $this->id,
                'namespace' => $this->namespace,
            ]);

            // 先标记任务为完成
            $task->markAsCompleted(['namespace_status' => 'not_found']);

            // 然后删除工作空间记录
            DB::afterCommit(function () {
                $this->delete();
            });

            return;
        }

        if ($namespaceStatus === 'active') {
            // Namespace 还存在且处于活跃状态，开始删除流程
            $success = $namespaceService->deleteNamespace($this);

            if (! $success) {
                throw new \Exception('删除 Namespace 失败');
            }

            Log::info('已发起 namespace 删除请求，等待删除完成', [
                'workspace_id' => $this->id,
                'namespace' => $this->namespace,
            ]);

            // 抛出异常让任务重试
            throw new \Exception('Namespace 删除中，需要重试检查状态');
        }

        if ($namespaceStatus === 'terminating') {
            // Namespace 正在删除中，等待删除完成
            Log::info('Namespace 正在删除中，等待完成', [
                'workspace_id' => $this->id,
                'namespace' => $this->namespace,
            ]);

            // 抛出异常让任务重试
            throw new \Exception('Namespace 删除中，需要重试检查状态');
        }

        // 其他未知状态，记录并重试
        Log::warning('Namespace 状态未知，重试中', [
            'workspace_id' => $this->id,
            'namespace' => $this->namespace,
            'status' => $namespaceStatus,
        ]);

        throw new \Exception('Namespace 状态未知，需要重试检查状态');
    }

    /**
     * 检查 Kubernetes namespace 状态
     */
    protected function checkKubernetesNamespaceStatus(): string
    {
        if (! $this->namespace) {
            return 'not_found';
        }

        try {
            $response = $this->cluster->http()->get("/api/v1/namespaces/{$this->namespace}");

            if ($response->successful()) {
                $namespace = $response->json();

                // 检查 namespace 的状态
                if (isset($namespace['status']['phase'])) {
                    $phase = $namespace['status']['phase'];

                    if ($phase === 'Terminating') {
                        return 'terminating';
                    }

                    if ($phase === 'Active') {
                        return 'active';
                    }
                }

                // 检查是否有删除时间戳
                if (isset($namespace['metadata']['deletionTimestamp'])) {
                    return 'terminating';
                }

                return 'active';
            }

            return 'not_found';
        } catch (\Exception $e) {
            // 404 错误表示 namespace 不存在
            if (str_contains($e->getMessage(), '404') || str_contains($e->getMessage(), 'not found')) {
                return 'not_found';
            }

            Log::error('检查 namespace 状态失败', [
                'namespace' => $this->namespace,
                'error' => $e->getMessage(),
                'cluster_id' => $this->cluster_id,
            ]);

            // 其他错误返回未知状态
            return 'unknown';
        }
    }

    /**
     * 启动创建工作空间任务
     */
    public function startCreationTask(): Task
    {
        $task = $this->createTask(self::TASK_WORKSPACE_CREATE, [
            'name' => $this->name,
            'cluster_id' => $this->cluster_id,
            'namespace' => $this->namespace,
        ]);

        // 分派队列任务
        ProcessTaskJob::dispatch($task);

        return $task;
    }

    /**
     * 启动删除工作空间任务
     */
    public function startDeletionTask(): Task
    {
        // 删除任务设置为无限重试，因为 namespace 删除可能需要很长时间
        $task = $this->createTask(self::TASK_WORKSPACE_DELETE, [
            'namespace' => $this->namespace,
        ], 0); // 0 表示无限重试

        // 分派队列任务
        ProcessTaskJob::dispatch($task);

        return $task;
    }

    /**
     * 重试创建 namespace
     */
    public function retryCreateNamespace(): void
    {
        if ($this->status === self::STATUS_FAILED) {
            DB::transaction(function () {
                // 重置状态为 pending
                $this->update([
                    'status' => self::STATUS_PENDING,
                    'suspension_reason' => null,
                ]);

                // 在事务提交后启动创建任务
                DB::afterCommit(function () {
                    Log::info('重试创建工作空间 Namespace', [
                        'workspace_id' => $this->id,
                        'namespace' => $this->namespace,
                    ]);

                    $this->startCreationTask();
                });
            });
        }
    }

    public function buildDefaultLabels(?Model $model = null, ?string $resourceName = null): array
    {
        $labels = [
            ClusterLabel::WORKSPACE->value => $this->sanitizeLabelValue($this->name),
            ClusterLabel::USER->value => $this->sanitizeLabelValue($this->user->name),
            ClusterLabel::MANAGED_BY->value => 'leaflow.cn',
            ClusterLabel::PLATFORM->value => 'leaflow.cn',
        ];

        // 如果有 model，使用其 ID；否则使用资源名称作为标识
        if ($model && $model->id) {
            $labels[ClusterLabel::RESOURCE_ID->value] = (string) $model->id;
        } elseif ($resourceName) {
            $labels[ClusterLabel::RESOURCE_ID->value] = $this->sanitizeLabelValue($resourceName);
        }

        return $labels;
    }

    /**
     * 清理 label 值，使其符合 Kubernetes 规范
     * Kubernetes label 值必须是空字符串或由字母数字字符、'-'、'_' 或 '.' 组成，
     * 并且必须以字母数字字符开头和结尾
     */
    private function sanitizeLabelValue(string $value): string
    {
        // 替换空格为连字符
        $value = str_replace(' ', '-', $value);

        // 只保留允许的字符
        $value = preg_replace('/[^A-Za-z0-9\-_.]/', '', $value);

        // 确保以字母数字字符开头
        $value = preg_replace('/^[^A-Za-z0-9]+/', '', $value);

        // 确保以字母数字字符结尾
        $value = preg_replace('/[^A-Za-z0-9]+$/', '', $value);

        // 限制长度（Kubernetes label 值最大 63 个字符）
        return substr($value, 0, 63);
    }

    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isSuspended(): bool
    {
        return $this->status === self::STATUS_SUSPENDED;
    }
}
