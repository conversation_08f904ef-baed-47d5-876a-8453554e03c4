<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" @class(['dark' => ($appearance ?? 'system') == 'dark'])>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {{-- csrf --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- Inline script to detect system dark mode preference and apply it immediately --}}
    <script>
        (function () {
            const appearance = '{{ $appearance ?? "system" }}';

            if (appearance === 'system') {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                if (prefersDark) {
                    document.documentElement.classList.add('dark');
                }
            }
        })();
    </script>

    {{-- Inline style to set the HTML background color based on our theme in app.css --}}
    <style>
        html {
            background-color: oklch(1 0 0);
        }

        html.dark {
            background-color: oklch(0.145 0 0);
        }
    </style>

    <title inertia>{{ config('app.name', 'Laravel') }}</title>

    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">

    {{--
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" /> --}}

    @php
        $user = auth('web')->user();
        $permissions = 0; // 默认值为0

        if ($user) {
            // 获取角色名称
            $roles = $user->getRoleNames();

            // 获取所有权限名称
            $permissionsList = $user->getAllPermissions()->pluck('name');

            // 构建权限数组
            $permissionsArray = [
                'roles' => $roles,
                'permissions' => $permissionsList,
            ];

            // 将权限数组编码为 JSON
            $permissions = json_encode($permissionsArray);
        }
    @endphp

    <script type="text/javascript">
        window.App = {
            user: {!! $user ? json_encode($user) : 'null' !!},
            csrfToken: "{{ csrf_token() }}",
            permissions: {!! $permissions !!}
        }
    </script>

    <script>
            (function () {
                const workspaceId = '{{ getPermissionsTeamId() }}';
                if (workspaceId) {
                    window.App.workspaceId = workspaceId;
                }
            })();
    </script>


    @routes
    @vite(['resources/js/app.ts', "resources/js/pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="font-sans antialiased">
    @inertia
</body>

</html>