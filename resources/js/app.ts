import '../css/app.css';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import './echo';
import Persistent from './layouts/app/Persistent.vue';
import './lib/axios'; // 初始化 axios 配置

const pinia = createPinia();

// @ts-expect-error pinia-plugin-persistedstate
pinia.use(piniaPluginPersistedstate);

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    // @ts-expect-error resolve
    resolve: (name) => {
        const page = resolvePageComponent(`./pages/${name}.vue`, import.meta.glob('./pages/**/*.vue'));

        let useDefaultLayout = true;

        // 如果 page 中包含 .Clean，比如 preview.Clean.Vue，则不适用默认 Layout
        if (name.includes('.Clean')) {
            useDefaultLayout = false;
        }

        if (name.includes('.Public')) {
            useDefaultLayout = false;
        }

        // 如果 URL 是以 /public 开头（不区分大小写）
        if (window.location.pathname.toLowerCase().startsWith('/public')) {
            useDefaultLayout = false;
        }

        if (useDefaultLayout) {
            page.then((module: any) => {
                module.default.layout = module.default.layout || Persistent;
            });
        }
        return page;
    },
    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(pinia)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on page load...
initializeTheme();
