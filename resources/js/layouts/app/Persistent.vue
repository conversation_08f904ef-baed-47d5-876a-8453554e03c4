<template>
    <Toaster class="pointer-events-auto" />

    <slot />
</template>

<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner';
import 'vue-sonner/style.css'; // vue-sonner v2 requires this import

import eventBus, { type ApiErrorPayload } from '@/lib/eventBus';
import { usePage } from '@inertiajs/vue3';
import { AlertCircle, CheckCircle } from 'lucide-vue-next';
import { onMounted, onUnmounted, watch } from 'vue';
import { toast } from 'vue-sonner';

const page = usePage();

const handleApiError = (payload: ApiErrorPayload) => {
    let description = '';
    if (payload.errors) {
        description = Object.values(payload.errors).flat().join('<br>');
    }

    toast.error(payload.message, {
        icon: AlertCircle,
        description,
        class: 'error-toast',
    });
};

onMounted(() => {
    eventBus.on('api:error', handleApiError);
});

onUnmounted(() => {
    eventBus.off('api:error', handleApiError);
});

watch(
    () => page.props.flash,
    (newFlash: any) => {
        if (newFlash?.success) {
            toast.success(newFlash.success, {
                icon: CheckCircle,
                description: '操作成功',
                class: 'success-toast',
            });
        }

        if (newFlash?.error) {
            toast.error(newFlash.error, {
                icon: AlertCircle,
                description: '发生错误',
                class: 'error-toast',
            });
        }

        if (newFlash?.message) {
            toast(newFlash.message, {
                icon: AlertCircle,
                description: '提示',
                class: 'info-toast',
            });
        }
    },
    { immediate: true, deep: true },
);

// Errors
watch(
    () => page.props.errors,
    (errors: any) => {
        if (errors && Object.keys(errors).length > 0) {
            for (const key in errors) {
                toast.error(errors[key]);
            }
        }
    },
    { immediate: true, deep: true },
);

const handlePlainTextNotification = (notification: any) => {
    let icon = AlertCircle;
    const message = notification.title ?? '提示';
    const description = notification.message ?? '未知消息';

    switch (notification.event) {
        case 'success':
            icon = CheckCircle;
            break;
    }

    toast.success(message, {
        icon: icon,
        description: description,
        class: notification.event + '-toast',
    });
};
</script>
