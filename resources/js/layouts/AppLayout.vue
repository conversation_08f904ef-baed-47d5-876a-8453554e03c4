<script setup lang="ts">
import AppSidebarLayout from '@/layouts/app/AppSidebarLayout.vue';
import type { BreadcrumbItemType } from '../types';
import Base from './Base.vue';

interface Props {
    breadcrumbs?: BreadcrumbItemType[];
    hideHeader?: boolean;
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
    hideHeader: false,
});
</script>

<template>
    <Base>
        <AppSidebarLayout :breadcrumbs="breadcrumbs">
            <slot />
        </AppSidebarLayout>
    </Base>
</template>
