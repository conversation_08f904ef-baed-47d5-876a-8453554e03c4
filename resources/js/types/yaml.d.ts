import type { ComputedRef, Ref } from 'vue';

export interface YamlDocument {
    api: string;
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    [key: string]: any;
}

export interface YamlValidationResult {
    isValid: boolean;
    error?: string;
    documents?: YamlDocument[];
}

export interface ApplyResult {
    index: number;
    api: string;
    method: string;
    status: 'success' | 'error';
    data?: any;
    error?: string;
}

export interface CurrentApplyingItem {
    api: string;
    method: string;
}

export interface UseYamlApplyReturn {
    yamlContent: Ref<string>;
    isSubmitting: Ref<boolean>;
    hasWorkspace: ComputedRef<boolean>;
    currentProgress: Ref<number>;
    totalProgress: Ref<number>;
    progressPercentage: ComputedRef<number>;
    currentApplyingItem: Ref<CurrentApplyingItem | null>;
    defaultYaml: string;
    initializeDefaultYaml: () => void;
    validateYaml: (content: string) => YamlValidationResult;
    submitYaml: (onSuccess?: () => void) => Promise<void>;
}
