import { RouteParams, Router } from 'ziggy-js';

declare global {
    function route(): Router;
    function route(name: string, params?: RouteParams<typeof name> | undefined, absolute?: boolean): string;
}

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        route: typeof route;
    }
}

declare module 'vue' {
    interface ComponentCustomProperties {
        route: typeof routeFn;
        can: (permission: string) => boolean;
        is: (role: string) => boolean;
    }
}
