import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
    workspace: Workspace | null;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: NavItem[];
}

export type AppPageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
};

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at?: string;
    current_workspace_id?: number;
    current_workspace?: Workspace;
    created_at: string;
    updated_at: string;
}

export interface ClusterPricing {
    id: number;
    cluster_id: number;
    memory_price_per_gb: string;
    cpu_price_per_core: string;
    storage_price_per_gb: string;
    loadbalancer_price_per_service: string;
    billing_enabled: boolean;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface Cluster {
    id: number;
    name: string;
    server_url: string;
    created_at: string;
    updated_at: string;
    pricing?: ClusterPricing;
}

export interface Workspace {
    id: number;
    user_id: number;
    cluster_id: number;
    name: string;
    status: 'pending' | 'creating' | 'deleting' | 'active' | 'suspended' | 'failed';
    namespace: string;
    suspended_at?: string;
    suspension_reason?: string;
    overdue_amount?: string;
    last_overdue_at?: string;
    created_at: string;
    updated_at: string;
    cluster: Cluster;
}

export interface IpPool {
    name: string;
    range: string;
    description?: string;
    allocation_strategy: 'sequential' | 'random' | 'least_used';
}

export interface CreateWorkspaceData {
    name: string;
    cluster_id: number;
}

// Container related types
export interface ContainerPort {
    name?: string;
    container_port: number;
    containerPort?: number;
    protocol?: string;
}

export interface EnvVar {
    name: string;
    value: string;
}

// ConfigMap/Secret 环境变量引用
export interface EnvFromConfigMap {
    configmap_name: string;
    key?: string; // 如果为空，引用整个 ConfigMap
    env_name?: string; // 环境变量名，默认使用 key
}

export interface EnvFromSecret {
    secret_name: string;
    key?: string; // 如果为空，引用整个 Secret
    env_name?: string; // 环境变量名，默认使用 key
}

export interface VolumeMount {
    mount_path: string;
    mountPath?: string;
    name?: string;
    storage_name: string;
    sub_path?: string;
    read_only?: boolean;
}

// ConfigMap 文件挂载项
export interface ConfigMapMountItem {
    key: string;
    path: string;
}

export interface ConfigMapMount {
    configmap_name: string;
    mount_path: string;
    items?: ConfigMapMountItem[]; // 如果为空，挂载整个 ConfigMap
    default_mode?: string; // 文件权限（八进制字符串）
}

// Secret 文件挂载项
export interface SecretMountItem {
    key: string;
    path: string;
}

export interface SecretMount {
    secret_name: string;
    mount_path: string;
    items?: SecretMountItem[]; // 如果为空，挂载整个 Secret
    default_mode?: string; // 文件权限（八进制字符串）
}

export interface Resources {
    memory: number;
    cpu: number;
}

export interface Container {
    name: string;
    image: string;
    working_dir?: string; // 工作目录
    command?: string[]; // Container command
    args?: string[]; // Container args
    ports?: ContainerPort[];
    env?: EnvVar[];
    env_from_configmap?: EnvFromConfigMap[]; // 从 ConfigMap 引用的环境变量
    env_from_secret?: EnvFromSecret[]; // 从 Secret 引用的环境变量
    volume_mounts?: VolumeMount[];
    volumeMounts?: VolumeMount[];
    configmap_mounts?: ConfigMapMount[]; // ConfigMap 文件挂载
    secret_mounts?: SecretMount[]; // Secret 文件挂载
    resources?: Resources;
    // 健康检查探针
    liveness_probe?: HealthProbe;
    readiness_probe?: HealthProbe;
    startup_probe?: HealthProbe;
}

export interface VolumeClaimTemplate {
    name: string;
    size: number;
}

// Secret types
export interface Secret {
    name: string;
    created_at: string;
    resource_version: string;
    type: 'SSH' | 'BasicAuth' | 'Opaque' | 'DockerConfig' | 'TLS' | string;
    data_keys: string[];
    data_count: number;
}

// ConfigMap types
export interface ConfigMap {
    name: string;
    created_at: string;
    resource_version: string;
    data_keys: string[];
    data_count: number;
}

// Storage types
export interface Storage {
    name: string;
    created_at: string;
    resource_version: string;
    size: string;
    status: string;
    capacity: string;
    conditions: any[];
    is_bound: boolean;
    is_read_write: boolean;
    is_read_write_many: boolean;
    formatted_size: string;
}

// Deployment types
export interface Deployment {
    name: string;
    namespace: string;
    replicas: number;
    selector: Record<string, string>;
    strategy: string;
    status: string;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    created_at: string;
    updated_at: string;
}

export interface CreateDeploymentData {
    name: string;
    replicas?: number;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    imagePullSecrets?: string[];
}

// StatefulSet types
export interface StatefulSet {
    name: string;
    namespace: string;
    replicas: number;
    selector: Record<string, string>;
    serviceName: string;
    updateStrategy: string;
    status: string;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    volumeClaimTemplates?: VolumeClaimTemplate[];
    created_at: string;
    updated_at: string;
}

export interface CreateStatefulSetData {
    name: string;
    replicas?: number;
    service_name?: string;
    serviceName?: string;
    containers: Container[];
    volumes?: any[];
    image_pull_secrets?: string[];
    imagePullSecrets?: string[];
    volumeClaimTemplates?: VolumeClaimTemplate[];
}

// Service types
export interface ServicePort {
    name?: string;
    port: number;
    targetPort: number;
    protocol: string;
}

export interface Service {
    name: string;
    namespace: string;
    type: string;
    ports: ServicePort[];
    selector: Record<string, string>;
    workload_type?: 'Deployment' | 'StatefulSet';
    workload_name?: string;
    session_affinity?: string;
    external_traffic_policy?: string;
    allow_shared_ip?: boolean;
    external_ips?: string[];
    status: string;
    created_at: string;
    updated_at: string;
}

export interface CreateServiceData {
    name: string;
    type: string;
    target_workload_type: string;
    target_workload_name: string;
    ports: ServicePort[];
    session_affinity?: string;
    external_traffic_policy?: string;
    allow_shared_ip?: boolean;
    ip_pool?: string;
}

// Workload selector types
export interface WorkloadOption {
    type: 'Deployment' | 'StatefulSet';
    name: string;
    ports: Array<{
        container_name: string;
        port_name?: string;
        port: number;
        protocol: string;
    }>;
    selector: Record<string, string>;
}

// Ingress types
export interface IngressRule {
    host: string;
    http: {
        paths: IngressPath[];
    };
}

export interface IngressPath {
    path: string;
    pathType: string;
    backend: {
        service: {
            name: string;
            port: {
                number: number | undefined;
            };
        };
    };
}

export interface IngressTLS {
    hosts: string[];
    secretName?: string;
}

export interface Ingress {
    name: string;
    namespace: string;
    className: string;
    rules: IngressRule[];
    tls?: IngressTLS[];
    loadBalancerIngress?: string;
    status: string;
    createdAt: string;
    domains: string[];
    has_tls: boolean;
    tls_domains: string[];
    access_urls: string[];
    uid?: string;
    resourceVersion?: string;
}

export interface CreateIngressData {
    name: string;
    rules: IngressRule[];
    tls?: IngressTLS[];
}

// Pod types
export interface PodContainer {
    name: string;
    image: string;
    ports: ContainerPort[];
    env: EnvVar[];
    resources: Resources;
    volumeMounts: VolumeMount[];
    ready: boolean;
    restartCount: number;
    state: string;
    lastState?: any;
}

export interface PodCondition {
    type: string;
    status: string;
    reason?: string;
    message?: string;
    lastTransitionTime?: string;
}

export interface Pod {
    name: string;
    namespace: string;
    labels: Record<string, string>;
    annotations: Record<string, string>;
    status: string;
    phase: string;
    pod_ip?: string;
    host_ip?: string;
    node_name?: string;
    containers: PodContainer[];
    init_containers: PodContainer[];
    conditions: PodCondition[];
    created_at?: string;
    start_time?: string;
    deletion_timestamp?: string;
    restart_count: number;
    events?: PodEvent[];
    metrics?: PodMetrics;
}

export interface PodEvent {
    type: string;
    reason: string;
    message: string;
    first_timestamp?: string;
    last_timestamp?: string;
    count: number;
    source: string;
}

export interface PodMetrics {
    timestamp?: string;
    window?: string;
    containers: {
        name: string;
        usage: {
            cpu: string;
            memory: string;
        };
    }[];
}

// Other existing types
export type BreadcrumbItemType = BreadcrumbItem;

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
};

export type PaymentMethod = {
    identifier: string;
    name: string;
    description: string;
    enabled: boolean;
    supports_refund: boolean;
    requires_callback: boolean;
};

export type TopUpRecord = {
    id: number;
    transaction_number: string;
    amount: string;
    remaining_amount: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partial_refunded';
    payment_method: string;
    remark: string | null;
    completed_at: string | null;
    created_at: string;
    updated_at: string;
};

export type BillingRecord = {
    id: number;
    workspace_id: number;
    cluster_id: number;
    user_id: number;
    billing_start_at: string;
    billing_end_at: string;
    resource_usage: {
        memory_mi?: number;
        cpu_m?: number;
        storage_gi?: number;
        loadbalancer_count?: number;
    };
    memory_cost: string;
    cpu_cost: string;
    storage_cost: string;
    loadbalancer_cost: string;
    total_cost: string;
    status: 'pending' | 'charged' | 'failed';
    charged_at?: string;
    created_at: string;
    updated_at: string;
    workspace_name?: string;
};

export type Paginated<T> = {
    data: T[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
};

// HorizontalPodAutoscaler types
export interface HPAMetric {
    type: 'Resource' | 'Pods' | 'Object' | 'External';
    resource_name?: string;
    target_type?: 'Utilization' | 'AverageValue' | 'Value';
    target_value?: string | number;
    resource?: {
        name: string;
        target: {
            type: 'Utilization' | 'AverageValue';
            average_utilization?: number;
            average_value?: string | number;
        };
    };
    metric_name?: string;
    selector?: Record<string, any>;
    object_api_version?: string;
    object_kind?: string;
    object_name?: string;
}

export interface HPAScaleTargetRef {
    apiVersion: string;
    kind: string;
    name: string;
}

export interface HPACondition {
    type: string;
    status: string;
    reason?: string;
    message?: string;
    lastTransitionTime?: string;
}

export interface HPACurrentMetric {
    type: string;
    resource?: {
        name: string;
        current: {
            averageUtilization?: number;
            averageValue?: string;
        };
    };
}

export interface HorizontalPodAutoscaler {
    name: string;
    namespace: string;
    created_at: string;
    resource_version: string;
    scale_target_ref: HPAScaleTargetRef;
    min_replicas: number;
    max_replicas: number;
    metrics: HPAMetric[];
    behavior?: any;
    current_replicas?: number;
    desired_replicas?: number;
    current_metrics: HPACurrentMetric[];
    last_scale_time?: string;
    conditions: HPACondition[];
    status: string;
    current_cpu_utilization?: number;
    current_memory_utilization?: number;
    target_cpu_utilization?: number;
    target_memory_utilization?: number;
}

export interface CreateHPAData {
    name: string;
    scale_target_ref: {
        kind: string;
        name: string;
        api_version: string;
    };
    target_type: string;
    target_name: string;
    min_replicas: number;
    max_replicas: number;
    metrics: HPAMetric[];
    behavior?: {
        scale_up: {
            stabilization_window_seconds: number;
        };
        scale_down: {
            stabilization_window_seconds: number;
        };
    };
    [key: string]: any; // 支持 Inertia.js 表单的索引签名
}

export interface ScalableWorkload {
    kind: string;
    name: string;
    api_version: string;
    current_replicas?: number;
    ready_replicas?: number;
    status?: string;
}

// 健康检查探针类型
export interface HttpHeader {
    name: string;
    value: string;
}

export interface HealthProbe {
    type: 'http' | 'tcp' | 'exec';
    initial_delay_seconds?: number;
    period_seconds?: number;
    timeout_seconds?: number;
    success_threshold?: number;
    failure_threshold?: number;
    // HTTP 探针配置
    http_path?: string;
    http_port?: number;
    http_scheme?: 'HTTP' | 'HTTPS';
    http_headers?: HttpHeader[];
    // TCP 探针配置
    tcp_port?: number;
    // Exec 探针配置
    exec_command?: string[];
}

export interface SharedData extends InertiaPageProps {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
}

interface InertiaPageProps {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            current_workspace_id?: number;
            [key: string]: any;
        };
    };
    flash?: any;
    errors?: any;

    [key: string]: any;
}

// Event types
export interface K8sEvent {
    type: string;
    reason: string;
    message: string;
    timestamp: string;
    first_timestamp: string;
    last_timestamp: string;
    count: number;
    source: string;
    involved_object?: {
        kind: string;
        name: string;
        uid: string;
    };
}

// Metrics types
export interface ContainerMetrics {
    name: string;
    usage: {
        cpu: string;
        memory: string;
    };
}

export interface PodMetrics {
    timestamp: string;
    window: string;
    containers: ContainerMetrics[];
}

export interface NamespaceMetrics {
    [podName: string]: PodMetrics;
}

// Simplified metrics for UI display
export interface SimplePodMetrics {
    cpu: string;
    memory: string;
}

// Warning status type
export interface PodWarningStatus {
    has_warnings: boolean;
    warning_events: K8sEvent[];
    warning_count: number;
}

// Enriched Pod type for UI display
// Enriched Pod type for UI display
export interface EnrichedPod {
    name: string;
    namespace: string;
    status: string;
    phase: string;
    pod_ip: string | null;
    containers: any[];
    conditions: any[];
    start_time: string | null;
    restart_count: number;
    created_at: string | null;
    events: K8sEvent[];
    metrics: SimplePodMetrics;
    warningStatus: PodWarningStatus;
    age: string;
}
