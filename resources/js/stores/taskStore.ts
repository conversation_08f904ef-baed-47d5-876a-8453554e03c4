import axios from '@/lib/axios';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';

export interface Task {
    id: string;
    type: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress?: number;
    message?: string;
    created_at: string;
    updated_at: string;
    workspace_id: string;
}

export interface TaskStats {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
}

export const useTaskStore = defineStore('task', () => {
    const tasks = ref<Task[]>([]);
    const stats = ref<TaskStats>({
        total: 0,
        pending: 0,
        running: 0,
        completed: 0,
        failed: 0,
        cancelled: 0,
    });
    const loading = ref(false);
    const error = ref<string | null>(null);
    const updateInterval = ref<number | null>(null);

    // 计算属性
    const activeTasks = computed(() => {
        if (!Array.isArray(tasks.value)) return [];
        return tasks.value.filter((task) => task.status === 'pending' || task.status === 'running');
    });

    const hasActiveTasks = computed(() => activeTasks.value.length > 0);

    const recentTasks = computed(() => {
        if (!Array.isArray(tasks.value)) return [];
        return tasks.value
            .slice()
            .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
            .slice(0, 10);
    });

    // 获取当前工作空间ID
    const getCurrentWorkspaceId = (): string | null => {
        const workspaceStore = useWorkspaceStore();
        return workspaceStore.currentWorkspace?.id?.toString() || null;
    };

    // 获取任务列表
    const fetchTasks = async (options: { background?: boolean } = {}) => {
        const workspaceId = getCurrentWorkspaceId();
        if (!workspaceId) {
            tasks.value = [];
            return;
        }

        try {
            if (!options.background) {
                loading.value = true;
            }
            error.value = null;
            const response = await axios.get('/api/tasks');
            // 确保返回的数据是数组
            if (response.data && Array.isArray(response.data.data)) {
                tasks.value = response.data.data.map((t: any) => ({ ...t, message: t.error_message }));
            } else {
                tasks.value = [];
            }
        } catch (err: any) {
            error.value = err.response?.data?.message || '获取任务列表失败';
            console.error('获取任务列表失败:', err);
            tasks.value = []; // 确保在错误时也是数组
        } finally {
            if (!options.background) {
                loading.value = false;
            }
        }
    };

    // 获取任务统计
    const fetchStats = async () => {
        const workspaceId = getCurrentWorkspaceId();
        if (!workspaceId) {
            stats.value = {
                total: 0,
                pending: 0,
                running: 0,
                completed: 0,
                failed: 0,
                cancelled: 0,
            };
            return;
        }

        try {
            const response = await axios.get('/api/tasks-stats');
            stats.value = response.data;
        } catch (err: any) {
            console.error('获取任务统计失败:', err);
        }
    };

    // 获取单个任务详情
    const fetchTask = async (taskId: string) => {
        try {
            const response = await axios.get(`/api/tasks/${taskId}`);
            const apiTask = response.data.data;
            const updatedTask = { ...apiTask, message: apiTask.error_message };

            // 更新任务列表中的对应任务
            if (Array.isArray(tasks.value)) {
                const index = tasks.value.findIndex((task) => task.id === updatedTask.id);
                if (index !== -1) {
                    tasks.value[index] = updatedTask;
                }
            }

            return updatedTask;
        } catch (err: any) {
            console.error('获取任务详情失败:', err);
            throw err;
        }
    };

    // 重试任务
    const retryTask = async (taskId: string) => {
        try {
            await axios.post(`/api/tasks/${taskId}/retry`);
            await fetchTask(taskId);
            await fetchStats();
        } catch (err: any) {
            error.value = err.response?.data?.message || '重试任务失败';
            throw err;
        }
    };

    // 取消任务
    const cancelTask = async (taskId: string) => {
        try {
            await axios.post(`/api/tasks/${taskId}/cancel`);
            await fetchTask(taskId);
            await fetchStats();
        } catch (err: any) {
            error.value = err.response?.data?.message || '取消任务失败';
            throw err;
        }
    };

    // 刷新数据
    const refreshData = () => {
        const workspaceId = getCurrentWorkspaceId();
        if (workspaceId) {
            fetchTasks();
            fetchStats();
        } else {
            // 如果没有工作空间，清空数据
            tasks.value = [];
            stats.value = {
                total: 0,
                pending: 0,
                running: 0,
                completed: 0,
                failed: 0,
                cancelled: 0,
            };
        }
    };

    // 开始自动更新
    const startAutoUpdate = () => {
        if (updateInterval.value) {
            clearInterval(updateInterval.value);
        }

        updateInterval.value = window.setInterval(() => {
            if (getCurrentWorkspaceId() && hasActiveTasks.value) {
                fetchTasks({ background: true });
                fetchStats();
            }
        }, 3000); // 每3秒更新一次
    };

    // 停止自动更新
    const stopAutoUpdate = () => {
        if (updateInterval.value) {
            clearInterval(updateInterval.value);
            updateInterval.value = null;
        }
    };

    // 初始化
    const initialize = () => {
        refreshData();
        startAutoUpdate();
    };

    // 清理
    const cleanup = () => {
        stopAutoUpdate();
        tasks.value = [];
        stats.value = {
            total: 0,
            pending: 0,
            running: 0,
            completed: 0,
            failed: 0,
            cancelled: 0,
        };
        error.value = null;
    };

    const workspaceStore = useWorkspaceStore();
    if (typeof window !== 'undefined') {
        watch(
            () => workspaceStore.currentWorkspace,
            (newWorkspace, oldWorkspace) => {
                if (newWorkspace?.id !== oldWorkspace?.id) {
                    if (newWorkspace) {
                        initialize();
                    } else {
                        cleanup();
                    }
                }
            },
            { deep: true },
        );

        // Initial load
        if (workspaceStore.currentWorkspace) {
            initialize();
        }
    }

    return {
        // 状态
        tasks,
        stats,
        loading,
        error,

        // 计算属性
        activeTasks,
        hasActiveTasks,
        recentTasks,

        // 方法
        fetchTasks,
        fetchStats,
        fetchTask,
        retryTask,
        cancelTask,
        refreshData,
        startAutoUpdate,
        stopAutoUpdate,
        initialize,
        cleanup,
        getCurrentWorkspaceId,
    };
});
