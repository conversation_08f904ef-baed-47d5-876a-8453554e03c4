/// <reference types="../types/globals" />
import axios from '@/lib/axios';
import { defineStore } from 'pinia';
import type { Workspace } from '../types';

interface WorkspaceState {
    workspaces: Workspace[];
    isLoaded: boolean;
    currentWorkspace: Workspace | null;
}

export const useWorkspaceStore = defineStore('workspace', {
    state: (): WorkspaceState => ({
        workspaces: [],
        isLoaded: false,
        currentWorkspace: null,
    }),

    actions: {
        async fetchWorkspaces() {
            // 如果已经加载过，不再重复请求
            if (this.isLoaded) {
                return;
            }

            // 检查用户是否已登录
            if (!window.App || !window.App.user) {
                return;
            }

            try {
                const response = await axios.get('/api/workspaces');

                if (response.data) {
                    this.workspaces = response.data.data;
                    this.currentWorkspace = response.data.currentWorkspace;
                }

                this.isLoaded = true;
            } catch (error) {
                console.error('获取工作空间数据失败:', error);
            }
        },

        switchWorkspace(workspaceId: number) {
            return new Promise((resolve, reject) => {
                axios
                    .post(`/api/workspaces/${workspaceId}/set-current`)
                    .then((response) => {
                        if (response.data) {
                            this.currentWorkspace = response.data.data;

                            // 切换成功，让 Inertia 处理页面状态更新
                            resolve(true);
                        }
                    })
                    .catch((error) => {
                        console.error('切换工作空间失败:', error);
                        reject(new Error('切换工作空间失败'));
                    });
            });
        },
    },
});
