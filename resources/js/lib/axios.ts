import axios from 'axios';
import eventBus from './eventBus';

// 设置基础 URL
axios.defaults.baseURL = window.location.origin;

// 设置默认头部
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.withCredentials = true;

// 获取 CSRF token
const token = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
}

// 请求拦截器 - 确保在 API 请求前获取 CSRF cookie
axios.interceptors.request.use(async (config) => {
    // 如果是 API 请求且没有 CSRF token，先获取 CSRF cookie
    if (config.url?.startsWith('/api/') && !axios.defaults.headers.common['X-CSRF-TOKEN']) {
        await axios.get('/sanctum/csrf-cookie');
    }
    return config;
});

// 响应拦截器
axios.interceptors.response.use(
    (response) => response,
    (error) => {
        eventBus.emit('api:error', {
            message: error.response?.data?.message || '请求失败',
            errors: error.response?.data?.errors,
        });
        return Promise.reject(error);
    },
);

export default axios;
