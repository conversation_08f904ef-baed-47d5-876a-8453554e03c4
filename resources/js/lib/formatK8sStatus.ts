export const formatK8sStatus = (status: string) => {
    switch (status) {
        case 'Running':
            return '运行中';
        case 'Available':
            return '可用';
        case 'Unavailable':
            return '不可用';
        case 'Partially Ready':
            return '部分就绪';
        case 'Pending':
            return '等待中';
        case 'Degraded':
            return '降级';
        case 'Progressing':
            return '部署中';
        case 'Updating':
            return '更新中';
        case 'Failed':
            return '失败';
        case 'Terminating':
            return '删除中';
        case 'Unknown':
            return '未知';
        default:
            return status;
    }
};

export const formatPodStatus = (status: string) => {
    switch (status) {
        case 'Not Ready':
            return '未就绪';
        case 'Ready':
            return '就绪';
        case 'Not Ready':
            return '未就绪';
        case 'Partially Ready':
            return '部分就绪';
        case 'Pending':
            return '等待中';
        case 'Running':
            return '运行中';
        case 'Succeeded':
            return '成功';
        case 'Failed':
            return '失败';
        case 'Unknown':
            return '未知';
        case 'Terminating':
            return '删除中';
        case 'CrashLoopBackOff':
            return '循环崩溃';
        default:
            return status;
    }
};

export const formatRollingUpdateStatus = (status: string) => {
    switch (status) {
        case 'RollingUpdate':
            return '滚动更新';
        default:
            return status;
    }
};

export const formatServiceType = (type: string) => {
    switch (type) {
        case 'LoadBalancer':
            return '负载均衡器';
        case 'ClusterIP':
            return '集群内部';
        case 'NodePort':
            return '外部端口';
        default:
            return type;
    }
};

export const formatWorkloadType = (type: string) => {
    switch (type) {
        case 'Deployment':
            return '无状态集';
        case 'StatefulSet':
            return '有状态集';
        case 'Job':
            return '任务';
        case 'CronJob':
            return '定时任务';
        case 'DaemonSet':
            return '守护进程集';
        case 'ReplicaSet':
        default:
            return type;
    }
};

export const formatStrategy = (strategy: string) => {
    switch (strategy) {
        case 'RollingUpdate':
            return '滚动更新';
        default:
            return strategy;
    }
};

export const formatIngressStatus = (status: string) => {
    switch (status) {
        case 'Ready':
            return '就绪';
        case 'Not Ready':
            return '未就绪';
        case 'Pending':
            return '等待中';
        case 'Failed':
            return '失败';
        case 'Terminating':
            return '删除中';
        case 'Unknown':
            return '未知';
        default:
            return status;
    }
};
