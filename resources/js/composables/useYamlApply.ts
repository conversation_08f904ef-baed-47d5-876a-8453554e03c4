import axios from '@/lib/axios';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import type { ApplyResult, CurrentApplyingItem, UseYamlApplyReturn, YamlDocument, YamlValidationResult } from '@/types/yaml';
import * as yaml from 'js-yaml';
import { computed, ref, type ComputedRef, type Ref } from 'vue';
import { toast } from 'vue-sonner';

export function useYamlApply(): UseYamlApplyReturn {
    const workspaceStore = useWorkspaceStore();
    const yamlContent: Ref<string> = ref('');
    const isSubmitting: Ref<boolean> = ref(false);
    const currentProgress: Ref<number> = ref(0);
    const totalProgress: Ref<number> = ref(0);
    const currentApplyingItem: Ref<CurrentApplyingItem | null> = ref(null);

    // 计算属性：判断是否有工作空间
    const hasWorkspace: ComputedRef<boolean> = computed(() => !!workspaceStore.currentWorkspace);

    // 计算进度百分比
    const progressPercentage: ComputedRef<number> = computed(() => {
        if (totalProgress.value === 0) return 0;
        return Math.round((currentProgress.value / totalProgress.value) * 100);
    });

    // 默认 YAML 模板
    const defaultYaml: string = `api: /api/deployments
method: POST
name: 'my-app'
replicas: 1
containers:
  - name: 'app'
    image: 'nginx:latest'
    working_dir: ''
    command: []
    args: []
    ports:
      - container_port: 80
        protocol: TCP
    env: []
    env_from_configmap: []
    env_from_secret: []
    volume_mounts: []
    configmap_mounts: []
    secret_mounts: []
    resources:
      memory: 512
      cpu: 500
image_pull_secrets: []
---
api: /api/statefulsets
method: POST
name: 'my-statefulset'
replicas: 1
containers:
  - name: 'app'
    image: 'nginx:latest'
    working_dir: ''
    command: []
    args: []
    ports:
      - container_port: 80
        protocol: TCP
    env: []
    env_from_configmap: []
    env_from_secret: []
    volume_mounts: []
    configmap_mounts: []
    secret_mounts: []
    resources:
      memory: 512
      cpu: 500
image_pull_secrets: []`;

    // 初始化时设置默认值
    const initializeDefaultYaml = (): void => {
        if (!yamlContent.value) {
            yamlContent.value = defaultYaml;
        }
    };

    // 重置进度
    const resetProgress = (): void => {
        currentProgress.value = 0;
        totalProgress.value = 0;
        currentApplyingItem.value = null;
    };

    // 验证 YAML 格式
    const validateYaml = (content: string): YamlValidationResult => {
        try {
            const documents = yaml.loadAll(content) as YamlDocument[];

            if (!documents || documents.length === 0) {
                return { isValid: false, error: 'YAML 内容不能为空' };
            }

            // 验证每个文档是否有必需的字段
            for (let i = 0; i < documents.length; i++) {
                const doc = documents[i];
                if (!doc || typeof doc !== 'object') {
                    return { isValid: false, error: `第 ${i + 1} 个文档格式不正确` };
                }

                if (!doc.api || !doc.method) {
                    return { isValid: false, error: `第 ${i + 1} 个文档缺少必需的 api 或 method 字段` };
                }

                // 验证 HTTP 方法
                const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
                if (!validMethods.includes(doc.method.toUpperCase())) {
                    return { isValid: false, error: `第 ${i + 1} 个文档的 method 字段值无效: ${doc.method}` };
                }
            }

            return { isValid: true, documents };
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            return { isValid: false, error: `YAML 解析错误: ${errorMessage}` };
        }
    };

    // 构建请求参数
    const buildRequestParams = (data: YamlDocument): Record<string, any> => {
        // 移除 api 和 method 字段，其余作为请求参数
        const { api, method, ...params } = data;
        return params;
    };

    // 提交 YAML
    const submitYaml = async (onSuccess?: () => void): Promise<void> => {
        if (!hasWorkspace.value) {
            toast.error('请先选择工作空间');
            return;
        }

        if (!yamlContent.value.trim()) {
            toast.error('请输入 YAML 内容');
            return;
        }

        const validation = validateYaml(yamlContent.value);
        if (!validation.isValid) {
            toast.error(validation.error || 'YAML 格式错误');
            return;
        }

        isSubmitting.value = true;
        const documents = validation.documents!;
        totalProgress.value = documents.length;
        currentProgress.value = 0;

        try {
            const results: ApplyResult[] = [];

            for (let i = 0; i < documents.length; i++) {
                const doc = documents[i];
                const { api, method } = doc;
                const params = buildRequestParams(doc);

                // 更新当前正在处理的项目
                currentApplyingItem.value = { api, method };

                try {
                    let response;

                    const upperMethod = method.toUpperCase();
                    switch (upperMethod) {
                        case 'GET':
                            response = await axios.get(api, { params });
                            break;
                        case 'POST':
                            response = await axios.post(api, params);
                            break;
                        case 'PUT':
                            response = await axios.put(api, params);
                            break;
                        case 'PATCH':
                            response = await axios.patch(api, params);
                            break;
                        case 'DELETE':
                            response = await axios.delete(api, { params });
                            break;
                        default:
                            throw new Error(`不支持的 HTTP 方法: ${method}`);
                    }

                    results.push({
                        index: i + 1,
                        api,
                        method,
                        status: 'success',
                        data: response.data,
                    });
                } catch (error: unknown) {
                    const errorMessage = error instanceof Error ? error.message : (error as any)?.response?.data?.message || '未知错误';

                    results.push({
                        index: i + 1,
                        api,
                        method,
                        status: 'error',
                        error: errorMessage,
                    });
                }

                // 更新进度
                currentProgress.value = i + 1;
            }

            // 清除当前处理项目
            currentApplyingItem.value = null;

            // 显示结果
            const successCount = results.filter((r) => r.status === 'success').length;
            const errorCount = results.filter((r) => r.status === 'error').length;

            if (errorCount === 0) {
                toast.success(`所有 ${successCount} 个请求都执行成功`);
                onSuccess?.();
            } else if (successCount === 0) {
                toast.error(`所有 ${errorCount} 个请求都执行失败`);
            } else {
                toast.warning(`${successCount} 个成功，${errorCount} 个失败`);
            }

            // 显示详细错误信息
            results
                .filter((r) => r.status === 'error')
                .forEach((result) => {
                    toast.error(`请求 ${result.index} (${result.method} ${result.api}) 失败: ${result.error}`);
                });
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            toast.error('提交失败: ' + errorMessage);
        } finally {
            isSubmitting.value = false;
            resetProgress();
        }
    };

    return {
        yamlContent,
        isSubmitting,
        hasWorkspace,
        currentProgress,
        totalProgress,
        progressPercentage,
        currentApplyingItem,
        defaultYaml,
        initializeDefaultYaml,
        validateYaml,
        submitYaml,
    };
}
