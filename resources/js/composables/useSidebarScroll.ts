import { router } from '@inertiajs/vue3';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';

/**
 * A Vue composable to remember the scroll position of the app sidebar.
 * It uses the browser's history state to avoid page flicker on navigation.
 */
export function useSidebarScroll() {
    const scrollAreaRef = ref<any>(null);
    let scrollContainer: HTMLElement | null = null;

    /**
     * Finds the scrollable viewport element within the ScrollArea component.
     */
    const getScrollContainer = (): HTMLElement | null => {
        if (!scrollAreaRef.value) return null;
        // The scrollable element is the direct child of the component's root $el.
        return scrollAreaRef.value.$el?.querySelector('[data-slot="scroll-area-viewport"]') ?? null;
    };

    /**
     * Saves the current scroll position to the browser's history state.
     */
    const saveScrollPosition = () => {
        if (scrollContainer) {
            history.replaceState({ ...history.state, sidebarScroll: scrollContainer.scrollTop }, '');
        }
    };

    /**
     * Restores the scroll position from the browser's history state.
     */
    const restoreScrollPosition = () => {
        if (scrollContainer && history.state.sidebarScroll) {
            scrollContainer.scrollTop = history.state.sidebarScroll;
        }
    };

    /**
     * Sets up the scroll container and restores its position.
     */
    const setup = () => {
        scrollContainer = getScrollContainer();
        restoreScrollPosition();
    };

    // --- Lifecycle and Event Listeners ---

    onMounted(() => {
        // We need to wait for the DOM to be updated after the component is mounted.
        nextTick(setup);
    });

    // Re-run setup if the component ref changes, which happens on navigation.
    watch(scrollAreaRef, setup);

    // Before a new visit starts, save the current position.
    const removeBeforeListener = router.on('before', saveScrollPosition);

    // After a visit finishes, restore the new page's saved position.
    const removeFinishListener = router.on('finish', restoreScrollPosition);

    // On component unmount, save one last time and clean up event listeners.
    onBeforeUnmount(() => {
        saveScrollPosition();
        removeBeforeListener();
        removeFinishListener();
    });

    return {
        scrollAreaRef,
    };
}
