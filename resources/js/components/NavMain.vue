<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Link } from '@inertiajs/vue3';
import { Box, Container, Database, FileText, Globe, HardDrive, Key, LayoutGrid, Server, Upload, Zap } from 'lucide-vue-next';

const isActive = (path: string) => {
    if (typeof path === 'string') {
        return window.location.href.startsWith(path);
    }
    return false;
};
</script>

<template>
    <!-- 基础部分 -->
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>基础</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive('/dashboard')" tooltip="仪表盘">
                    <Link href="/dashboard">
                        <LayoutGrid class="mr-2 h-4 w-4" />
                        <span>仪表盘</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive('/workspaces')" tooltip="工作区">
                    <Link href="/workspaces">
                        <Server class="mr-2 h-4 w-4" />
                        <span>工作区</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('apply.index'))" tooltip="YAML 应用">
                    <Link :href="route('apply.index')">
                        <Upload class="mr-2 h-4 w-4" />
                        <span>部署清单</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>

    <!-- 工作负载 -->
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>工作负载</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('deployments.index'))" tooltip="应用管理">
                    <Link :href="route('deployments.index')">
                        <Container class="mr-2 h-4 w-4" />
                        <span>无状态</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('statefulsets.index'))" tooltip="有状态应用">
                    <Link :href="route('statefulsets.index')">
                        <Database class="mr-2 h-4 w-4" />
                        <span>有状态</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('pods.index'))" tooltip="容器管理">
                    <Link :href="route('pods.index')">
                        <Box class="mr-2 h-4 w-4" />
                        <span>Pod 管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('hpas.index'))" tooltip="自动扩缩容">
                    <Link :href="route('hpas.index')">
                        <Zap class="mr-2 h-4 w-4" />
                        <span>自动扩缩</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>

    <!-- 配置 -->
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>配置</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('configmaps.index'))" tooltip="配置管理">
                    <Link :href="route('configmaps.index')">
                        <FileText class="mr-2 h-4 w-4" />
                        <span>配置管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('secrets.index'))" tooltip="密钥管理">
                    <Link :href="route('secrets.index')">
                        <Key class="mr-2 h-4 w-4" />
                        <span>密钥管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>

    <!-- 网络 -->
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>网络</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('services.index'))" tooltip="服务管理">
                    <Link :href="route('services.index')">
                        <Server class="mr-2 h-4 w-4" />
                        <span>服务管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('ingresses.index'))" tooltip="入口管理">
                    <Link :href="route('ingresses.index')">
                        <Globe class="mr-2 h-4 w-4" />
                        <span>入口管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>

    <!-- 存储 -->
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>存储</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem>
                <SidebarMenuButton as-child :is-active="isActive(route('storages.index'))" tooltip="存储管理">
                    <Link :href="route('storages.index')">
                        <HardDrive class="mr-2 h-4 w-4" />
                        <span>存储管理</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>
</template>
