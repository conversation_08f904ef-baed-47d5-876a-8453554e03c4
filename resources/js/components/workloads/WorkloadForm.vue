<template>
    <div class="grid grid-cols-1 gap-8 md:grid-cols-[280px_1fr]">
        <!-- Left Navigation -->
        <div class="flex flex-col space-y-4">
            <Button variant="outline" @click="refreshAllSelectors" :disabled="refreshing">
                <RefreshCw v-if="!refreshing" class="mr-2 h-4 w-4" />
                <Loader2 v-else class="mr-2 h-4 w-4 animate-spin" />
                刷新资源
            </Button>
            <!-- Main Navigation -->
            <div class="space-y-1">
                <Button
                    :variant="activeSection === 'general' ? 'secondary' : 'ghost'"
                    class="w-full justify-start"
                    @click="activeSection = 'general'"
                >
                    基本配置
                </Button>
                <Button
                    :variant="activeSection === 'containers' ? 'secondary' : 'ghost'"
                    class="w-full justify-start"
                    @click="activeSection = 'containers'"
                >
                    容器配置
                </Button>
            </div>

            <!-- Container Sub-navigation -->
            <div v-if="activeSection === 'containers'" class="space-y-2 border-t pt-4">
                <h4 class="px-3 text-sm font-medium">容器列表</h4>
                <div v-for="(container, containerIndex) in modelValue.containers" :key="containerIndex" class="space-y-1">
                    <Button
                        :variant="activeContainerIndex === containerIndex ? 'secondary' : 'ghost'"
                        class="w-full justify-start"
                        @click="activeContainerIndex = containerIndex"
                    >
                        {{ container.name || `容器 ${containerIndex + 1}` }}
                    </Button>
                </div>
                <Button variant="outline" size="sm" class="w-full" @click="addContainer">
                    <Plus class="mr-2 h-4 w-4" />
                    添加容器
                </Button>
            </div>
        </div>

        <!-- Right Content -->
        <div class="overflow-hidden">
            <!-- General Settings -->
            <div v-if="activeSection === 'general'" class="space-y-6">
                <Card>
                    <CardHeader>
                        <CardTitle>基本配置</CardTitle>
                        <CardDescription>设置工作负载的基本信息</CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-6">
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                            <div>
                                <Label for="name">名称 *</Label>
                                <Input
                                    id="name"
                                    :model-value="modelValue.name"
                                    @update:model-value="emitFieldUpdate('name', $event)"
                                    placeholder="例如：my-app"
                                    class="mt-1"
                                    :disabled="isEdit"
                                />
                                <p class="mt-1 text-xs text-gray-500">只能包含小写字母、数字和连字符</p>
                            </div>
                            <div>
                                <Label for="replicas">副本数 *</Label>
                                <Input
                                    id="replicas"
                                    :model-value="modelValue.replicas"
                                    @update:model-value="emitFieldUpdate('replicas', Number($event))"
                                    type="number"
                                    min="0"
                                    max="100"
                                    class="mt-1"
                                />
                            </div>
                            <div v-if="workloadType === 'statefulset'">
                                <Label for="service_name">服务名称 (可选)</Label>
                                <Input
                                    id="service_name"
                                    :model-value="(modelValue as CreateStatefulSetData).service_name"
                                    @update:model-value="emitFieldUpdate('service_name', $event)"
                                    placeholder="留空将自动创建 headless 服务"
                                    class="mt-1"
                                />
                            </div>
                        </div>

                        <SecretSelector
                            ref="secretSelectorRef"
                            :model-value="modelValue.image_pull_secrets!"
                            @update:model-value="emitFieldUpdate('image_pull_secrets', $event)"
                            label="拉取密钥 (可选)"
                            secret-type="DockerConfig"
                            add-button-text="添加拉取密钥"
                        />
                    </CardContent>
                </Card>
            </div>

            <!-- Container Settings -->
            <div v-if="activeSection === 'containers' && modelValue.containers[activeContainerIndex]">
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between">
                        <div>
                            <CardTitle>容器 {{ activeContainerIndex + 1 }} 配置</CardTitle>
                            <CardDescription>为选定的容器进行详细配置</CardDescription>
                        </div>
                        <Button
                            v-if="modelValue.containers.length > 1"
                            variant="destructive"
                            size="sm"
                            @click="removeContainer(activeContainerIndex)"
                        >
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除容器
                        </Button>
                    </CardHeader>
                    <CardContent>
                        <Tabs default-value="general">
                            <TabsList class="grid w-full grid-cols-5">
                                <TabsTrigger value="general">常规</TabsTrigger>
                                <TabsTrigger value="networking">网络</TabsTrigger>
                                <TabsTrigger value="environment">环境</TabsTrigger>
                                <TabsTrigger value="probes">健康检查</TabsTrigger>
                                <TabsTrigger value="storage">存储</TabsTrigger>
                            </TabsList>
                            <TabsContent value="general" class="mt-6">
                                <div class="space-y-6">
                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label>容器名称 *</Label>
                                            <Input
                                                :model-value="container.name"
                                                @update:model-value="updateContainerField(activeContainerIndex, 'name', $event)"
                                                placeholder="例如：nginx"
                                                class="mt-1"
                                            />
                                        </div>
                                        <div>
                                            <Label>镜像 *</Label>
                                            <Input
                                                :model-value="container.image"
                                                @update:model-value="updateContainerField(activeContainerIndex, 'image', $event)"
                                                placeholder="例如：nginx:latest"
                                                class="mt-1"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <Label>工作目录 (可选)</Label>
                                        <Input
                                            :model-value="container.working_dir"
                                            @update:model-value="updateContainerField(activeContainerIndex, 'working_dir', $event)"
                                            placeholder="例如：/app"
                                            class="mt-1"
                                        />
                                        <p class="mt-1 text-xs text-gray-500">覆盖镜像中的默认工作目录</p>
                                    </div>
                                    <CommandSelector
                                        :model-value="{ command: container.command || [], args: container.args || [] }"
                                        @update:model-value="
                                            (value) => {
                                                updateContainerField(activeContainerIndex, 'command', value.command);
                                                updateContainerField(activeContainerIndex, 'args', value.args);
                                            }
                                        "
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent value="networking" class="mt-6">
                                <PortSelector v-model="container.ports!" mode="container" label="端口配置" />
                                <div
                                    v-if="!container.ports || container.ports.length === 0"
                                    class="mt-2 flex items-center text-sm text-yellow-600 dark:text-yellow-400"
                                >
                                    <AlertTriangle class="mr-2 h-4 w-4" />
                                    <span>警告：未添加任何端口，您将无法访问此容器。</span>
                                </div>
                                <div v-else class="mt-2 flex items-center text-sm text-blue-600 dark:text-blue-400">
                                    <Info class="mr-2 h-4 w-4" />
                                    <span>提示：请记得到「服务」页面创建服务来暴露端口。</span>
                                </div>
                            </TabsContent>
                            <TabsContent value="environment" class="mt-6 space-y-6">
                                <EnvSelector v-model="container.env!" label="环境变量" />
                                <EnvFromSelector
                                    ref="envFromSelectorRef"
                                    :model-value="{
                                        configmap: container.env_from_configmap || [],
                                        secret: container.env_from_secret || [],
                                    }"
                                    @update:model-value="
                                        (value) => {
                                            updateContainerField(activeContainerIndex, 'env_from_configmap', value.configmap);
                                            updateContainerField(activeContainerIndex, 'env_from_secret', value.secret);
                                        }
                                    "
                                />
                            </TabsContent>
                            <TabsContent value="probes" class="mt-6 space-y-6">
                                <ResourceSelector v-model="container.resources!" label="资源限制" />
                                <div class="space-y-4">
                                    <h5 class="font-medium text-gray-900 dark:text-white">健康检查探针</h5>
                                    <HealthProbeSelector v-model="container.liveness_probe" label="存活探针 (Liveness Probe)" probe-type="liveness" />
                                    <HealthProbeSelector
                                        v-model="container.readiness_probe"
                                        label="就绪探针 (Readiness Probe)"
                                        probe-type="readiness"
                                    />
                                    <HealthProbeSelector v-model="container.startup_probe" label="启动探针 (Startup Probe)" probe-type="startup" />
                                    <div class="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
                                        <div class="flex">
                                            <Info class="h-5 w-5 text-blue-400" />
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">探针说明</h3>
                                                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                                    <ul class="list-inside list-disc space-y-1">
                                                        <li><strong>存活探针：</strong>检测容器是否正在运行，失败时重启容器</li>
                                                        <li><strong>就绪探针：</strong>检测容器是否准备好接收流量</li>
                                                        <li><strong>启动探针：</strong>检测容器是否已成功启动，适用于启动缓慢的应用</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                            <TabsContent value="storage" class="mt-6 space-y-6">
                                <div>
                                    <Label>卷挂载</Label>
                                    <div class="mt-2 space-y-2">
                                        <div
                                            v-for="(mount, mountIndex) in container.volume_mounts"
                                            :key="mountIndex"
                                            class="grid grid-cols-1 items-end gap-2 md:grid-cols-4"
                                        >
                                            <div>
                                                <Label>挂载路径 *</Label>
                                                <Input
                                                    :model-value="mount.mount_path"
                                                    @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'mount_path', $event)"
                                                    placeholder="/data"
                                                />
                                            </div>
                                            <div>
                                                <StorageSelector
                                                    ref="storageSelectorRef"
                                                    :model-value="mount.storage_name"
                                                    @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'storage_name', $event)"
                                                    label="存储名称 *"
                                                    placeholder="选择存储"
                                                    only-bound
                                                />
                                            </div>
                                            <div>
                                                <Label>子路径 (可选)</Label>
                                                <Input
                                                    :model-value="mount.sub_path"
                                                    @update:model-value="updateVolumeMount(activeContainerIndex, mountIndex, 'sub_path', $event)"
                                                    placeholder="subdir"
                                                />
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <Checkbox
                                                    :id="`readonly-${activeContainerIndex}-${mountIndex}`"
                                                    :checked="mount.read_only"
                                                    @update:checked="updateVolumeMount(activeContainerIndex, mountIndex, 'read_only', $event)"
                                                />
                                                <Label :for="`readonly-${activeContainerIndex}-${mountIndex}`">只读</Label>
                                                <Button variant="outline" size="sm" @click="removeVolumeMount(activeContainerIndex, mountIndex)">
                                                    <X class="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <Button variant="outline" size="sm" @click="addVolumeMount(activeContainerIndex)">
                                            <Plus class="mr-2 h-4 w-4" />
                                            添加卷挂载
                                        </Button>
                                    </div>
                                    <div
                                        v-if="!container.volume_mounts || container.volume_mounts.length === 0"
                                        class="mt-2 flex items-center text-sm text-yellow-600 dark:text-yellow-400"
                                    >
                                        <AlertTriangle class="mr-2 h-4 w-4" />
                                        <span>警告：未挂载存储卷，容器重启后数据将会丢失。</span>
                                    </div>
                                </div>
                                <FileMountSelector
                                    ref="fileMountSelectorRef"
                                    :model-value="{
                                        configmap_mounts: container.configmap_mounts || [],
                                        secret_mounts: container.secret_mounts || [],
                                    }"
                                    @update:model-value="
                                        (value) => {
                                            updateContainerField(activeContainerIndex, 'configmap_mounts', value.configmap_mounts);
                                            updateContainerField(activeContainerIndex, 'secret_mounts', value.secret_mounts);
                                        }
                                    "
                                />
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>
            </div>
            <!-- Placeholder for when no container is selected -->
            <div v-else-if="activeSection === 'containers'" class="flex h-full items-center justify-center rounded-lg border-2 border-dashed">
                <div class="text-center">
                    <p class="text-gray-500">请从左侧选择一个容器进行配置，或添加一个新容器。</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import CommandSelector from '@/components/selectors/CommandSelector.vue';
import EnvFromSelector from '@/components/selectors/EnvFromSelector.vue';
import EnvSelector from '@/components/selectors/EnvSelector.vue';
import FileMountSelector from '@/components/selectors/FileMountSelector.vue';
import HealthProbeSelector from '@/components/selectors/HealthProbeSelector.vue';
import PortSelector from '@/components/selectors/PortSelector.vue';
import ResourceSelector from '@/components/selectors/ResourceSelector.vue';
import SecretSelector from '@/components/selectors/SecretSelector.vue';
import StorageSelector from '@/components/selectors/StorageSelector.vue';
import type { Container, CreateDeploymentData, CreateStatefulSetData } from '@/types';
import { AlertTriangle, Info, Loader2, Plus, RefreshCw, Trash2, X } from 'lucide-vue-next';
import { computed, ref, toRefs } from 'vue';

type WorkloadData = CreateDeploymentData | CreateStatefulSetData;

interface Props {
    modelValue: WorkloadData;
    workloadType: 'deployment' | 'statefulset';
    isEdit?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    isEdit: false,
});

const emit = defineEmits(['update:modelValue']);

const { modelValue, workloadType, isEdit } = toRefs(props);

const activeSection = ref<'general' | 'containers'>('general');
const activeContainerIndex = ref(0);

const secretSelectorRef = ref<InstanceType<typeof SecretSelector> | null>(null);
const storageSelectorRef = ref<InstanceType<typeof StorageSelector> | null>(null);
const envFromSelectorRef = ref<InstanceType<typeof EnvFromSelector> | null>(null);
const fileMountSelectorRef = ref<InstanceType<typeof FileMountSelector> | null>(null);

const container = computed<Container>(() => {
    return modelValue.value.containers[activeContainerIndex.value];
});

const emitFieldUpdate = (field: string, value: any) => {
    emit('update:modelValue', { ...modelValue.value, [field]: value });
};

const updateContainerField = (containerIndex: number, field: string, value: any) => {
    const newContainers = [...modelValue.value.containers];
    (newContainers[containerIndex] as any)[field] = value;
    emitFieldUpdate('containers', newContainers);
};

const addContainer = () => {
    const newContainers = [
        ...modelValue.value.containers,
        {
            name: '',
            image: '',
            working_dir: '',
            command: [],
            args: [],
            ports: [],
            env: [],
            env_from_configmap: [],
            env_from_secret: [],
            volume_mounts: [],
            configmap_mounts: [],
            secret_mounts: [],
            resources: {
                memory: 512,
                cpu: 500,
            },
            liveness_probe: undefined,
            readiness_probe: undefined,
            startup_probe: undefined,
        },
    ];
    emitFieldUpdate('containers', newContainers);
    activeContainerIndex.value = newContainers.length - 1;
};

const removeContainer = (index: number) => {
    const newContainers = [...modelValue.value.containers];
    newContainers.splice(index, 1);
    emitFieldUpdate('containers', newContainers);

    if (activeContainerIndex.value >= newContainers.length) {
        activeContainerIndex.value = newContainers.length - 1;
    }
};

const addVolumeMount = (containerIndex: number) => {
    const newContainers = [...modelValue.value.containers];
    if (!newContainers[containerIndex].volume_mounts) {
        newContainers[containerIndex].volume_mounts = [];
    }
    newContainers[containerIndex].volume_mounts!.push({
        mount_path: '',
        storage_name: '',
        sub_path: '',
        read_only: false,
    });
    emitFieldUpdate('containers', newContainers);
};

const removeVolumeMount = (containerIndex: number, mountIndex: number) => {
    const newContainers = [...modelValue.value.containers];
    newContainers[containerIndex].volume_mounts?.splice(mountIndex, 1);
    emitFieldUpdate('containers', newContainers);
};

const updateVolumeMount = (containerIndex: number, mountIndex: number, field: string, value: any) => {
    const newContainers = [...modelValue.value.containers];
    const mount = newContainers[containerIndex].volume_mounts![mountIndex];
    (mount as any)[field] = value;
    emitFieldUpdate('containers', newContainers);
};

const refreshing = ref(false);

const refreshAllSelectors = () => {
    refreshing.value = true;
    secretSelectorRef.value?.refresh();
    storageSelectorRef.value?.refresh();
    envFromSelectorRef.value?.refresh();
    fileMountSelectorRef.value?.refresh();
    refreshing.value = false;
};
</script>
