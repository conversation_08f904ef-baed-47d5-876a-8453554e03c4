<template>
    <div class="relative h-full w-full">
        <!-- 连接状态栏 -->
        <div v-if="props.showStatusBar" class="border-b bg-muted/50 px-4 py-2">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div
                        class="h-2 w-2 rounded-full"
                        :class="{
                            'bg-green-500': connectionStatus === 'connected',
                            'bg-yellow-500': connectionStatus === 'connecting',
                            'bg-red-500': connectionStatus === 'disconnected',
                            'bg-gray-500': connectionStatus === 'closed',
                        }"
                    />
                    <span class="text-sm font-medium">
                        {{ getStatusText() }}
                    </span>
                    <Badge v-if="mode" variant="outline" class="text-xs">
                        {{ mode === 'shell' ? 'Shell' : 'Attach' }}
                    </Badge>
                    <Badge v-if="containerName" variant="secondary" class="text-xs">
                        {{ containerName }}
                    </Badge>
                </div>
                <div class="flex items-center space-x-2">
                    <Button v-if="connectionStatus === 'disconnected'" variant="outline" size="sm" @click="connect" :disabled="connecting">
                        <RotateCcw class="mr-1 h-3 w-3" />
                        重连
                    </Button>
                    <Button v-if="connectionStatus === 'connected'" variant="outline" size="sm" @click="disconnect">
                        <X class="mr-1 h-3 w-3" />
                        断开
                    </Button>
                </div>
            </div>
        </div>

        <!-- 终端区域 -->
        <div ref="terminalContainer" class="h-full w-full bg-black" :class="{ 'opacity-50': connectionStatus !== 'connected' }" />

        <!-- 加载遮罩 -->
        <div v-if="connecting" class="absolute inset-0 flex items-center justify-center bg-black/50">
            <div class="text-white">
                <div class="mb-2 text-center">
                    <div class="mx-auto h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
                </div>
                <p class="text-sm">正在连接到 Pod 终端...</p>
            </div>
        </div>

        <!-- 错误提示 -->
        <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-black/80">
            <div class="max-w-md rounded-lg bg-destructive/90 p-4 text-destructive-foreground">
                <div class="mb-2 flex items-center">
                    <AlertCircle class="mr-2 h-4 w-4" />
                    <h3 class="font-medium">连接失败</h3>
                </div>
                <p class="text-sm">{{ error }}</p>
                <Button variant="outline" size="sm" class="mt-3" @click="clearError"> 关闭 </Button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import axios from '@/lib/axios';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { Terminal } from '@xterm/xterm';
import { AlertCircle, RotateCcw, X } from 'lucide-vue-next';
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { toast } from 'vue-sonner';

// 引入 xterm 样式
import '@xterm/xterm/css/xterm.css';

interface Props {
    podName: string;
    containerName?: string;
    mode?: 'shell' | 'attach';
    autoConnect?: boolean;
    showStatusBar?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    mode: 'shell',
    autoConnect: true,
    showStatusBar: true,
});

const emit = defineEmits<{
    'connection-status-changed': [status: string];
}>();

// 响应式状态
const terminalContainer = ref<HTMLDivElement>();
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'closed'>('disconnected');
const connecting = ref(false);
const error = ref<string | null>(null);

// Terminal 和插件实例
let terminal: Terminal | null = null;
let fitAddon: FitAddon | null = null;
let websocket: WebSocket | null = null;
let reconnectTimer: number | null = null;
let heartbeatTimer: number | null = null;

// 获取状态文本
const getStatusText = () => {
    switch (connectionStatus.value) {
        case 'connecting':
            return '正在连接...';
        case 'connected':
            return '已连接';
        case 'disconnected':
            return '未连接';
        case 'closed':
            return '连接关闭';
        default:
            return '未知状态';
    }
};

// 初始化终端
const initTerminal = async () => {
    if (!terminalContainer.value) return;

    // 创建终端实例
    terminal = new Terminal({
        cursorBlink: true,
        theme: {
            background: '#000000',
            foreground: '#ffffff',
            cursor: '#ffffff',
            selectionBackground: '#3f3f3f',
        },
        fontSize: 14,
        fontFamily: 'Menlo, Monaco, "Courier New", monospace',
        rows: 24,
        cols: 80,
    });

    // 添加插件
    fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    // 打开终端
    terminal.open(terminalContainer.value);

    // 适应容器大小
    await nextTick();
    fitAddon.fit();

    // 监听窗口大小变化
    const resizeObserver = new ResizeObserver(() => {
        if (fitAddon) {
            fitAddon.fit();
            // 发送终端大小到服务器
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'resize',
                    cols: terminal?.cols || 80,
                    rows: terminal?.rows || 24,
                };
                websocket.send(JSON.stringify(message));
            }
        }
    });

    resizeObserver.observe(terminalContainer.value);

    // 监听用户输入
    terminal.onData((data) => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: 'input',
                data: data,
            };
            websocket.send(JSON.stringify(message));
        }
    });

    return () => {
        resizeObserver.disconnect();
    };
};

// 获取连接 token
const getToken = async () => {
    const response = await axios.post(`/api/pods/${props.podName}/terminal/token`, {
        pod_name: props.podName,
        container_name: props.containerName,
        mode: props.mode,
    });
    return response.data;
};

// 连接到 WebSocket
const connect = async () => {
    if (connecting.value || connectionStatus.value === 'connected') return;

    try {
        connecting.value = true;
        connectionStatus.value = 'connecting';
        error.value = null;

        // 获取 token
        const tokenData = await getToken();

        // 建立 WebSocket 连接
        websocket = new WebSocket(tokenData.websocket_url);

        websocket.onopen = () => {
            console.log('WebSocket connected');

            // 发送认证信息
            const authMessage = {
                type: 'auth',
                token: tokenData.token,
            };
            websocket!.send(JSON.stringify(authMessage));
        };

        websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            } catch (e) {
                console.error('Failed to parse WebSocket message:', e);
            }
        };

        websocket.onerror = (event) => {
            console.error('WebSocket error:', event);
            error.value = '连接错误';
            connectionStatus.value = 'disconnected';
        };

        websocket.onclose = (event) => {
            console.log('WebSocket closed:', event.code, event.reason);
            connectionStatus.value = 'closed';

            // 自动重连（如果不是手动关闭）
            if (event.code !== 1000) {
                scheduleReconnect();
            }
        };
    } catch (e) {
        console.error('Failed to connect:', e);
        error.value = `连接失败: ${e instanceof Error ? e.message : '未知错误'}`;
        connectionStatus.value = 'disconnected';
    } finally {
        connecting.value = false;
    }
};

// 处理 WebSocket 消息
const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
        case 'auth_success':
            connectionStatus.value = 'connected';
            terminal?.write('\r\n\x1b[32m✓ 连接成功\x1b[0m\r\n');
            startHeartbeat();
            break;

        case 'connected':
            connectionStatus.value = 'connected';
            break;

        case 'output':
            if (message.data && message.data.data) {
                terminal?.write(message.data.data);
            }
            break;

        case 'error':
            error.value = message.data?.message || '服务器错误';
            terminal?.write(`\r\n\x1b[31m错误: ${error.value}\x1b[0m\r\n`);
            break;

        case 'pong':
            // Server heartbeat response, ignore.
            break;

        default:
            console.log('Unknown message type:', message.type);
    }
};

// 断开连接
const disconnect = () => {
    connectionStatus.value = 'disconnected';

    if (websocket) {
        websocket.close(1000, 'User disconnected');
        websocket = null;
    }

    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }

    if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
        heartbeatTimer = null;
    }

    terminal?.write('\r\n\x1b[33m连接已断开\x1b[0m\r\n');
};

// 计划重连
const scheduleReconnect = () => {
    if (reconnectTimer) return;

    reconnectTimer = setTimeout(() => {
        reconnectTimer = null;
        if (connectionStatus.value !== 'connected' && connectionStatus.value !== 'disconnected') {
            toast({
                title: '尝试重连',
                description: '正在尝试重新连接到终端...',
            });
            connect();
        }
    }, 5000);
};

// 开始心跳
const startHeartbeat = () => {
    if (heartbeatTimer) return;

    heartbeatTimer = setInterval(() => {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({ type: 'ping' }));
        }
    }, 30000);
};

// 清除错误
const clearError = () => {
    error.value = null;
};

// 监听连接状态变化
watch(connectionStatus, (newStatus) => {
    emit('connection-status-changed', newStatus);
});

// 监听 props 变化
watch([() => props.podName, () => props.containerName, () => props.mode], () => {
    if (connectionStatus.value === 'connected') {
        disconnect();
    }
    if (props.autoConnect) {
        nextTick(() => connect());
    }
});

// 组件挂载
onMounted(async () => {
    await initTerminal();

    if (props.autoConnect) {
        await nextTick();
        connect();
    }
});

// 组件卸载
onUnmounted(() => {
    disconnect();

    if (terminal) {
        terminal.dispose();
        terminal = null;
    }
});

// 暴露方法给父组件
defineExpose({
    connect,
    disconnect,
    connectionStatus,
});
</script>
