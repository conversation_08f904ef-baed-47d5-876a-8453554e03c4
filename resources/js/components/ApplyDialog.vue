<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useYamlApply } from '@/composables/useYamlApply';
import { FileText, Loader2, Send } from 'lucide-vue-next';
import { onMounted } from 'vue';

interface Props {
    open: boolean;
}

interface Emits {
    (e: 'update:open', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const {
    yamlContent,
    isSubmitting,
    hasWorkspace,
    currentProgress,
    totalProgress,
    progressPercentage,
    currentApplyingItem,
    initializeDefaultYaml,
    submitYaml,
} = useYamlApply();

// 初始化默认 YAML
onMounted(() => {
    initializeDefaultYaml();
});

// 提交 YAML
const handleSubmit = async () => {
    await submitYaml(() => {
        // 成功后关闭对话框
        emit('update:open', false);
    });
};

// 关闭对话框
const handleClose = () => {
    emit('update:open', false);
};
</script>

<template>
    <Dialog :open="props.open" @update:open="handleClose">
        <DialogContent class="flex max-h-[100dvh] max-w-4xl flex-col">
            <DialogHeader>
                <DialogTitle class="flex items-center gap-2">
                    <FileText class="size-5" />
                    YAML 资源应用
                </DialogTitle>
                <DialogDescription>
                    {{ hasWorkspace ? '输入 YAML 配置来创建或更新资源' : '请先选择工作空间' }}
                </DialogDescription>
            </DialogHeader>

            <!-- 进度显示 -->
            <div v-if="isSubmitting" class="space-y-3 border-b pb-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-muted-foreground">应用进度</span>
                    <span class="font-medium">{{ currentProgress }} / {{ totalProgress }}</span>
                </div>
                <Progress :value="progressPercentage" class="h-2" />
                <div v-if="currentApplyingItem" class="text-sm text-muted-foreground">
                    正在处理: {{ currentApplyingItem.method }} {{ currentApplyingItem.api }}
                </div>
            </div>

            <!-- 无工作空间提示 -->
            <div v-if="!hasWorkspace" class="flex flex-1 items-center justify-center">
                <div class="text-center">
                    <FileText class="mx-auto h-12 w-12 text-muted-foreground" />
                    <p class="mt-4 text-sm text-muted-foreground">请先选择一个工作空间来应用资源</p>
                </div>
            </div>

            <!-- YAML 编辑器 -->
            <div v-else class="flex flex-1 flex-col gap-4">
                <div class="space-y-2">
                    <Label for="yaml-content">YAML 配置</Label>
                    <div class="text-xs text-muted-foreground">
                        使用 <code>---</code> 分隔多个资源配置。每个配置必须包含 <code>api</code> 和 <code>method</code> 字段。
                    </div>
                </div>

                <ScrollArea class="flex-1 rounded-md border">
                    <textarea
                        id="yaml-content"
                        v-model="yamlContent"
                        class="h-full min-h-[400px] w-full resize-none border-0 bg-transparent p-4 font-mono text-sm focus:ring-0 focus:outline-none"
                        placeholder="输入 YAML 配置..."
                        spellcheck="false"
                        :disabled="isSubmitting"
                    />
                </ScrollArea>

                <!-- 操作按钮 -->
                <div class="flex justify-end gap-2">
                    <Button variant="outline" @click="handleClose" :disabled="isSubmitting"> 取消 </Button>
                    <Button @click="handleSubmit" :disabled="isSubmitting || !hasWorkspace">
                        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
                        <Send v-else class="mr-2 h-4 w-4" />
                        {{ isSubmitting ? '应用中...' : '应用资源' }}
                    </Button>
                </div>
            </div>
        </DialogContent>
    </Dialog>
</template>
