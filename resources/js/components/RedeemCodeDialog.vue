<template>
    <Dialog v-model:open="open">
        <DialogTrigger as-child>
            <Button variant="outline" size="sm">
                <TicketIcon class="mr-2 h-4 w-4" />
                兑换卡密
            </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-md">
            <DialogHeader>
                <DialogTitle>兑换卡密</DialogTitle>
                <DialogDescription> 输入有效的兑换码来增加账户余额 </DialogDescription>
            </DialogHeader>
            <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="space-y-2">
                    <Label for="code">兑换码</Label>
                    <Input
                        id="code"
                        v-model="form.code"
                        placeholder="请输入兑换码"
                        class="font-mono uppercase"
                        :class="{ 'border-red-500': form.errors.code }"
                        @input="(e: Event) => (form.code = (e.target as HTMLInputElement).value.toUpperCase())"
                    />
                    <p v-if="form.errors.code" class="text-sm text-red-500">
                        {{ form.errors.code }}
                    </p>
                </div>
                <DialogFooter>
                    <Button type="button" variant="outline" @click="open = false" :disabled="form.processing"> 取消 </Button>
                    <Button type="submit" :disabled="form.processing || !form.code">
                        <Loader2 v-if="form.processing" class="mr-2 h-4 w-4 animate-spin" />
                        {{ form.processing ? '兑换中...' : '兑换' }}
                    </Button>
                </DialogFooter>
            </form>
        </DialogContent>
    </Dialog>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from '@inertiajs/vue3';
import { Loader2, TicketIcon } from 'lucide-vue-next';
import { ref } from 'vue';

const open = ref(false);

const form = useForm({
    code: '',
});

const handleSubmit = () => {
    form.post(route('balance.redeem'), {
        onSuccess: () => {
            open.value = false;
            form.reset();
        },
        onError: () => {
            // 错误会通过 Inertia 的错误处理显示
        },
    });
};
</script>
