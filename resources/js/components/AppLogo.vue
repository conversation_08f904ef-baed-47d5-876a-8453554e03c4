<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { usePage } from '@inertiajs/vue3';

const page = usePage();

const appName = page.props.display_name;
</script>

<template>
    <div class="flex items-center">
        <AppLogoIcon class="size-5 fill-current text-white dark:text-black" />
        <div class="ml-4 grid flex-1 text-left text-sm">
            <span class="truncate leading-none font-semibold">{{ appName }}</span>
        </div>
    </div>
</template>
