<script setup lang="ts">
import { Tooltip, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { PodContainer } from '@/types';

interface Props {
    containers: PodContainer[];
}

const props = defineProps<Props>();

const getContainerStatusColor = (container: PodContainer): string => {
    if (!container.ready && container.state.includes('Terminated')) {
        return 'bg-gray-400'; // 退出状态
    }

    if (container.ready && container.state.includes('Running')) {
        return 'bg-green-500'; // 运行中
    }

    if (container.state.includes('Waiting')) {
        return 'bg-orange-400'; // 等待中
    }

    if (container.state.includes('Terminated')) {
        return 'bg-gray-400'; // 已终止
    }

    return 'bg-yellow-400'; // 其他状态，默认为警告色
};

const getContainerStatusText = (container: PodContainer): string => {
    const status = container.state;
    const ready = container.ready ? '准备就绪' : '未就绪';
    const restarts = container.restartCount > 0 ? ` (重启 ${container.restartCount} 次)` : '';

    return `${container.name}: ${status} - ${ready}${restarts}`;
};
</script>

<template>
    <TooltipProvider>
        <div class="flex flex-wrap gap-1">
            <Tooltip v-for="container in containers" :key="container.name">
                <TooltipTrigger as-child>
                    <div :class="['h-3 w-3 cursor-help rounded-sm transition-colors', getContainerStatusColor(container)]" />
                </TooltipTrigger>
                <TooltipContent side="top" class="max-w-xs">
                    <div class="text-sm">
                        {{ getContainerStatusText(container) }}
                    </div>
                </TooltipContent>
            </Tooltip>
        </div>
    </TooltipProvider>
</template>
