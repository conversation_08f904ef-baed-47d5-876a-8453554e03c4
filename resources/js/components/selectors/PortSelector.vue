<template>
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <Label>{{ label }}</Label>
            <Button type="button" variant="outline" size="sm" @click="addPort">
                <Plus class="mr-2 h-4 w-4" />
                添加端口
            </Button>
        </div>

        <div
            v-if="!internalPorts.length"
            class="rounded-lg border-2 border-dashed border-gray-300 py-6 text-center text-gray-500 dark:border-gray-600"
        >
            <p>暂未配置端口</p>
            <p class="mt-1 text-sm">点击上方按钮添加端口</p>
        </div>

        <div v-else class="space-y-3">
            <div v-for="(port, index) in internalPorts" :key="port.id" class="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                <div class="mb-4 flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">端口 {{ index + 1 }}</span>
                    <Button type="button" variant="ghost" size="sm" @click="removePort(index)" class="text-red-600 hover:text-red-700">
                        <Trash2 class="h-4 w-4" />
                    </Button>
                </div>

                <div
                    v-if="mode === 'service'"
                    :class="serviceType === 'LoadBalancer' ? 'grid grid-cols-1 gap-4 md:grid-cols-3' : 'grid grid-cols-1 gap-4 md:grid-cols-4'"
                >
                    <div>
                        <Label :for="`port_name_${index}`">端口名称（可选）</Label>
                        <Input v-model="port.name" @update:model-value="emitUpdate" type="text" placeholder="如：http" />
                    </div>

                    <div v-if="serviceType !== 'LoadBalancer'">
                        <Label :for="`port_${index}`">服务端口</Label>
                        <Input
                            v-model.number="(port as ServicePortWithId).port"
                            @update:model-value="emitUpdate"
                            type="number"
                            min="1"
                            max="65535"
                            placeholder="80"
                        />
                    </div>

                    <div>
                        <Label :for="`target_port_${index}`">目标端口</Label>
                        <Input
                            v-model.number="(port as ServicePortWithId).target_port"
                            @update:model-value="emitUpdate"
                            type="number"
                            min="1"
                            max="65535"
                            placeholder="8080"
                        />
                        <p v-if="serviceType === 'LoadBalancer'" class="mt-1 text-xs text-gray-500">服务端口将自动分配</p>
                    </div>

                    <div>
                        <Label :for="`protocol_${index}`">协议</Label>
                        <Select v-model="port.protocol" @update:model-value="emitUpdate">
                            <SelectTrigger>
                                <SelectValue placeholder="选择协议" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="TCP">TCP</SelectItem>
                                <SelectItem value="UDP">UDP</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div v-else-if="mode === 'container'" class="grid grid-cols-4 gap-2">
                    <Input v-model="port.name" @update:model-value="emitUpdate" placeholder="名称" />
                    <Input
                        v-model.number="(port as ContainerPortWithId).container_port"
                        @update:model-value="emitUpdate"
                        type="number"
                        placeholder="端口"
                        min="1"
                        max="65535"
                    />
                    <Select v-model="port.protocol" @update:model-value="emitUpdate">
                        <SelectTrigger>
                            <SelectValue placeholder="协议" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="TCP">TCP</SelectItem>
                            <SelectItem value="UDP">UDP</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button type="button" variant="outline" size="sm" @click="removePort(index)">
                        <X class="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface ContainerPort {
    name?: string;
    container_port?: number;
    protocol?: string;
}

interface ServicePort {
    name?: string;
    port?: number;
    target_port?: number;
    protocol?: string;
}

type InputPortType = ContainerPort | ServicePort;

interface ContainerPortWithId extends ContainerPort {
    id: number;
}
interface ServicePortWithId extends ServicePort {
    id: number;
}
type PortWithId = ContainerPortWithId | ServicePortWithId;

interface Props {
    modelValue: InputPortType[] | null | undefined;
    label?: string;
    mode?: 'container' | 'service';
    serviceType?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '端口配置',
    mode: 'container',
    serviceType: 'ClusterIP',
});

const emit = defineEmits<{
    'update:modelValue': [value: InputPortType[]];
}>();

let idCounter = 0;
const internalPorts = ref<PortWithId[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanVal = newVal ? JSON.stringify(newVal) : '[]';
        const internalCleanVal = JSON.stringify(internalPorts.value.map(({ id, ...rest }) => rest));

        if (newCleanVal !== internalCleanVal) {
            internalPorts.value = (newVal || []).map((port) => ({ ...port, id: idCounter++ }) as PortWithId);
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output = internalPorts.value.map(({ id, ...rest }) => rest);
    emit('update:modelValue', output);
}

const addPort = () => {
    let newPort: PortWithId;
    const id = idCounter++;

    if (props.mode === 'container') {
        newPort = {
            id,
            name: '',
            container_port: 80,
            protocol: 'TCP',
        };
    } else {
        if (props.serviceType === 'LoadBalancer') {
            newPort = {
                id,
                name: '',
                port: undefined,
                target_port: 8080,
                protocol: 'TCP',
            };
        } else {
            newPort = {
                id,
                name: '',
                port: 80,
                target_port: 8080,
                protocol: 'TCP',
            };
        }
    }
    internalPorts.value.push(newPort);
    emitUpdate();
};

const removePort = (index: number) => {
    internalPorts.value.splice(index, 1);
    emitUpdate();
};
</script>
