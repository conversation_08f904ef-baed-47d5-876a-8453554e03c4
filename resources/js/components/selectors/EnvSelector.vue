<template>
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <Label>{{ label }}</Label>
            <Button type="button" variant="outline" size="sm" @click="addEnv">
                <Plus class="mr-2 h-4 w-4" />
                添加环境变量
            </Button>
        </div>

        <div
            v-if="!internalEnvs.length"
            class="rounded-lg border-2 border-dashed border-gray-300 py-6 text-center text-gray-500 dark:border-gray-600"
        >
            <p>暂未配置环境变量</p>
            <p class="mt-1 text-sm">点击上方按钮添加环境变量</p>
        </div>

        <div v-else class="space-y-2">
            <div v-for="(env, index) in internalEnvs" :key="env.id" class="grid grid-cols-3 gap-2">
                <Input
                    v-model="env.name"
                    @update:model-value="emitUpdate"
                    placeholder="变量名"
                    :class="{ 'border-red-500': !env.name && showValidation }"
                />
                <Input
                    v-model="env.value"
                    @update:model-value="emitUpdate"
                    placeholder="变量值"
                    :class="{ 'border-red-500': !env.value && showValidation }"
                />
                <Button type="button" variant="outline" size="sm" @click="removeEnv(index)">
                    <X class="h-4 w-4" />
                </Button>
            </div>
        </div>

        <p v-if="description" class="text-sm text-gray-500 dark:text-gray-400">
            {{ description }}
        </p>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface InputEnvVar {
    name: string;
    value: string;
}

interface EnvVarWithId extends InputEnvVar {
    id: number;
}

interface Props {
    modelValue: InputEnvVar[] | null | undefined;
    label?: string;
    description?: string;
    showValidation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    label: '环境变量',
    description: '',
    showValidation: false,
});

const emit = defineEmits<{
    'update:modelValue': [value: InputEnvVar[]];
}>();

let idCounter = 0;
const internalEnvs = ref<EnvVarWithId[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanVal = newVal ? JSON.stringify(newVal) : '[]';
        const internalCleanVal = JSON.stringify(internalEnvs.value.map(({ id, ...rest }) => rest));

        if (newCleanVal !== internalCleanVal) {
            // Smart update to prevent focus loss
            // Remove extra items from the end
            if (newVal && newVal.length < internalEnvs.value.length) {
                internalEnvs.value.splice(newVal.length);
            }

            // Update existing items or add new ones
            (newVal || []).forEach((env, index) => {
                if (internalEnvs.value[index]) {
                    // Update existing item
                    internalEnvs.value[index].name = env.name;
                    internalEnvs.value[index].value = env.value;
                } else {
                    // Add new item
                    internalEnvs.value.push({ ...env, id: idCounter++ });
                }
            });
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output = internalEnvs.value.map(({ id, ...rest }) => rest);
    emit('update:modelValue', output);
}

const addEnv = () => {
    internalEnvs.value.push({ id: idCounter++, name: '', value: '' });
    emitUpdate();
};

const removeEnv = (index: number) => {
    internalEnvs.value.splice(index, 1);
    emitUpdate();
};
</script>
