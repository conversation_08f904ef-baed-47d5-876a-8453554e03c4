<template>
    <div class="space-y-4">
        <div>
            <Label>{{ label }}</Label>
            <div v-if="!modelValue" class="mt-2">
                <Button type="button" variant="outline" size="sm" @click="addProbe">
                    <Plus class="mr-2 h-4 w-4" />
                    添加{{ label }}
                </Button>
            </div>
            <div v-else class="mt-2 space-y-4 rounded-lg border p-4">
                <div class="flex items-center justify-between">
                    <h5 class="font-medium">{{ label }}配置</h5>
                    <Button type="button" variant="outline" size="sm" @click="removeProbe">
                        <X class="h-4 w-4" />
                    </Button>
                </div>

                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                        <Label>探针类型 *</Label>
                        <Select v-model="probe.type">
                            <SelectTrigger class="mt-1">
                                <SelectValue placeholder="选择探针类型" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="http">HTTP GET</SelectItem>
                                <SelectItem value="tcp">TCP Socket</SelectItem>
                                <SelectItem value="exec">Exec Command</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label>初始延迟 (秒)</Label>
                        <Input v-model.number="probe.initial_delay_seconds" type="number" min="0" max="3600" placeholder="0" class="mt-1" />
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                        <Label>检查间隔 (秒)</Label>
                        <Input v-model.number="probe.period_seconds" type="number" min="1" max="3600" placeholder="10" class="mt-1" />
                    </div>
                    <div>
                        <Label>超时时间 (秒)</Label>
                        <Input v-model.number="probe.timeout_seconds" type="number" min="1" max="3600" placeholder="1" class="mt-1" />
                    </div>
                    <div>
                        <Label>失败阈值</Label>
                        <Input
                            v-model.number="probe.failure_threshold"
                            type="number"
                            min="1"
                            :max="probeType === 'startup' ? 30 : 10"
                            placeholder="3"
                            class="mt-1"
                        />
                    </div>
                </div>

                <!-- HTTP 探针配置 -->
                <div v-if="probe.type === 'http'" class="space-y-4">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div>
                            <Label>路径 *</Label>
                            <Input v-model="probe.http_path" placeholder="/" class="mt-1" />
                        </div>
                        <div>
                            <Label>端口 *</Label>
                            <Input v-model.number="probe.http_port" type="number" min="1" max="65535" placeholder="80" class="mt-1" />
                        </div>
                        <div>
                            <Label>协议</Label>
                            <Select v-model="probe.http_scheme">
                                <SelectTrigger class="mt-1">
                                    <SelectValue placeholder="HTTP" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="HTTP">HTTP</SelectItem>
                                    <SelectItem value="HTTPS">HTTPS</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div>
                        <Label>自定义头部 (可选)</Label>
                        <div class="mt-2 space-y-2">
                            <div v-for="(header, headerIndex) in probe.http_headers" :key="headerIndex" class="grid grid-cols-3 gap-2">
                                <Input v-model="header.name" placeholder="头部名称" />
                                <Input v-model="header.value" placeholder="头部值" />
                                <Button type="button" variant="outline" size="sm" @click="removeHeader(headerIndex)">
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                            <Button type="button" variant="outline" size="sm" @click="addHeader">
                                <Plus class="mr-2 h-4 w-4" />
                                添加头部
                            </Button>
                        </div>
                    </div>
                </div>

                <!-- TCP 探针配置 -->
                <div v-if="probe.type === 'tcp'" class="space-y-4">
                    <div>
                        <Label>端口 *</Label>
                        <Input v-model.number="probe.tcp_port" type="number" min="1" max="65535" placeholder="80" class="mt-1" />
                    </div>
                </div>

                <!-- Exec 探针配置 -->
                <div v-if="probe.type === 'exec'" class="space-y-4">
                    <div>
                        <Label>命令 *</Label>
                        <div class="mt-2 space-y-2">
                            <div v-for="(command, commandIndex) in probe.exec_command" :key="commandIndex" class="grid grid-cols-2 gap-2">
                                <Input v-model="probe.exec_command![commandIndex]" placeholder="命令参数" />
                                <Button type="button" variant="outline" size="sm" @click="removeCommand(commandIndex)">
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                            <Button type="button" variant="outline" size="sm" @click="addCommand">
                                <Plus class="mr-2 h-4 w-4" />
                                添加命令参数
                            </Button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">例如：["/bin/sh", "-c", "ps aux | grep myapp"]</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { HealthProbe } from '@/types';
import { Plus, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface Props {
    modelValue?: HealthProbe | null;
    label: string;
    probeType?: 'liveness' | 'readiness' | 'startup';
}

interface Emits {
    (e: 'update:modelValue', value: HealthProbe | null): void;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    probeType: 'liveness',
});

const emit = defineEmits<Emits>();

const probe = ref<HealthProbe>({
    type: 'http',
    initial_delay_seconds: 0,
    period_seconds: 10,
    timeout_seconds: 1,
    success_threshold: 1,
    failure_threshold: 3,
    http_path: '/',
    http_port: 80,
    http_scheme: 'HTTP',
    http_headers: [],
    exec_command: [],
});

// 监听 modelValue 变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            // Deep comparison to avoid unnecessary updates
            if (JSON.stringify(newValue) !== JSON.stringify(probe.value)) {
                probe.value = { ...probe.value, ...newValue };
                if (!probe.value.http_headers) {
                    probe.value.http_headers = [];
                }
                if (!probe.value.exec_command) {
                    probe.value.exec_command = [];
                }
            }
        }
    },
    { immediate: true, deep: true },
);

// 监听 probe 变化
watch(
    probe,
    (newValue) => {
        if (props.modelValue) {
            emit('update:modelValue', { ...newValue });
        }
    },
    { deep: true },
);

const addProbe = () => {
    emit('update:modelValue', { ...probe.value });
};

const removeProbe = () => {
    emit('update:modelValue', null);
};

const addHeader = () => {
    if (!probe.value.http_headers) {
        probe.value.http_headers = [];
    }
    probe.value.http_headers.push({ name: '', value: '' });
};

const removeHeader = (index: number) => {
    probe.value.http_headers?.splice(index, 1);
};

const addCommand = () => {
    if (!probe.value.exec_command) {
        probe.value.exec_command = [];
    }
    probe.value.exec_command.push('');
};

const removeCommand = (index: number) => {
    probe.value.exec_command?.splice(index, 1);
};
</script>
