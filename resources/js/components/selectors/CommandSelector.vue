<template>
    <div class="space-y-4">
        <div class="space-y-4">
            <!-- Command 配置 -->
            <div>
                <div class="flex items-center justify-between">
                    <Label>Command (可选)</Label>
                    <Button type="button" variant="outline" size="sm" @click="addCommand">
                        <Plus class="mr-2 h-4 w-4" />
                        添加命令
                    </Button>
                </div>
                <p class="mb-2 text-xs text-gray-500">覆盖容器镜像的默认 ENTRYPOINT</p>

                <div
                    v-if="!internalCommand.length"
                    class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
                >
                    <p class="text-sm">使用镜像默认的 ENTRYPOINT</p>
                </div>

                <div v-else class="space-y-2">
                    <div v-for="(cmd, index) in internalCommand" :key="cmd.id" class="flex gap-2">
                        <Input v-model="cmd.value" @update:model-value="emitUpdate" :placeholder="`命令 ${index + 1}`" class="flex-1" />
                        <Button type="button" variant="outline" size="sm" @click="removeCommand(index)">
                            <X class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Args 配置 -->
            <div>
                <div class="flex items-center justify-between">
                    <Label>Args (可选)</Label>
                    <Button type="button" variant="outline" size="sm" @click="addArg">
                        <Plus class="mr-2 h-4 w-4" />
                        添加参数
                    </Button>
                </div>
                <p class="mb-2 text-xs text-gray-500">覆盖容器镜像的默认 CMD</p>

                <div
                    v-if="!internalArgs.length"
                    class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
                >
                    <p class="text-sm">使用镜像默认的 CMD</p>
                </div>

                <div v-else class="space-y-2">
                    <div v-for="(arg, index) in internalArgs" :key="arg.id" class="flex gap-2">
                        <Input v-model="arg.value" @update:model-value="emitUpdate" :placeholder="`参数 ${index + 1}`" class="flex-1" />
                        <Button type="button" variant="outline" size="sm" @click="removeArg(index)">
                            <X class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="description" class="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/30">
            <p class="text-sm text-blue-800 dark:text-blue-200">
                {{ description }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, X } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface CommandModel {
    command: string[];
    args: string[];
}

interface Props {
    modelValue: CommandModel | null | undefined;
    description?: string;
}

const props = withDefaults(defineProps<Props>(), {
    description: 'Command 用于覆盖镜像的 ENTRYPOINT，Args 用于覆盖镜像的 CMD',
});

const emit = defineEmits<{
    'update:modelValue': [value: CommandModel];
}>();

interface CommandItem {
    id: number;
    value: string;
}

let idCounter = 0;
const internalCommand = ref<CommandItem[]>([]);
const internalArgs = ref<CommandItem[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            const newCommands = newVal.command || [];
            if (newCommands.length !== internalCommand.value.length || newCommands.some((cmd, i) => cmd !== internalCommand.value[i].value)) {
                internalCommand.value = newCommands.map((cmd) => ({ id: idCounter++, value: cmd }));
            }

            const newArgs = newVal.args || [];
            if (newArgs.length !== internalArgs.value.length || newArgs.some((arg, i) => arg !== internalArgs.value[i].value)) {
                internalArgs.value = newArgs.map((arg) => ({ id: idCounter++, value: arg }));
            }
        } else {
            internalCommand.value = [];
            internalArgs.value = [];
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output: CommandModel = {
        command: internalCommand.value.map((item) => item.value),
        args: internalArgs.value.map((item) => item.value),
    };
    emit('update:modelValue', output);
}

const addCommand = () => {
    internalCommand.value.push({ id: idCounter++, value: '' });
    emitUpdate();
};

const removeCommand = (index: number) => {
    internalCommand.value.splice(index, 1);
    emitUpdate();
};

const addArg = () => {
    internalArgs.value.push({ id: idCounter++, value: '' });
    emitUpdate();
};

const removeArg = (index: number) => {
    internalArgs.value.splice(index, 1);
    emitUpdate();
};
</script>
