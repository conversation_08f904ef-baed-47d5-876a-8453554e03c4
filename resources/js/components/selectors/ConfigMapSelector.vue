<template>
    <div class="space-y-2">
        <Label v-if="label">{{ label }}</Label>
        <div class="space-y-2">
            <div v-for="(selectedConfigMap, index) in internalConfigMaps" :key="index" class="flex items-center space-x-2">
                <Select :model-value="selectedConfigMap || undefined" @update:model-value="(value) => updateConfigMapValue(index, value)">
                    <SelectTrigger class="flex-1">
                        <SelectValue :placeholder="placeholder" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="configMap in availableConfigMaps" :key="configMap.name" :value="configMap.name">
                            {{ configMap.name }} ({{ configMap.data_count }} 项)
                        </SelectItem>
                        <div v-if="availableConfigMaps.length === 0" class="py-6 text-center text-sm text-gray-500">暂无可用 ConfigMap</div>
                    </SelectContent>
                </Select>
                <Button type="button" variant="outline" size="icon" @click="removeConfigMap(index)">
                    <Trash2 class="h-4 w-4" />
                </Button>
            </div>
            <Button type="button" variant="outline" @click="addConfigMap" class="w-full">
                <Plus class="mr-2 h-4 w-4" />
                {{ addButtonText }}
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { ConfigMap } from '@/types';
import { Plus, Trash2 } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface Props {
    modelValue: string[] | null | undefined;
    label?: string;
    placeholder?: string;
    addButtonText?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '',
    placeholder: '选择 ConfigMap',
    addButtonText: '添加 ConfigMap',
});

const emit = defineEmits<{
    'update:modelValue': [value: string[]];
}>();

const configMaps = ref<ConfigMap[]>([]);
const internalConfigMaps = computed(() => props.modelValue ?? []);

const availableConfigMaps = computed(() => configMaps.value);

const loadConfigMaps = async () => {
    try {
        const response = await axios.get('/api/configmaps');
        configMaps.value = response.data;
    } catch (error) {
        console.error('加载 ConfigMap 失败:', error);
    }
};

const addConfigMap = () => {
    const newValue = [...internalConfigMaps.value, ''];
    emit('update:modelValue', newValue);
};

const removeConfigMap = (index: number) => {
    const newValue = internalConfigMaps.value.filter((_, i) => i !== index);
    emit('update:modelValue', newValue);
};

const updateConfigMapValue = (index: number, value: any) => {
    const newValue = [...internalConfigMaps.value];
    newValue[index] = value || '';
    emit('update:modelValue', newValue);
};

// 当可用的 ConfigMap 列表变化时，验证当前的选择
watch(availableConfigMaps, (newAvailableConfigMaps) => {
    const currentConfigMaps = internalConfigMaps.value;
    if (currentConfigMaps.length === 0 || newAvailableConfigMaps.length === 0) {
        return;
    }

    const validConfigMaps = currentConfigMaps.filter(
        (configMapName) => !configMapName || newAvailableConfigMaps.some((cm) => cm.name === configMapName),
    );

    if (validConfigMaps.length !== currentConfigMaps.length) {
        emit('update:modelValue', validConfigMaps);
    }
});

onMounted(() => {
    loadConfigMaps();
});
</script>
