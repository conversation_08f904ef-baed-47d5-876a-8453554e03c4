<template>
    <div class="space-y-4">
        <Label>{{ label }}</Label>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
                <Label :for="`memory-${id}`">内存 (Mi) *</Label>
                <Input
                    :id="`memory-${id}`"
                    v-model.number="memory"
                    type="number"
                    min="512"
                    step="512"
                    placeholder="512"
                    :class="{ 'border-red-500': !isValidMemory && showValidation }"
                    required
                />
                <p class="mt-1 text-xs text-gray-500">最小 512Mi，必须是 512 的倍数</p>
                <p v-if="!isValidMemory && showValidation" class="mt-1 text-xs text-red-600">内存值必须是 512 的倍数且不小于 512</p>
            </div>

            <div>
                <Label :for="`cpu-${id}`">CPU (m) *</Label>
                <Input
                    :id="`cpu-${id}`"
                    v-model.number="cpu"
                    type="number"
                    min="500"
                    step="500"
                    placeholder="500"
                    :class="{ 'border-red-500': !isValidCpu && showValidation }"
                    required
                />
                <p class="mt-1 text-xs text-gray-500">最小 500m，必须是 500 的倍数 (大约 {{ Math.round(cpuForDisplay / 1000) }} 核)</p>
                <p v-if="!isValidCpu && showValidation" class="mt-1 text-xs text-red-600">CPU 值必须是 500 的倍数且不小于 500</p>
            </div>
        </div>

        <div v-if="description" class="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/30">
            <p class="text-sm text-blue-800 dark:text-blue-200">
                {{ description }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { computed } from 'vue';

interface ModelResources {
    memory: number | null;
    cpu: number | null;
}

interface Props {
    modelValue: ModelResources | null | undefined;
    label?: string;
    description?: string;
    showValidation?: boolean;
    id?: string;
}

const props = withDefaults(defineProps<Props>(), {
    label: '资源限制',
    description: '',
    showValidation: false,
    id: 'default',
});

const emit = defineEmits<{
    'update:modelValue': [value: ModelResources];
}>();

const memory = computed({
    get: () => props.modelValue?.memory ?? undefined,
    set: (value) => {
        const newValue = typeof value === 'number' ? value : null;
        emit('update:modelValue', {
            cpu: props.modelValue?.cpu ?? null,
            memory: newValue,
        });
    },
});

const cpu = computed({
    get: () => props.modelValue?.cpu ?? undefined,
    set: (value) => {
        const newValue = typeof value === 'number' ? value : null;
        emit('update:modelValue', {
            memory: props.modelValue?.memory ?? null,
            cpu: newValue,
        });
    },
});

const isValidMemory = computed(() => {
    const mem = memory.value;
    if (typeof mem !== 'number') return false;
    return mem >= 512 && mem % 512 === 0;
});

const isValidCpu = computed(() => {
    const c = cpu.value;
    if (typeof c !== 'number') return false;
    return c >= 500 && c % 500 === 0;
});

const cpuForDisplay = computed(() => {
    return cpu.value ?? 0;
});

defineExpose({
    isValid: computed(() => isValidMemory.value && isValidCpu.value),
});
</script>
