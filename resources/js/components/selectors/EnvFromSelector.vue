<template>
    <div class="space-y-4">
        <!-- ConfigMap 环境变量 -->
        <div>
            <div class="flex items-center justify-between">
                <Label>从 ConfigMap 引用环境变量</Label>
                <Button type="button" variant="outline" size="sm" @click="addConfigMapEnv">
                    <Plus class="mr-2 h-4 w-4" />
                    添加 ConfigMap 环境变量
                </Button>
            </div>
            <p class="mb-2 text-xs text-gray-500">从 ConfigMap 的数据中引用环境变量</p>

            <div
                v-if="!internalConfigMapEnvs.length"
                class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未配置 ConfigMap 环境变量</p>
            </div>

            <div v-else class="space-y-3">
                <div v-for="(envItem, index) in internalConfigMapEnvs" :key="envItem.id" class="rounded-lg border p-3">
                    <div class="grid grid-cols-1 gap-3 md:grid-cols-4">
                        <div>
                            <Label>ConfigMap</Label>
                            <Select v-model="envItem.configmap_name" @update:model-value="emitUpdate">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择 ConfigMap" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="configMap in availableConfigMaps" :key="configMap.name" :value="configMap.name">
                                        {{ configMap.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>键名 (可选)</Label>
                            <Input v-model="envItem.key" @update:model-value="emitUpdate" placeholder="留空引用整个 ConfigMap" />
                            <p class="mt-1 text-xs text-gray-500">留空将引用整个 ConfigMap</p>
                        </div>

                        <div>
                            <Label>环境变量名 (可选)</Label>
                            <Input v-model="envItem.env_name" @update:model-value="emitUpdate" placeholder="默认使用键名" />
                            <p class="mt-1 text-xs text-gray-500">指定键时可自定义变量名</p>
                        </div>

                        <div class="flex items-end">
                            <Button type="button" variant="outline" size="sm" @click="removeConfigMapEnv(index)" class="w-full">
                                <Trash2 class="mr-2 h-4 w-4" />
                                删除
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secret 环境变量 -->
        <div>
            <div class="flex items-center justify-between">
                <Label>从 Secret 引用环境变量</Label>
                <Button type="button" variant="outline" size="sm" @click="addSecretEnv">
                    <Plus class="mr-2 h-4 w-4" />
                    添加 Secret 环境变量
                </Button>
            </div>
            <p class="mb-2 text-xs text-gray-500">从 Secret 的数据中引用环境变量</p>

            <div
                v-if="!internalSecretEnvs.length"
                class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未配置 Secret 环境变量</p>
            </div>

            <div v-else class="space-y-3">
                <div v-for="(envItem, index) in internalSecretEnvs" :key="envItem.id" class="rounded-lg border p-3">
                    <div class="grid grid-cols-1 gap-3 md:grid-cols-4">
                        <div>
                            <Label>Secret</Label>
                            <Select v-model="envItem.secret_name" @update:model-value="emitUpdate">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择 Secret" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="secret in availableSecrets" :key="secret.name" :value="secret.name">
                                        {{ secret.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>键名 (可选)</Label>
                            <Input v-model="envItem.key" @update:model-value="emitUpdate" placeholder="留空引用整个 Secret" />
                            <p class="mt-1 text-xs text-gray-500">留空将引用整个 Secret</p>
                        </div>

                        <div>
                            <Label>环境变量名 (可选)</Label>
                            <Input v-model="envItem.env_name" @update:model-value="emitUpdate" placeholder="默认使用键名" />
                            <p class="mt-1 text-xs text-gray-500">指定键时可自定义变量名</p>
                        </div>

                        <div class="flex items-end">
                            <Button type="button" variant="outline" size="sm" @click="removeSecretEnv(index)" class="w-full">
                                <Trash2 class="mr-2 h-4 w-4" />
                                删除
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { ConfigMap, EnvFromConfigMap, EnvFromSecret, Secret } from '@/types';
import { Plus, Trash2 } from 'lucide-vue-next';
import { onMounted, ref, watch } from 'vue';

interface EnvFromModel {
    configmap?: EnvFromConfigMap[];
    secret?: EnvFromSecret[];
}

interface Props {
    modelValue: EnvFromModel | null | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'update:modelValue': [value: EnvFromModel];
}>();

interface EnvFromConfigMapWithId extends EnvFromConfigMap {
    id: number;
}

interface EnvFromSecretWithId extends EnvFromSecret {
    id: number;
}

let idCounter = 0;
const internalConfigMapEnvs = ref<EnvFromConfigMapWithId[]>([]);
const internalSecretEnvs = ref<EnvFromSecretWithId[]>([]);

const availableConfigMaps = ref<ConfigMap[]>([]);
const availableSecrets = ref<Secret[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanConfigMapEnvs = newVal?.configmap ? JSON.stringify(newVal.configmap) : '[]';
        const internalCleanConfigMapEnvs = JSON.stringify(internalConfigMapEnvs.value.map(({ id, ...rest }) => rest));

        if (newCleanConfigMapEnvs !== internalCleanConfigMapEnvs) {
            internalConfigMapEnvs.value = (newVal?.configmap || []).map((env) => ({ ...env, id: idCounter++ }));
        }

        const newCleanSecretEnvs = newVal?.secret ? JSON.stringify(newVal.secret) : '[]';
        const internalCleanSecretEnvs = JSON.stringify(internalSecretEnvs.value.map(({ id, ...rest }) => rest));

        if (newCleanSecretEnvs !== internalCleanSecretEnvs) {
            internalSecretEnvs.value = (newVal?.secret || []).map((env) => ({ ...env, id: idCounter++ }));
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output: EnvFromModel = {
        configmap: internalConfigMapEnvs.value.map(({ id, ...rest }) => rest),
        secret: internalSecretEnvs.value.map(({ id, ...rest }) => rest),
    };
    emit('update:modelValue', output);
}

const addConfigMapEnv = () => {
    internalConfigMapEnvs.value.push({
        id: idCounter++,
        configmap_name: '',
        key: '',
        env_name: '',
    });
    emitUpdate();
};

const removeConfigMapEnv = (index: number) => {
    internalConfigMapEnvs.value.splice(index, 1);
    emitUpdate();
};

const addSecretEnv = () => {
    internalSecretEnvs.value.push({
        id: idCounter++,
        secret_name: '',
        key: '',
        env_name: '',
    });
    emitUpdate();
};

const removeSecretEnv = (index: number) => {
    internalSecretEnvs.value.splice(index, 1);
    emitUpdate();
};

const loadResources = async () => {
    try {
        const [configMapsResponse, secretsResponse] = await Promise.all([axios.get('/api/configmaps'), axios.get('/api/secrets')]);

        availableConfigMaps.value = configMapsResponse.data;
        availableSecrets.value = secretsResponse.data;
    } catch (error) {
        console.error('加载资源失败:', error);
    }
};

onMounted(() => {
    loadResources();
});

defineExpose({
    refresh: loadResources,
});
</script>
