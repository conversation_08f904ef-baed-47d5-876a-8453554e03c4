<template>
    <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div class="space-y-2">
            <Label :for="`service-name-${id}`" :class="{ required: required }">服务名称</Label>
            <Select v-model="selectedServiceName" @update:model-value="onServiceChange">
                <SelectTrigger>
                    <SelectValue placeholder="选择服务" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem v-for="service in servicesWithPorts" :key="service.name" :value="service.name">
                        {{ service.name }}
                    </SelectItem>
                    <div v-if="!loading && servicesWithPorts.length === 0" class="py-6 text-center text-sm text-gray-500">暂无可用服务</div>
                </SelectContent>
            </Select>
        </div>
        <div class="space-y-2">
            <Label :for="`service-port-${id}`" :class="{ required: required }">服务端口</Label>
            <Select v-model="selectedPortNumber" :disabled="!selectedService" @update:model-value="onPortChange">
                <SelectTrigger>
                    <SelectValue placeholder="选择端口" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem v-for="port in selectedService?.ports" :key="port.port" :value="port.port">
                        {{ port.port }} ({{ port.protocol }})
                        <span v-if="port.name">- {{ port.name }}</span>
                    </SelectItem>
                </SelectContent>
            </Select>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { Service } from '@/types';
import { computed, onMounted, ref, watch } from 'vue';

interface ServiceBackend {
    name: string;
    port: {
        number: number | undefined;
    };
}

interface Props {
    modelValue: ServiceBackend;
    required?: boolean;
    id?: string;
}

const props = withDefaults(defineProps<Props>(), {
    required: false,
    id: 'default',
});

const emit = defineEmits<{
    'update:modelValue': [value: ServiceBackend];
}>();

const services = ref<Service[]>([]);
const loading = ref(true);

const servicesWithPorts = computed(() => {
    return services.value.filter((service) => service.ports && service.ports.length > 0);
});

const selectedServiceName = ref<string | undefined>(props.modelValue.name);
const selectedPortNumber = ref<number | undefined>(props.modelValue.port.number);

const selectedService = computed(() => {
    return services.value.find((s) => s.name === selectedServiceName.value);
});

const loadServices = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/api/services');
        services.value = response.data;
    } catch (error) {
        console.error('加载服务失败:', error);
    } finally {
        loading.value = false;
    }
};

const onServiceChange = (serviceName: any) => {
    selectedServiceName.value = serviceName;
    const service = services.value.find((s) => s.name === serviceName);
    if (service && service.ports.length > 0) {
        selectedPortNumber.value = service.ports[0].port;
    } else {
        selectedPortNumber.value = undefined;
    }
    emitUpdate();
};

const onPortChange = (portNumber: any) => {
    selectedPortNumber.value = portNumber;
    emitUpdate();
};

const emitUpdate = () => {
    if (selectedServiceName.value) {
        emit('update:modelValue', {
            name: selectedServiceName.value,
            port: {
                number: selectedPortNumber.value,
            },
        });
    }
};

watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            selectedServiceName.value = newVal.name;
            selectedPortNumber.value = newVal.port.number;
        }
    },
    { deep: true, immediate: true },
);

onMounted(() => {
    loadServices();
});
</script>

<style scoped>
.required::after {
    content: ' *';
    color: rgb(239 68 68);
}
</style>
