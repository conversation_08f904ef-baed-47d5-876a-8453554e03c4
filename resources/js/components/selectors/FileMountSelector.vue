<template>
    <div class="space-y-4">
        <!-- ConfigMap 文件挂载 -->
        <div>
            <div class="flex items-center justify-between">
                <Label>ConfigMap 文件挂载</Label>
                <Button type="button" variant="outline" size="sm" @click="addConfigMapMount">
                    <Plus class="mr-2 h-4 w-4" />
                    添加 ConfigMap 挂载
                </Button>
            </div>
            <p class="mb-2 text-xs text-gray-500">将 ConfigMap 数据挂载为文件</p>

            <div
                v-if="!internalConfigMapMounts.length"
                class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未配置 ConfigMap 文件挂载</p>
            </div>

            <div v-else class="space-y-4">
                <div v-for="(mountItem, index) in internalConfigMapMounts" :key="mountItem.id" class="rounded-lg border p-4">
                    <div class="mb-4 flex items-center justify-between">
                        <h4 class="font-medium">ConfigMap 挂载 {{ index + 1 }}</h4>
                        <Button type="button" variant="outline" size="sm" @click="removeConfigMapMount(index)">
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除
                        </Button>
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div>
                            <Label>ConfigMap</Label>
                            <Select v-model="mountItem.configmap_name" @update:model-value="emitUpdate">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择 ConfigMap" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="configMap in availableConfigMaps" :key="configMap.name" :value="configMap.name">
                                        {{ configMap.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>挂载路径</Label>
                            <Input v-model="mountItem.mount_path" @update:model-value="emitUpdate" placeholder="/etc/config" />
                        </div>

                        <div>
                            <Label>文件权限 (可选)</Label>
                            <div class="space-y-2">
                                <div class="flex gap-2">
                                    <Input
                                        v-model="mountItem.default_mode"
                                        @update:model-value="emitUpdate"
                                        type="text"
                                        placeholder="644"
                                        class="flex-1"
                                    />
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button type="button" variant="outline" size="sm">
                                                <Settings class="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end" class="w-80">
                                            <div class="p-3">
                                                <h4 class="mb-3 font-medium">权限生成器</h4>

                                                <!-- 快速选择 -->
                                                <div class="mb-4">
                                                    <Label class="text-sm font-medium">常用权限</Label>
                                                    <div class="mt-2 grid grid-cols-2 gap-2">
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForConfigMap(index, '644')"
                                                            class="justify-start text-xs"
                                                        >
                                                            644 - 标准只读
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForConfigMap(index, '600')"
                                                            class="justify-start text-xs"
                                                        >
                                                            600 - 仅所有者
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForConfigMap(index, '755')"
                                                            class="justify-start text-xs"
                                                        >
                                                            755 - 可执行
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForConfigMap(index, '777')"
                                                            class="justify-start text-xs"
                                                        >
                                                            777 - 完全权限
                                                        </Button>
                                                    </div>
                                                </div>

                                                <!-- 权限构建器 -->
                                                <div class="space-y-3">
                                                    <Label class="text-sm font-medium">自定义权限</Label>
                                                    <div class="space-y-2 text-xs">
                                                        <div class="grid grid-cols-4 gap-2 text-center font-medium">
                                                            <div></div>
                                                            <div>读(4)</div>
                                                            <div>写(2)</div>
                                                            <div>执行(1)</div>
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">所有者</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">组</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">其他</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>
                                                    </div>

                                                    <div class="border-t pt-2">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-sm font-medium">结果: {{ builtPermission }}</span>
                                                            <Button
                                                                type="button"
                                                                variant="outline"
                                                                size="sm"
                                                                @click="applyBuiltPermissionToConfigMap(index)"
                                                                :disabled="!builtPermission"
                                                            >
                                                                应用
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                                <p class="text-xs text-gray-500">八进制格式，如 644、0644、777。留空使用默认权限</p>
                            </div>
                        </div>
                    </div>

                    <!-- 文件项配置 -->
                    <div class="mt-4">
                        <div class="flex items-center justify-between">
                            <Label>文件映射 (可选)</Label>
                            <Button type="button" variant="outline" size="sm" @click="addConfigMapItem(index)">
                                <Plus class="mr-2 h-4 w-4" />
                                添加文件
                            </Button>
                        </div>
                        <p class="mb-2 text-xs text-gray-500">留空将挂载 ConfigMap 中的所有数据</p>

                        <div v-if="mountItem.items && mountItem.items.length > 0" class="mt-2 space-y-2">
                            <div v-for="(item, itemIndex) in mountItem.items" :key="itemIndex" class="grid grid-cols-3 gap-2">
                                <Input v-model="item.key" @update:model-value="emitUpdate" placeholder="ConfigMap 键名" />
                                <Input v-model="item.path" @update:model-value="emitUpdate" placeholder="文件路径" />
                                <Button type="button" variant="outline" size="sm" @click="removeConfigMapItem(index, itemIndex)">
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secret 文件挂载 -->
        <div>
            <div class="flex items-center justify-between">
                <Label>Secret 文件挂载</Label>
                <Button type="button" variant="outline" size="sm" @click="addSecretMount">
                    <Plus class="mr-2 h-4 w-4" />
                    添加 Secret 挂载
                </Button>
            </div>
            <p class="mb-2 text-xs text-gray-500">将 Secret 数据挂载为文件</p>

            <div
                v-if="!internalSecretMounts.length"
                class="rounded-lg border-2 border-dashed border-gray-300 py-4 text-center text-gray-500 dark:border-gray-600"
            >
                <p class="text-sm">暂未配置 Secret 文件挂载</p>
            </div>

            <div v-else class="space-y-4">
                <div v-for="(mountItem, index) in internalSecretMounts" :key="mountItem.id" class="rounded-lg border p-4">
                    <div class="mb-4 flex items-center justify-between">
                        <h4 class="font-medium">Secret 挂载 {{ index + 1 }}</h4>
                        <Button type="button" variant="outline" size="sm" @click="removeSecretMount(index)">
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除
                        </Button>
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div>
                            <Label>Secret</Label>
                            <Select v-model="mountItem.secret_name" @update:model-value="emitUpdate">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择 Secret" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="secret in availableSecrets" :key="secret.name" :value="secret.name">
                                        {{ secret.name }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>挂载路径</Label>
                            <Input v-model="mountItem.mount_path" @update:model-value="emitUpdate" placeholder="/etc/secret" />
                        </div>

                        <div>
                            <Label>文件权限 (可选)</Label>
                            <div class="space-y-2">
                                <div class="flex gap-2">
                                    <Input
                                        v-model="mountItem.default_mode"
                                        @update:model-value="emitUpdate"
                                        type="text"
                                        placeholder="600"
                                        class="flex-1"
                                    />
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button type="button" variant="outline" size="sm">
                                                <Settings class="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end" class="w-80">
                                            <div class="p-3">
                                                <h4 class="mb-3 font-medium">权限生成器</h4>

                                                <!-- 快速选择 -->
                                                <div class="mb-4">
                                                    <Label class="text-sm font-medium">常用权限</Label>
                                                    <div class="mt-2 grid grid-cols-2 gap-2">
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForSecret(index, '644')"
                                                            class="justify-start text-xs"
                                                        >
                                                            644 - 标准只读
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForSecret(index, '600')"
                                                            class="justify-start text-xs"
                                                        >
                                                            600 - 仅所有者
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForSecret(index, '755')"
                                                            class="justify-start text-xs"
                                                        >
                                                            755 - 可执行
                                                        </Button>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            @click="setPermissionForSecret(index, '777')"
                                                            class="justify-start text-xs"
                                                        >
                                                            777 - 完全权限
                                                        </Button>
                                                    </div>
                                                </div>

                                                <!-- 权限构建器 -->
                                                <div class="space-y-3">
                                                    <Label class="text-sm font-medium">自定义权限</Label>
                                                    <div class="space-y-2 text-xs">
                                                        <div class="grid grid-cols-4 gap-2 text-center font-medium">
                                                            <div></div>
                                                            <div>读(4)</div>
                                                            <div>写(2)</div>
                                                            <div>执行(1)</div>
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">所有者</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.owner.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">组</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.group.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>

                                                        <div class="grid grid-cols-4 items-center gap-2">
                                                            <Label class="text-xs">其他</Label>
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.read"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.write"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                            <input
                                                                type="checkbox"
                                                                v-model="permissionBuilder.other.execute"
                                                                @change="updateBuiltPermission"
                                                                class="h-4 w-4"
                                                            />
                                                        </div>
                                                    </div>

                                                    <div class="border-t pt-2">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-sm font-medium">结果: {{ builtPermission }}</span>
                                                            <Button
                                                                type="button"
                                                                variant="outline"
                                                                size="sm"
                                                                @click="applyBuiltPermissionToSecret(index)"
                                                                :disabled="!builtPermission"
                                                            >
                                                                应用
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                                <p class="text-xs text-gray-500">八进制格式，如 644、0644、777。留空使用默认权限</p>
                            </div>
                        </div>
                    </div>

                    <!-- 文件项配置 -->
                    <div class="mt-4">
                        <div class="flex items-center justify-between">
                            <Label>文件映射 (可选)</Label>
                            <Button type="button" variant="outline" size="sm" @click="addSecretItem(index)">
                                <Plus class="mr-2 h-4 w-4" />
                                添加文件
                            </Button>
                        </div>
                        <p class="mb-2 text-xs text-gray-500">留空将挂载 Secret 中的所有数据</p>

                        <div v-if="mountItem.items && mountItem.items.length > 0" class="mt-2 space-y-2">
                            <div v-for="(item, itemIndex) in mountItem.items" :key="itemIndex" class="grid grid-cols-3 gap-2">
                                <Input v-model="item.key" @update:model-value="emitUpdate" placeholder="Secret 键名" />
                                <Input v-model="item.path" @update:model-value="emitUpdate" placeholder="文件路径" />
                                <Button type="button" variant="outline" size="sm" @click="removeSecretItem(index, itemIndex)">
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import axios from '@/lib/axios';
import type { ConfigMap, ConfigMapMount, Secret, SecretMount } from '@/types';
import { Plus, Settings, Trash2, X } from 'lucide-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

interface FileMountModel {
    configmap_mounts?: ConfigMapMount[];
    secret_mounts?: SecretMount[];
}

interface Props {
    modelValue: FileMountModel | null | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
    'update:modelValue': [value: FileMountModel];
}>();

interface ConfigMapMountWithId extends ConfigMapMount {
    id: number;
}

interface SecretMountWithId extends SecretMount {
    id: number;
}

// 权限构建器状态
interface PermissionBuilder {
    owner: { read: boolean; write: boolean; execute: boolean };
    group: { read: boolean; write: boolean; execute: boolean };
    other: { read: boolean; write: boolean; execute: boolean };
}

const permissionBuilder = ref<PermissionBuilder>({
    owner: { read: false, write: false, execute: false },
    group: { read: false, write: false, execute: false },
    other: { read: false, write: false, execute: false },
});

// 计算构建的权限
const builtPermission = computed(() => {
    const calculateDigit = (perms: { read: boolean; write: boolean; execute: boolean }) => {
        return (perms.read ? 4 : 0) + (perms.write ? 2 : 0) + (perms.execute ? 1 : 0);
    };

    const owner = calculateDigit(permissionBuilder.value.owner);
    const group = calculateDigit(permissionBuilder.value.group);
    const other = calculateDigit(permissionBuilder.value.other);

    return `${owner}${group}${other}`;
});

let idCounter = 0;
const internalConfigMapMounts = ref<ConfigMapMountWithId[]>([]);
const internalSecretMounts = ref<SecretMountWithId[]>([]);

const availableConfigMaps = ref<ConfigMap[]>([]);
const availableSecrets = ref<Secret[]>([]);

watch(
    () => props.modelValue,
    (newVal) => {
        const newCleanConfigMapMounts = newVal?.configmap_mounts ? JSON.stringify(newVal.configmap_mounts) : '[]';
        const internalCleanConfigMapMounts = JSON.stringify(internalConfigMapMounts.value.map(({ id, ...rest }) => rest));

        if (newCleanConfigMapMounts !== internalCleanConfigMapMounts) {
            internalConfigMapMounts.value = (newVal?.configmap_mounts || []).map((mount) => ({ ...mount, id: idCounter++ }));
        }

        const newCleanSecretMounts = newVal?.secret_mounts ? JSON.stringify(newVal.secret_mounts) : '[]';
        const internalCleanSecretMounts = JSON.stringify(internalSecretMounts.value.map(({ id, ...rest }) => rest));

        if (newCleanSecretMounts !== internalCleanSecretMounts) {
            internalSecretMounts.value = (newVal?.secret_mounts || []).map((mount) => ({ ...mount, id: idCounter++ }));
        }
    },
    { deep: true, immediate: true },
);

function emitUpdate() {
    const output: FileMountModel = {
        configmap_mounts: internalConfigMapMounts.value.map(({ id, ...rest }) => rest),
        secret_mounts: internalSecretMounts.value.map(({ id, ...rest }) => rest),
    };
    emit('update:modelValue', output);
}

// 权限管理
const setPermission = (index: number, permission: string) => {
    // 判断是 ConfigMap 还是 Secret 挂载
    if (index < internalConfigMapMounts.value.length) {
        internalConfigMapMounts.value[index].default_mode = permission;
    } else {
        const secretIndex = index - internalConfigMapMounts.value.length;
        internalSecretMounts.value[secretIndex].default_mode = permission;
    }
    emitUpdate();
};

const setPermissionForConfigMap = (index: number, permission: string) => {
    internalConfigMapMounts.value[index].default_mode = permission;
    emitUpdate();
};

const setPermissionForSecret = (index: number, permission: string) => {
    internalSecretMounts.value[index].default_mode = permission;
    emitUpdate();
};

const updateBuiltPermission = () => {
    // 这个方法会在权限构建器的复选框改变时触发
    // 计算会通过 computed 自动更新
};

const applyBuiltPermission = (index: number) => {
    const permission = builtPermission.value;
    if (permission && permission !== '000') {
        setPermission(index, permission);
    }
};

const applyBuiltPermissionToConfigMap = (index: number) => {
    const permission = builtPermission.value;
    if (permission && permission !== '000') {
        setPermissionForConfigMap(index, permission);
    }
};

const applyBuiltPermissionToSecret = (index: number) => {
    const permission = builtPermission.value;
    if (permission && permission !== '000') {
        setPermissionForSecret(index, permission);
    }
};

// 重置权限构建器
const resetPermissionBuilder = () => {
    permissionBuilder.value = {
        owner: { read: false, write: false, execute: false },
        group: { read: false, write: false, execute: false },
        other: { read: false, write: false, execute: false },
    };
};

// ConfigMap 挂载管理
const addConfigMapMount = () => {
    internalConfigMapMounts.value.push({
        id: idCounter++,
        configmap_name: '',
        mount_path: '',
        items: [],
        default_mode: undefined,
    });
    emitUpdate();
};

const removeConfigMapMount = (index: number) => {
    internalConfigMapMounts.value.splice(index, 1);
    emitUpdate();
};

const addConfigMapItem = (mountIndex: number) => {
    if (!internalConfigMapMounts.value[mountIndex].items) {
        internalConfigMapMounts.value[mountIndex].items = [];
    }
    internalConfigMapMounts.value[mountIndex].items!.push({
        key: '',
        path: '',
    });
    emitUpdate();
};

const removeConfigMapItem = (mountIndex: number, itemIndex: number) => {
    internalConfigMapMounts.value[mountIndex].items?.splice(itemIndex, 1);
    emitUpdate();
};

// Secret 挂载管理
const addSecretMount = () => {
    internalSecretMounts.value.push({
        id: idCounter++,
        secret_name: '',
        mount_path: '',
        items: [],
        default_mode: undefined,
    });
    emitUpdate();
};

const removeSecretMount = (index: number) => {
    internalSecretMounts.value.splice(index, 1);
    emitUpdate();
};

const addSecretItem = (mountIndex: number) => {
    if (!internalSecretMounts.value[mountIndex].items) {
        internalSecretMounts.value[mountIndex].items = [];
    }
    internalSecretMounts.value[mountIndex].items!.push({
        key: '',
        path: '',
    });
    emitUpdate();
};

const removeSecretItem = (mountIndex: number, itemIndex: number) => {
    internalSecretMounts.value[mountIndex].items?.splice(itemIndex, 1);
    emitUpdate();
};

const loadResources = async () => {
    try {
        const [configMapsResponse, secretsResponse] = await Promise.all([axios.get('/api/configmaps'), axios.get('/api/secrets')]);

        availableConfigMaps.value = configMapsResponse.data;
        availableSecrets.value = secretsResponse.data;
    } catch (error) {
        console.error('加载资源失败:', error);
    }
};

onMounted(() => {
    loadResources();
});

defineExpose({
    refresh: loadResources,
});
</script>
