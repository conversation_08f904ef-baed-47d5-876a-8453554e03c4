<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useTaskStore, type Task } from '@/stores/taskStore';
import { useWorkspaceStore } from '@/stores/workspaceStore';
import { AlertCircle, CheckCircle, Clock, Loader2, Play, RotateCcw, X, XCircle } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { toast } from 'vue-sonner';

const taskStore = useTaskStore();
const workspaceStore = useWorkspaceStore();
const showPopover = ref(false);
const selectedTaskForError = ref<Task | null>(null);

// 计算属性：判断是否有工作空间
const hasWorkspace = computed(() => !!workspaceStore.currentWorkspace);

// 任务状态对应的图标和颜色
const getTaskStatusInfo = (status: Task['status']) => {
    switch (status) {
        case 'pending':
            return { icon: Clock, color: 'text-yellow-500', bgColor: 'bg-yellow-50 dark:bg-yellow-950', label: '等待中' };
        case 'running':
            return { icon: Play, color: 'text-blue-500', bgColor: 'bg-blue-50 dark:bg-blue-950', label: '执行中' };
        case 'completed':
            return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-50 dark:bg-green-950', label: '已完成' };
        case 'failed':
            return { icon: AlertCircle, color: 'text-red-500', bgColor: 'bg-red-50 dark:bg-red-950', label: '失败' };
        case 'cancelled':
            return { icon: XCircle, color: 'text-gray-500', bgColor: 'bg-gray-50 dark:bg-gray-950', label: '已取消' };
        default:
            return { icon: Clock, color: 'text-gray-500', bgColor: 'bg-gray-50 dark:bg-gray-950', label: '未知' };
    }
};

// 格式化时间
const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) {
        // 小于1分钟
        return '刚刚';
    } else if (diff < 3600000) {
        // 小于1小时
        return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
        // 小于1天
        return `${Math.floor(diff / 3600000)}小时前`;
    } else {
        return date.toLocaleDateString();
    }
};

// 重试任务
const handleRetryTask = async (taskId: string) => {
    try {
        await taskStore.retryTask(taskId);
        toast.success('任务重试成功');
    } catch (error: any) {
        toast.error(error.message || '重试任务失败');
    }
};

// 取消任务
const handleCancelTask = async (taskId: string) => {
    try {
        await taskStore.cancelTask(taskId);
        toast.success('任务取消成功');
    } catch (error: any) {
        toast.error(error.message || '取消任务失败');
    }
};

// 计算任务徽章显示
const taskBadge = computed(() => {
    if (taskStore.hasActiveTasks) {
        return taskStore.activeTasks.length;
    }
    return null;
});

// 手动刷新任务数据
const refreshTasks = () => {
    if (hasWorkspace.value) {
        taskStore.fetchTasks();
    }
};

const getErrorMessageSummary = (message: string | undefined): string => {
    if (!message) return '';

    // 优先显示冒号前的内容
    const separator = '：';
    const separatorIndex = message.indexOf(separator);
    if (separatorIndex !== -1) {
        return message.substring(0, separatorIndex + 1).trim();
    }

    // 其次显示 JSON 前的内容
    const jsonMatch = message.match(/{.*}/s);
    if (jsonMatch?.index && jsonMatch.index > 0) {
        return message.substring(0, jsonMatch.index).trim();
    }

    // 最后进行长度截断
    if (message.length > 50) {
        return message.substring(0, 50) + '...';
    }

    return message;
};

const formattedError = computed(() => {
    if (!selectedTaskForError.value?.message) {
        return '';
    }

    const message = selectedTaskForError.value.message;
    const jsonMatch = message.match(/{.*}/s);

    if (jsonMatch && jsonMatch[0]) {
        try {
            const jsonObj = JSON.parse(jsonMatch[0]);
            const prefix = message.substring(0, jsonMatch.index);

            return `${prefix.trim()}\n\n${JSON.stringify(jsonObj, null, 2)}`;
        } catch (e) {
            return message;
        }
    }

    return message;
});
</script>

<template>
    <Popover v-model:open="showPopover">
        <PopoverTrigger as-child>
            <Button variant="ghost" size="icon" class="group relative h-9 w-9 cursor-pointer">
                <Loader2 v-if="taskStore.hasActiveTasks" class="size-5 animate-spin opacity-80 group-hover:opacity-100" />
                <Clock v-else class="size-5 opacity-80 group-hover:opacity-100" />

                <!-- 任务数量徽章 -->
                <div
                    v-if="taskBadge"
                    class="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white"
                >
                    {{ taskBadge > 99 ? '99+' : taskBadge }}
                </div>
            </Button>
        </PopoverTrigger>

        <PopoverContent class="w-96 p-0" align="end">
            <Card class="border-0 shadow-none">
                <CardHeader class="pb-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <CardTitle class="text-lg">任务中心</CardTitle>
                            <CardDescription>
                                {{ hasWorkspace ? '当前工作空间的任务状态' : '请先选择工作空间' }}
                            </CardDescription>
                        </div>
                        <Button variant="ghost" size="icon" class="h-6 w-6" @click="showPopover = false">
                            <X class="h-4 w-4" />
                        </Button>
                    </div>
                </CardHeader>

                <CardContent class="space-y-4">
                    <!-- 无工作空间提示 -->
                    <div v-if="!hasWorkspace" class="py-8 text-center">
                        <Clock class="mx-auto h-12 w-12 text-muted-foreground" />
                        <p class="mt-4 text-sm text-muted-foreground">请先选择一个工作空间来查看任务</p>
                    </div>

                    <!-- 任务统计 -->
                    <div v-else-if="taskStore.stats.total > 0" class="grid grid-cols-3 gap-2">
                        <div class="rounded-lg bg-yellow-50 p-3 text-center dark:bg-yellow-950">
                            <div class="text-lg font-semibold text-yellow-700 dark:text-yellow-300">
                                {{ taskStore.stats.pending + taskStore.stats.running }}
                            </div>
                            <div class="text-xs text-yellow-600 dark:text-yellow-400">活跃任务</div>
                        </div>
                        <div class="rounded-lg bg-green-50 p-3 text-center dark:bg-green-950">
                            <div class="text-lg font-semibold text-green-700 dark:text-green-300">
                                {{ taskStore.stats.completed }}
                            </div>
                            <div class="text-xs text-green-600 dark:text-green-400">已完成</div>
                        </div>
                        <div class="rounded-lg bg-red-50 p-3 text-center dark:bg-red-950">
                            <div class="text-lg font-semibold text-red-700 dark:text-red-300">
                                {{ taskStore.stats.failed }}
                            </div>
                            <div class="text-xs text-red-600 dark:text-red-400">失败</div>
                        </div>
                    </div>

                    <Separator v-if="hasWorkspace && taskStore.stats.total > 0" />

                    <!-- 加载状态 -->
                    <div v-if="taskStore.loading" class="space-y-3">
                        <div v-for="i in 3" :key="i" class="flex items-center space-x-3">
                            <Skeleton class="h-10 w-10 rounded-full" />
                            <div class="space-y-2">
                                <Skeleton class="h-4 w-32" />
                                <Skeleton class="h-3 w-24" />
                            </div>
                        </div>
                    </div>

                    <!-- 任务列表 -->
                    <div v-else-if="hasWorkspace && taskStore.recentTasks.length > 0" class="space-y-3">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium">最近任务</h4>
                            <Button variant="ghost" size="sm" @click="refreshTasks" :disabled="taskStore.loading">
                                <RotateCcw class="h-3 w-3" :class="{ 'animate-spin': taskStore.loading }" />
                            </Button>
                        </div>

                        <div class="max-h-64 space-y-2 overflow-y-auto">
                            <div v-for="task in taskStore.recentTasks" :key="task.id" class="flex items-center justify-between rounded-lg border p-3">
                                <div class="flex items-center space-x-3">
                                    <div :class="[getTaskStatusInfo(task.status).bgColor, 'rounded-full p-2']">
                                        <component
                                            :is="getTaskStatusInfo(task.status).icon"
                                            :class="[getTaskStatusInfo(task.status).color, 'h-4 w-4']"
                                        />
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div class="flex items-center space-x-2">
                                            <p class="truncate text-sm font-medium">{{ task.type }}</p>
                                            <Badge variant="secondary" class="text-xs">
                                                {{ getTaskStatusInfo(task.status).label }}
                                            </Badge>
                                        </div>
                                        <p class="text-xs text-muted-foreground">
                                            {{ formatTime(task.updated_at) }}
                                        </p>
                                        <div v-if="task.message" class="mt-1">
                                            <p
                                                class="cursor-pointer truncate text-xs text-muted-foreground hover:underline"
                                                @click="selectedTaskForError = task"
                                            >
                                                {{ getErrorMessageSummary(task.message) }}
                                            </p>
                                        </div>
                                        <!-- 进度条 -->
                                        <div v-if="task.status === 'running' && task.progress && task.progress > 0" class="mt-2">
                                            <div class="h-1 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                                                <div
                                                    class="h-1 rounded-full bg-blue-500 transition-all duration-300"
                                                    :style="{ width: `${task.progress}%` }"
                                                ></div>
                                            </div>
                                            <p class="mt-1 text-xs text-muted-foreground">{{ task.progress }}%</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="flex space-x-1">
                                    <Button
                                        v-if="task.status === 'failed'"
                                        variant="ghost"
                                        size="sm"
                                        @click="handleRetryTask(task.id)"
                                        class="h-8 w-8 p-0"
                                    >
                                        <RotateCcw class="h-3 w-3" />
                                    </Button>
                                    <Button
                                        v-if="task.status === 'pending' || task.status === 'running'"
                                        variant="ghost"
                                        size="sm"
                                        @click="handleCancelTask(task.id)"
                                        class="h-8 w-8 p-0"
                                    >
                                        <X class="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 无任务状态 -->
                    <div v-else-if="hasWorkspace && !taskStore.loading" class="py-8 text-center">
                        <CheckCircle class="mx-auto h-12 w-12 text-muted-foreground" />
                        <p class="mt-4 text-sm text-muted-foreground">当前没有任务</p>
                    </div>

                    <!-- 错误状态 -->
                    <div v-if="taskStore.error" class="rounded-lg bg-red-50 p-3 dark:bg-red-950">
                        <div class="flex items-center space-x-2">
                            <AlertCircle class="h-4 w-4 text-red-500" />
                            <p class="text-sm text-red-700 dark:text-red-300">{{ taskStore.error }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </PopoverContent>
    </Popover>

    <Dialog :open="!!selectedTaskForError" @update:open="(isOpen) => !isOpen && (selectedTaskForError = null)">
        <DialogContent class="max-w-2xl">
            <DialogHeader>
                <DialogTitle>任务错误详情</DialogTitle>
            </DialogHeader>
            <div class="mt-4 max-h-[60vh] overflow-y-auto rounded-md bg-muted p-4 text-sm">
                <pre class="break-all whitespace-pre-wrap">{{ formattedError }}</pre>
            </div>
        </DialogContent>
    </Dialog>
</template>
