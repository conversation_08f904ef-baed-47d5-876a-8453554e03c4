<!-- eslint-disable vue/no-v-text-v-html-on-component -->
<template>
    <div v-if="links.length > 3" class="mt-4 flex justify-center">
        <div class="flex items-center space-x-1">
            <template v-for="(link, i) in links" :key="i">
                <div v-if="link.url === null" class="cursor-not-allowed px-4 py-2 text-sm text-gray-500 dark:text-gray-400" v-html="link.label" />
                <Link
                    v-else
                    :href="link.url"
                    class="rounded-md px-4 py-2 text-sm"
                    :class="{
                        'bg-primary text-primary-foreground': link.active,
                        'hover:bg-muted': !link.active,
                    }"
                    v-html="link.label"
                ></Link>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Link } from '@inertiajs/vue3';

interface PaginationLink {
    active: boolean;
    label: string;
    url: string | null;
}

defineProps<{
    links: PaginationLink[];
}>();
</script>
