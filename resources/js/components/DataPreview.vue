<script setup lang="ts">
import yaml from 'js-yaml';

interface Props {
    api: string;
    method: string;
    dataRef: any;
    showType?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showType: true,
});

const formatValue = (value: any): string => {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';

    // Create a new object with api first
    const formattedValue = { api: props.api, method: props.method };

    // Copy all other properties (excluding api if it already exists)
    for (const key in value) {
        if (key !== 'api' && key !== 'method') {
            // @ts-ignore key
            formattedValue[key] = value[key];
        }
    }

    if (Array.isArray(value)) {
        return `[${value.map(formatValue).join(', ')}]`;
    }

    if (typeof value === 'object') {
        try {
            return yaml.dump(formattedValue, { indent: 2, sortKeys: false });
        } catch {
            return String(value);
        }
    }

    return String(value);
};

const getType = (value: any): string => {
    if (value === null) return 'null';

    if (Array.isArray(value)) {
        return 'array';
    }

    return typeof value;
};
</script>

<template>
    <h3 class="text-sm font-medium text-muted-foreground">API 数据提交预览</h3>
    <span v-if="showType" class="text-sm font-normal text-muted-foreground"> 类型: {{ getType(dataRef) }} </span>

    <div class="rounded-md bg-muted/50 p-4">
        <pre class="max-h-60 overflow-auto text-sm">{{ formatValue(dataRef) }}</pre>
    </div>
</template>
