<template>
    <AppLayout>
        <Head :title="`StatefulSet: ${statefulSetData?.name || 'Loading...'}`" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('statefulsets.index'))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回列表
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ statefulSetData?.name || 'Loading...' }}
                        </h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">详情信息</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2" v-if="statefulSetData">
                    <Button variant="outline" @click="showScaleDialog = true">
                        <Expand class="mr-2 h-4 w-4" />
                        扩容
                    </Button>
                    <Button variant="outline" @click="editStatefulSet">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑
                    </Button>
                    <Button variant="destructive" @click="showDeleteDialog = true">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="space-y-6">
                <Card>
                    <CardContent class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-1/4" />
                            <Skeleton class="h-4 w-1/2" />
                            <Skeleton class="h-4 w-3/4" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- StatefulSet Details -->
            <div v-else-if="statefulSetData" class="space-y-6">
                <!-- 基本信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>基本信息</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">名称</Label>
                                <p class="font-mono text-sm">{{ statefulSetData.name }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">工作空间</Label>
                                <p class="font-mono text-sm">{{ workspace.name }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">副本数</Label>
                                <p class="text-sm">{{ statefulSetData.replicas }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">状态</Label>
                                <Badge :variant="getStatusVariant(statefulSetData.status)">
                                    {{ formatK8sStatus(statefulSetData.status) }}
                                </Badge>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">服务名称</Label>
                                <p class="text-sm">{{ statefulSetData.serviceName }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">更新策略</Label>
                                <p class="text-sm">{{ statefulSetData.updateStrategy }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间</Label>
                                <p class="text-sm">{{ formatDate(statefulSetData.created_at) }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 容器信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>容器信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-6">
                            <div v-for="(container, index) in statefulSetData.containers" :key="index" class="rounded-lg border p-4">
                                <h4 class="mb-4 font-medium">{{ container.name }}</h4>

                                <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">镜像</Label>
                                        <p class="font-mono text-sm">{{ container.image }}</p>
                                    </div>
                                    <div v-if="container.resources">
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">资源限制</Label>
                                        <p class="text-sm">内存: {{ container.resources.memory }}Mi, CPU: {{ container.resources.cpu }}m</p>
                                    </div>
                                </div>

                                <!-- 端口 -->
                                <div v-if="container.ports && container.ports.length > 0" class="mb-4">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">端口</Label>
                                    <div class="mt-2 space-y-2">
                                        <div v-for="port in container.ports" :key="port.container_port" class="text-sm">
                                            {{ port.name ? `${port.name}: ` : '' }}{{ port.container_port }}/{{ port.protocol || 'TCP' }}
                                        </div>
                                    </div>
                                </div>

                                <!-- 环境变量 -->
                                <div v-if="container.env && container.env.length > 0" class="mb-4">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">环境变量</Label>
                                    <div class="mt-2 space-y-1">
                                        <div v-for="envVar in container.env" :key="envVar.name" class="font-mono text-sm">
                                            {{ envVar.name }}={{ envVar.value }}
                                        </div>
                                    </div>
                                </div>

                                <!-- 卷挂载 -->
                                <div v-if="container.volume_mounts && container.volume_mounts.length > 0">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">卷挂载</Label>
                                    <div class="mt-2 space-y-1">
                                        <div v-for="mount in container.volume_mounts" :key="mount.storage_name + mount.mount_path" class="text-sm">
                                            {{ mount.storage_name }} → {{ mount.mount_path }}
                                            <span v-if="mount.sub_path" class="text-gray-500">:{{ mount.sub_path }}</span>
                                            <span v-if="mount.read_only" class="ml-2 text-yellow-600">(只读)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 卷声明模板 -->
                <Card v-if="statefulSetData.volumeClaimTemplates && statefulSetData.volumeClaimTemplates.length > 0">
                    <CardHeader>
                        <CardTitle>卷声明模板</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-4">
                            <div v-for="template in statefulSetData.volumeClaimTemplates" :key="template.name" class="rounded-lg border p-4">
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">名称</Label>
                                        <p class="font-mono text-sm">{{ template.name }}</p>
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">容量</Label>
                                        <p class="text-sm">{{ template.size }}Mi</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!--拉取密钥s -->
                <Card v-if="statefulSetData.image_pull_secrets && statefulSetData.image_pull_secrets.length > 0">
                    <CardHeader>
                        <CardTitle>拉取密钥</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-2">
                            <div v-for="secret in statefulSetData.image_pull_secrets" :key="secret" class="font-mono text-sm">
                                {{ secret }}
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 选择器标签 -->
                <Card>
                    <CardHeader>
                        <CardTitle>选择器标签</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-2">
                            <div v-for="(value, key) in statefulSetData.selector" :key="key" class="font-mono text-sm">{{ key }}={{ value }}</div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Error State -->
            <Card v-else-if="error">
                <CardContent class="p-6 text-center">
                    <div class="text-red-600 dark:text-red-400">
                        <AlertCircle class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-lg font-medium">加载失败</p>
                        <p class="text-sm">{{ error }}</p>
                        <Button class="mt-4" @click="loadStatefulSet">重试</Button>
                    </div>
                </CardContent>
            </Card>

            <!-- 扩容对话框 -->
            <Dialog v-model:open="showScaleDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容有状态应用</DialogTitle>
                        <DialogDescription> 调整 {{ statefulSetData?.name }} 的副本数 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="replicas">副本数</Label>
                            <Input id="replicas" v-model.number="scaleForm.replicas" type="number" min="0" max="100" class="mt-1" />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showScaleDialog = false">取消</Button>
                        <Button @click="scaleStatefulSet" :disabled="scaling">
                            <Loader2 v-if="scaling" class="mr-2 h-4 w-4 animate-spin" />
                            确认扩容
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除有状态应用</DialogTitle>
                        <DialogDescription> 确定要删除有状态应用 "{{ statefulSetData?.name }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deleteStatefulSet" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatK8sStatus } from '@/lib/formatK8sStatus';
import type { StatefulSet, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Edit, Expand, Loader2, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    statefulSetName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const workspace = computed(() => props.workspace);

if (!workspace.value) {
    console.error('No current workspace found');
}

const statefulSetData = ref<StatefulSet | null>(null);
const loading = ref(true);
const error = ref<string | null>(null);
const showScaleDialog = ref(false);
const showDeleteDialog = ref(false);
const scaling = ref(false);
const deleting = ref(false);

const scaleForm = ref({
    replicas: 1,
});

const loadStatefulSet = async () => {
    if (!workspace.value) {
        error.value = '请先选择一个工作空间';
        loading.value = false;
        return;
    }

    try {
        loading.value = true;
        error.value = null;
        const response = await axios.get(`/api/statefulsets/${props.statefulSetName}`);
        statefulSetData.value = response.data;
        scaleForm.value.replicas = response.data.replicas;
    } catch (err: any) {
        console.error('加载详情失败:', err);
        error.value = err.response?.data?.message || '加载详情失败';
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'failed':
            return 'destructive';
        default:
            return 'outline';
    }
};

const editStatefulSet = () => {
    router.visit(route('statefulsets.edit', { statefulset: props.statefulSetName }));
};

const scaleStatefulSet = async () => {
    if (!statefulSetData.value) return;

    try {
        scaling.value = true;
        await axios.patch(`/api/statefulsets/${props.statefulSetName}/scale`, {
            replicas: scaleForm.value.replicas,
        });

        toast.success('扩容成功');
        showScaleDialog.value = false;
        loadStatefulSet();
    } catch (error) {
        console.error('扩容失败:', error);
        toast.error('扩容失败');
    } finally {
        scaling.value = false;
    }
};

const deleteStatefulSet = async () => {
    if (!statefulSetData.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/statefulsets/${props.statefulSetName}`);

        toast.success('删除成功');
        router.visit(route('statefulsets.index'));
    } catch (error) {
        console.error('删除失败:', error);
        toast.error('删除失败');
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

onMounted(() => {
    loadStatefulSet();
});
</script>
