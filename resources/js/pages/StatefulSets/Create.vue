<template>
    <AppLayout>
        <Head title="创建有状态应用" />

        <div class="space-y-6 p-4">
            <div class="flex items-center space-x-4">
                <Button variant="ghost" @click="router.visit(route('statefulsets.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">创建</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">配置并创建新的有状态应用</p>
                </div>
            </div>

            <WorkloadForm v-model="form" workload-type="statefulset" />

            <div class="flex justify-end space-x-4">
                <Button variant="outline" @click="router.visit(route('statefulsets.index'))"> 取消 </Button>
                <Button @click="createStatefulSet" :disabled="creating">
                    <Loader2 v-if="creating" class="mr-2 h-4 w-4 animate-spin" />
                    创建
                </Button>
            </div>
        </div>

        <div class="p-4">
            <DataPreview api="/api/statefulsets" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Button } from '@/components/ui/button';
import WorkloadForm from '@/components/workloads/WorkloadForm.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { CreateStatefulSetData } from '@/types';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ArrowLeft, Loader2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const creating = ref(false);

const form = ref<CreateStatefulSetData>({
    name: '',
    replicas: 1,
    service_name: '',
    containers: [
        {
            name: '',
            image: '',
            working_dir: '',
            command: [],
            args: [],
            ports: [],
            env: [],
            env_from_configmap: [],
            env_from_secret: [],
            volume_mounts: [],
            configmap_mounts: [],
            secret_mounts: [],
            resources: {
                memory: 512,
                cpu: 500,
            },
            liveness_probe: undefined,
            readiness_probe: undefined,
            startup_probe: undefined,
        },
    ],
    image_pull_secrets: [],
});

const createStatefulSet = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        creating.value = true;

        // 验证表单
        if (!form.value.name) {
            toast.error('请输入名称');
            return;
        }

        if (form.value.containers.length === 0) {
            toast.error('至少需要一个容器');
            return;
        }

        for (const container of form.value.containers) {
            if (!container.name || !container.image) {
                toast.error('请填写容器名称和镜像');
                return;
            }
        }

        // 过滤空的配置项
        const cleanedData = {
            ...form.value,
            containers: form.value.containers.map((container) => ({
                ...container,
                working_dir: container.working_dir?.trim() || undefined,
                command: container.command?.filter((cmd) => cmd.trim()) || [],
                args: container.args?.filter((arg) => arg.trim()) || [],
                ports: container.ports?.filter((p) => p.container_port) || [],
                env: container.env?.filter((e) => e.name && e.value) || [],
                env_from_configmap: container.env_from_configmap?.filter((e) => e.configmap_name) || [],
                env_from_secret: container.env_from_secret?.filter((e) => e.secret_name) || [],
                volume_mounts: container.volume_mounts?.filter((m) => m.mount_path && m.storage_name) || [],
                configmap_mounts: container.configmap_mounts?.filter((m) => m.configmap_name && m.mount_path) || [],
                secret_mounts: container.secret_mounts?.filter((m) => m.secret_name && m.mount_path) || [],
                liveness_probe: container.liveness_probe || undefined,
                readiness_probe: container.readiness_probe || undefined,
                startup_probe: container.startup_probe || undefined,
            })),
            image_pull_secrets: form.value.image_pull_secrets?.filter((s) => s.trim()) || [],
        };

        // 如果服务名称为空，移除该字段
        if (!cleanedData.service_name?.trim()) {
            delete (cleanedData as Partial<CreateStatefulSetData>).service_name;
        }

        await axios.post('/api/statefulsets', cleanedData);

        toast.success('创建成功');
        router.visit(route('statefulsets.index'));
    } catch (error: any) {
        console.error('创建失败:', error);
        if (error.response?.data?.errors) {
            const errors = error.response.data.errors;
            Object.values(errors)
                .flat()
                .forEach((message: any) => {
                    toast.error(message);
                });
        } else {
            toast.error('创建失败');
        }
    } finally {
        creating.value = false;
    }
};
</script>
