<template>
    <Head title="服务管理" />
    <AppLayout>
        <div class="space-y-6 p-4 md:p-6">
            <div class="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">服务管理</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">管理工作空间中的服务，为应用提供网络访问。</p>
                </div>
                <Button @click="$inertia.visit(route('services.create'))">
                    <Plus class="mr-2 h-4 w-4" />
                    创建服务
                </Button>
            </div>

            <div v-if="error" class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
                <div class="flex items-center">
                    <AlertCircle class="mr-2 h-5 w-5 text-red-500" />
                    <span class="text-red-700 dark:text-red-300">{{ error }}</span>
                </div>
            </div>

            <div v-if="loading" class="py-12 text-center">
                <div class="inline-flex items-center">
                    <Loader2 class="mr-2 h-6 w-6 animate-spin" />
                    加载中...
                </div>
            </div>

            <div v-else-if="services.length === 0" class="py-12 text-center">
                <div class="text-gray-400 dark:text-gray-600">
                    <Server class="mx-auto mb-4 h-12 w-12" />
                    <h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">暂无服务</h3>
                    <p class="mb-4 text-gray-600 dark:text-gray-400">还没有创建任何服务，开始创建第一个服务吧。</p>
                    <Button @click="$inertia.visit(route('services.create'))">
                        <Plus class="mr-2 h-4 w-4" />
                        创建服务
                    </Button>
                </div>
            </div>

            <div v-else class="space-y-4">
                <div
                    v-for="service in services"
                    :key="service.name"
                    class="rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-lg md:p-6 dark:border-gray-700 dark:bg-gray-800"
                >
                    <div class="flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-3">
                                <Link
                                    :href="route('services.show', service.name)"
                                    class="text-lg font-semibold text-gray-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
                                >
                                    {{ service.name }}
                                </Link>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <Badge :variant="getServiceTypeVariant(service.type)">
                                                {{ friendlyServiceType(service.type).name }}
                                            </Badge>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{{ friendlyServiceType(service.type).description }}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                                <Badge :variant="getStatusVariant(service.status)">
                                    {{ formatServiceStatus(service.status) }}
                                </Badge>
                            </div>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">创建于 {{ formatDate(service.created_at) }}</div>
                        </div>

                        <div class="flex w-full flex-wrap items-center gap-2 md:w-auto md:flex-nowrap">
                            <Button variant="outline" size="sm" @click="$inertia.visit(route('services.show', service.name))">
                                <Eye class="mr-1 h-4 w-4" />
                                查看
                            </Button>
                            <Button variant="outline" size="sm" @click="$inertia.visit(route('services.edit', service.name))">
                                <Edit class="mr-1 h-4 w-4" />
                                编辑
                            </Button>
                            <Button variant="destructive" size="sm" @click="confirmDelete(service)">
                                <Trash2 class="mr-1 h-4 w-4" />
                                删除
                            </Button>
                        </div>
                    </div>

                    <div
                        class="mt-4 grid grid-cols-1 gap-x-6 gap-y-4 border-t border-gray-200 pt-4 sm:grid-cols-2 lg:grid-cols-3 dark:border-gray-700"
                    >
                        <!-- 工作负载信息 -->
                        <div v-if="service.workload_type && service.workload_name">
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">目标应用</div>
                            <div class="mt-1 flex items-center space-x-2">
                                <Badge variant="secondary">{{ formatWorkloadType(service.workload_type) }}</Badge>
                                <span class="font-semibold">{{ service.workload_name }}</span>
                            </div>
                        </div>

                        <!-- 访问地址 -->
                        <div>
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">访问地址</div>
                            <div class="mt-1 space-y-1">
                                <div class="flex items-center gap-2">
                                    <span class="font-mono text-sm text-gray-800 dark:text-gray-200" title="内部访问地址">
                                        {{ service.name }}.{{ workspace.namespace }}.svc.cluster.local
                                    </span>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <Badge variant="outline">内部</Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>此地址仅限集群内部访问</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div v-if="service.type === 'LoadBalancer'">
                                    <div v-if="service.loadBalancerIp" class="font-mono text-sm">
                                        {{ service.loadBalancerIp }}
                                    </div>
                                    <div v-else class="text-sm text-yellow-600 dark:text-yellow-400">正在分配外部 IP...</div>
                                </div>
                                <div v-if="service.type === 'NodePort' && service.ports.some((p) => p.nodePort)">
                                    <div v-for="node in clusterNodes" :key="node.name" class="mt-1">
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            通过节点 <span class="font-semibold text-gray-700 dark:text-gray-300">{{ node.name }}</span> 访问:
                                        </div>
                                        <div class="ml-2 flex flex-wrap gap-1">
                                            <span v-for="port in service.ports.filter((p) => p.nodePort)" :key="port.port" class="font-mono text-sm">
                                                {{ node.external_ip }}:{{ port.nodePort }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 端口信息 -->
                        <div>
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">端口映射</div>
                            <div class="mt-1 flex flex-wrap gap-1">
                                <TooltipProvider v-for="port in service.ports" :key="port.port">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <span
                                                class="inline-flex items-center rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                                            >
                                                {{ port.port }} <ArrowRight class="mx-1 h-3 w-3" />
                                                {{ port.targetPort || port.port }}
                                                <span class="ml-1 text-gray-500">/{{ port.protocol }}</span>
                                            </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>服务端口 {{ port.port }} 转发至目标端口 {{ port.targetPort || port.port }}</p>
                                            <p v-if="port.name">端口名称: {{ port.name }}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="deleteDialog.open">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除服务</AlertDialogTitle>
                        <AlertDialogDescription> 您确定要删除服务 "{{ deleteDialog.service?.name }}" 吗？此操作无法撤销。 </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction @click="deleteService" class="bg-red-600 hover:bg-red-700"> 删除 </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatWorkloadType } from '@/lib/formatK8sStatus';
import { Head, Link } from '@inertiajs/vue3';
import { AlertCircle, ArrowRight, Edit, Eye, Loader2, Plus, Server, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface ServicePort {
    name?: string;
    port: number;
    targetPort?: number;
    protocol: string;
    nodePort?: number;
}

interface Service {
    name: string;
    type: string;
    clusterIp?: string;
    loadBalancerIp?: string;
    ports: ServicePort[];
    status: string;
    workload_type?: string;
    workload_name?: string;
    created_at: string;
}

import type { Workspace } from '@/types';

interface Props {
    workspace: Workspace;
}

const props = defineProps<Props>();

const services = ref<Service[]>([]);
const loading = ref(true);
const error = ref<string | undefined>();
const clusterNodes = ref<any[]>([]);

const deleteDialog = ref({
    open: false,
    service: null as Service | null,
});

const loadServices = async () => {
    loading.value = true;
    try {
        const response = await axios.get('/api/services');
        services.value = response.data;
        error.value = undefined;
    } catch (err: any) {
        error.value = err.response?.data?.message || '加载服务列表失败';
        console.error('加载服务失败:', err);
    } finally {
        loading.value = false;
    }
};

const loadClusterNodes = async () => {
    try {
        // 获取当前工作空间的集群ID
        const clusterId = props.workspace.cluster_id;
        if (!clusterId) return;

        const response = await axios.get(`/api/clusters/${clusterId}/nodes`);
        clusterNodes.value = response.data;
    } catch (err: any) {
        console.error('加载集群节点失败:', err);
    }
};

const confirmDelete = (service: Service) => {
    deleteDialog.value = {
        open: true,
        service,
    };
};

const deleteService = async () => {
    if (!deleteDialog.value.service) return;

    try {
        await axios.delete(`/api/services/${deleteDialog.value.service.name}`);

        // 从列表中移除已删除的服务
        services.value = services.value.filter((s) => s.name !== deleteDialog.value.service?.name);

        toast.success('服务删除成功');
    } catch (err: any) {
        toast.error(err.response?.data?.message || '删除服务失败');
        console.error('删除服务失败:', err);
    } finally {
        deleteDialog.value = {
            open: false,
            service: null,
        };
    }
};

const getServiceTypeVariant = (type: string) => {
    switch (type) {
        case 'LoadBalancer':
            return 'default';
        case 'ClusterIP':
            return 'secondary';
        default:
            return 'outline';
    }
};

const getStatusVariant = (status: string) => {
    switch (status) {
        case 'Active':
            return 'default';
        case 'Pending':
            return 'secondary';
        default:
            return 'outline';
    }
};

const formatServiceStatus = (status: string) => {
    switch (status) {
        case 'Active':
            return '活跃';
        case 'Pending':
            return '等待中';
        default:
            return status;
    }
};

const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN');
};

const friendlyServiceType = (type: string) => {
    switch (type) {
        case 'ClusterIP':
            return { name: '内部服务', description: '仅在集群内部暴露服务' };
        case 'NodePort':
            return { name: '节点端口', description: '通过每个节点的静态端口暴露服务' };
        case 'LoadBalancer':
            return { name: '负载均衡', description: '使用云服务商的负载均衡器向外部暴露服务' };
        default:
            return { name: type, description: '未知的服务类型' };
    }
};

onMounted(() => {
    loadServices();
    loadClusterNodes();
});
</script>
