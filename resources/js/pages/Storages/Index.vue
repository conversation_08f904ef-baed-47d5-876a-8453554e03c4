<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="存储卷管理" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">存储卷管理</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">管理工作空间中的持久化存储卷</p>
                </div>
                <Button @click="showCreateDialog = true">
                    <Plus class="mr-2 h-4 w-4" />
                    创建存储卷
                </Button>
            </div>

            <!-- 存储卷列表 -->
            <Card>
                <CardContent class="p-0">
                    <div v-if="loading" class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-full" />
                            <Skeleton class="h-4 w-3/4" />
                            <Skeleton class="h-4 w-1/2" />
                        </div>
                    </div>

                    <div v-else-if="storages.length === 0" class="p-6 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <HardDrive class="mx-auto mb-4 h-12 w-12 opacity-50" />
                            <p class="text-lg font-medium">暂无存储卷</p>
                            <p class="text-sm">创建您的第一个存储卷来提供持久化存储</p>
                        </div>
                    </div>

                    <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div v-for="storage in storages" :key="storage.name" class="p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <HardDrive class="h-5 w-5 text-gray-400" />
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ storage.name }}
                                            </h3>
                                            <div class="mt-1 flex items-center space-x-4">
                                                <Badge :variant="getStatusVariant(storage.status)">
                                                    {{ getStatusLabel(storage.status) }}
                                                </Badge>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ storage.formatted_size }}
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ storage.storage_class }}
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ formatDate(storage.created_at) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="viewStorage(storage)">
                                            <Eye class="mr-2 h-4 w-4" />
                                            查看详情
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="expandStorage(storage)" :disabled="!storage.is_bound">
                                            <ArrowUp class="mr-2 h-4 w-4" />
                                            扩容
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem @click="confirmDeleteStorage(storage)" class="text-red-600 dark:text-red-400">
                                            <Trash2 class="mr-2 h-4 w-4" />
                                            删除
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- 创建 Storage 对话框 -->
            <Dialog v-model:open="showCreateDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>创建存储卷</DialogTitle>
                        <DialogDescription> 创建新的持久化存储卷 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="storageName">存储卷名称</Label>
                            <Input id="storageName" v-model="createForm.name" placeholder="输入存储卷名称" />
                        </div>

                        <div>
                            <Label for="storageSize">存储容量 (M)</Label>
                            <Input
                                id="storageSize"
                                v-model.number="createForm.size"
                                type="number"
                                placeholder="最小 512，必须为 512 的倍数"
                                :min="512"
                                :step="512"
                            />
                            <p class="mt-1 text-xs text-gray-500">容量单位为 M，最小 512M，必须为 512 的倍数</p>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showCreateDialog = false"> 取消 </Button>
                        <Button @click="createStorage" :disabled="creating">
                            {{ creating ? '创建中...' : '创建存储卷' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 查看 Storage 对话框 -->
            <Dialog v-model:open="showViewDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>存储卷详情</DialogTitle>
                    </DialogHeader>

                    <div v-if="selectedStorage" class="space-y-4">
                        <div>
                            <Label>名称</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ selectedStorage.name }}</p>
                        </div>
                        <div>
                            <Label>状态</Label>
                            <Badge :variant="getStatusVariant(selectedStorage.status)">
                                {{ getStatusLabel(selectedStorage.status) }}
                            </Badge>
                        </div>
                        <div>
                            <Label>容量</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ selectedStorage.formatted_size }}</p>
                        </div>
                        <div>
                            <Label>存储类</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ selectedStorage.storage_class }}</p>
                        </div>
                        <div>
                            <Label>访问模式</Label>
                            <div class="flex space-x-2">
                                <Badge v-for="mode in selectedStorage.access_modes" :key="mode" variant="outline">
                                    {{ mode }}
                                </Badge>
                            </div>
                        </div>
                        <div>
                            <Label>创建时间</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedStorage.created_at) }}</p>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showViewDialog = false"> 关闭 </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 扩容 Storage 对话框 -->
            <Dialog v-model:open="showExpandDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容存储卷</DialogTitle>
                        <DialogDescription> 增加 "{{ storageToExpand?.name }}" 的存储容量 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label>当前容量</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ storageToExpand?.formatted_size }}</p>
                        </div>
                        <div>
                            <Label for="newSize">新容量 (M)</Label>
                            <Input
                                id="newSize"
                                v-model.number="expandForm.size"
                                type="number"
                                placeholder="输入新的容量大小"
                                :min="getMinExpandSize()"
                                :step="512"
                            />
                            <p class="mt-1 text-xs text-gray-500">新容量必须大于当前容量，且为 512 的倍数</p>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showExpandDialog = false"> 取消 </Button>
                        <Button @click="doExpandStorage" :disabled="expanding">
                            {{ expanding ? '扩容中...' : '扩容' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除 Storage 对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription> 确定要删除存储 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false"> 取消 </Button>
                        <Button variant="destructive" @click="deleteStorage" :disabled="deleting">
                            {{ deleting ? '删除中...' : '删除' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, usePage } from '@inertiajs/vue3';
import { ArrowUp, Eye, HardDrive, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';

interface Storage {
    name: string;
    size: string;
    storage_class: string;
    access_modes: string[];
    status: string;
    volume_name?: string;
    capacity: string;
    conditions: any[];
    is_bound: boolean;
    is_read_write: boolean;
    is_read_write_many: boolean;
    formatted_size: string;
    created_at: string;
}

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const storages = ref<Storage[]>([]);
const loading = ref(true);
const showCreateDialog = ref(false);
const showViewDialog = ref(false);
const showExpandDialog = ref(false);
const showDeleteDialog = ref(false);
const selectedStorage = ref<Storage | null>(null);
const storageToExpand = ref<Storage | null>(null);
const storageToDelete = ref<Storage | null>(null);
const creating = ref(false);
const expanding = ref(false);
const deleting = ref(false);

const createForm = ref({
    name: '',
    size: 512,
});

const expandForm = ref({
    size: 0,
});

const loadStorages = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        loading.value = true;
        const response = await axios.get('/api/storages');
        storages.value = response.data;
    } catch (error) {
        console.error('加载 Storage 列表失败:', error);
        toast.error('加载 Storage 列表失败');
    } finally {
        loading.value = false;
    }
};

const getStatusLabel = (status: string): string => {
    const labels: Record<string, string> = {
        Bound: '已绑定',
        Pending: '等待中',
        Lost: '丢失',
        Unknown: '未知',
    };
    return labels[status] || status;
};

const getStatusVariant = (status: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const variants: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
        Bound: 'default',
        Pending: 'secondary',
        Lost: 'destructive',
        Unknown: 'outline',
    };
    return variants[status] || 'outline';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const createStorage = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        creating.value = true;

        if (createForm.value.size < 512) {
            toast.error('存储容量不能小于 512Mi');
            return;
        }

        if (createForm.value.size % 512 !== 0) {
            toast.error('存储容量必须是 512Mi 的倍数');
            return;
        }

        await axios.post('/api/storages', {
            name: createForm.value.name,
            size: createForm.value.size,
        });

        toast.success('Storage 创建成功');
        showCreateDialog.value = false;
        resetCreateForm();
        loadStorages();
    } catch (error: any) {
        console.error('创建 Storage 失败:', error);
        const message = error.response?.data?.message || '创建 Storage 失败';
        toast.error(message);
    } finally {
        creating.value = false;
    }
};

const resetCreateForm = () => {
    createForm.value = {
        name: '',
        size: 512,
    };
};

const viewStorage = (storage: Storage) => {
    selectedStorage.value = storage;
    showViewDialog.value = true;
};

const expandStorage = (storage: Storage) => {
    storageToExpand.value = storage;
    // 设置默认新容量为当前容量 + 512Mi
    const currentSize = parseSizeToMi(storage.formatted_size);
    expandForm.value.size = currentSize + 512;
    showExpandDialog.value = true;
};

const getMinExpandSize = (): number => {
    if (!storageToExpand.value) return 512;
    const currentSize = parseSizeToMi(storageToExpand.value.formatted_size);
    return currentSize + 512;
};

const parseSizeToMi = (sizeStr: string): number => {
    if (!sizeStr) return 0;

    const trimmed = sizeStr.trim();

    if (trimmed.endsWith('Mi')) {
        return parseInt(trimmed.replace('Mi', ''));
    }

    if (trimmed.endsWith('Gi')) {
        return parseInt(trimmed.replace('Gi', '')) * 1024;
    }

    // 默认假设是 Mi
    return parseInt(trimmed) || 0;
};

const doExpandStorage = async () => {
    if (!workspace || !storageToExpand.value) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        expanding.value = true;

        if (expandForm.value.size % 512 !== 0) {
            toast.error('存储容量必须是 512Mi 的倍数');
            return;
        }

        const currentSize = parseSizeToMi(storageToExpand.value.formatted_size);
        if (expandForm.value.size <= currentSize) {
            toast.error('新容量必须大于当前容量');
            return;
        }

        await axios.patch(`/api/storages/${storageToExpand.value.name}/expand`, {
            size: expandForm.value.size,
        });

        toast.success('Storage 扩容成功');
        showExpandDialog.value = false;
        storageToExpand.value = null;
        loadStorages();
    } catch (error: any) {
        console.error('扩容 Storage 失败:', error);
        const message = error.response?.data?.message || '扩容 Storage 失败';
        toast.error(message);
    } finally {
        expanding.value = false;
    }
};

const confirmDeleteStorage = (storage: Storage) => {
    storageToDelete.value = storage;
    showDeleteDialog.value = true;
};

const deleteStorage = async () => {
    if (!workspace || !storageToDelete.value) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        deleting.value = true;
        await axios.delete(`/api/storages/${storageToDelete.value.name}`);
        toast.success('Storage 删除成功');
        showDeleteDialog.value = false;
        storageToDelete.value = null;
        loadStorages();
    } catch (error: any) {
        console.error('删除 Storage 失败:', error);
        const message = error.response?.data?.message || '删除 Storage 失败';
        toast.error(message);
    } finally {
        deleting.value = false;
    }
};

onMounted(() => {
    loadStorages();
});
</script>
