<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="创建存储" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">创建存储</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">创建新的持久化存储卷</p>
                </div>
                <Button variant="outline" @click="router.visit(route('storages.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <Card>
                <CardContent class="p-6">
                    <div class="space-y-6">
                        <div>
                            <Label for="storageName">持久化存储卷名称</Label>
                            <Input id="storageName" v-model="form.name" placeholder="输入存储名称" />
                            <p class="mt-1 text-xs text-gray-500">名称必须以小写字母或数字开头和结尾，中间可以包含连字符</p>
                        </div>

                        <div>
                            <Label for="storageSize">存储容量 (M)</Label>
                            <Input
                                id="storageSize"
                                v-model.number="form.size"
                                type="number"
                                placeholder="最小 512，必须为 512 的倍数"
                                :min="512"
                                :step="512"
                            />
                            <p class="mt-1 text-xs text-gray-500">容量单位为 M，最小 512M，必须为 512 的倍数</p>
                        </div>

                        <div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                            <h4 class="mb-2 text-sm font-medium text-blue-900 dark:text-blue-100">Storage 配置说明</h4>
                            <ul class="space-y-1 text-xs text-blue-800 dark:text-blue-200">
                                <li>• 最小容量：512M</li>
                                <li>• 容量限制：必须为 512 的倍数</li>
                                <li>• 扩容：支持，但不支持缩减容量</li>
                            </ul>
                        </div>

                        <div class="flex space-x-4">
                            <Button @click="createStorage" :disabled="creating">
                                {{ creating ? '创建中...' : '创建存储' }}
                            </Button>
                            <Button variant="outline" @click="router.visit(route('storages.index'))"> 取消 </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <div class="p-4">
            <DataPreview api="/api/storages" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ArrowLeft } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const creating = ref(false);

const form = ref({
    name: '',
    size: 512,
});

const createStorage = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        creating.value = true;

        if (form.value.size < 512) {
            toast.error('存储容量不能小于 512Mi');
            return;
        }

        if (form.value.size % 512 !== 0) {
            toast.error('存储容量必须是 512Mi 的倍数');
            return;
        }

        await axios.post(`/api/workspaces/${workspace.id}/storages`, {
            name: form.value.name,
            size: form.value.size,
        });

        toast.success('存储创建成功');
        router.visit(route('storages.index'));
    } catch (error: any) {
        console.error('创建存储失败:', error);
        const message = error.response?.data?.message || '创建存储失败';
        toast.error(message);
    } finally {
        creating.value = false;
    }
};
</script>
