<template>
    <AppLayout>
        <Head :title="`部署: ${deploymentData?.name || 'Loading...'}`" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('deployments.index'))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回列表
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ deploymentData?.name || 'Loading...' }}
                        </h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">部署信息</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2" v-if="deploymentData">
                    <Button variant="outline" @click="showScaleDialog = true">
                        <Expand class="mr-2 h-4 w-4" />
                        扩容
                    </Button>
                    <Button variant="outline" @click="editDeployment">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑
                    </Button>
                    <Button variant="destructive" @click="showDeleteDialog = true">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="space-y-6">
                <Card>
                    <CardContent class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-1/4" />
                            <Skeleton class="h-4 w-1/2" />
                            <Skeleton class="h-4 w-3/4" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!--工作负载Details -->
            <div v-else-if="deploymentData" class="space-y-6">
                <!-- 基本信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>基本信息</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">名称</Label>
                                <p class="font-mono text-sm">{{ deploymentData.name }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">工作空间</Label>
                                <p class="font-mono text-sm">{{ workspace.name }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">副本数</Label>
                                <p class="text-sm">{{ deploymentData.replicas }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">状态</Label>
                                <Badge :variant="getStatusVariant(deploymentData.status)">
                                    {{ formatK8sStatus(deploymentData.status) }}
                                </Badge>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">更新策略</Label>
                                <p class="text-sm">{{ formatRollingUpdateStatus(deploymentData.strategy) }}</p>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间</Label>
                                <p class="text-sm">{{ formatDate(deploymentData.created_at) }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 容器信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle>容器信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-6">
                            <div v-for="(container, index) in deploymentData.containers" :key="index" class="rounded-lg border p-4">
                                <h4 class="mb-4 font-medium">{{ container.name }}</h4>

                                <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">镜像</Label>
                                        <p class="font-mono text-sm">{{ container.image }}</p>
                                    </div>
                                    <div v-if="container.resources">
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">资源限制</Label>
                                        <p class="text-sm">
                                            内存: {{ container.resources.memory }}M, CPU: {{ container.resources.cpu }}毫核(大约
                                            {{ Math.round(container.resources.cpu / 1000) }} 核)
                                        </p>
                                    </div>
                                </div>

                                <!-- 端口 -->
                                <div v-if="container.ports && container.ports.length > 0" class="mb-4">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">端口</Label>
                                    <div class="mt-2 space-y-2">
                                        <div v-for="port in container.ports" :key="port.container_port" class="text-sm">
                                            {{ port.name ? `${port.name}: ` : '' }}{{ port.container_port }}/{{ port.protocol || 'TCP' }}
                                        </div>
                                    </div>
                                </div>

                                <!-- 环境变量 -->
                                <div v-if="container.env && container.env.length > 0" class="mb-4">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">环境变量</Label>
                                    <div class="mt-2 space-y-1">
                                        <div v-for="envVar in container.env" :key="envVar.name" class="font-mono text-sm">
                                            {{ envVar.name }}={{ envVar.value }}
                                        </div>
                                    </div>
                                </div>

                                <!-- 卷挂载 -->
                                <div v-if="container.volume_mounts && container.volume_mounts.length > 0">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">卷挂载</Label>
                                    <div class="mt-2 space-y-1">
                                        <div v-for="mount in container.volume_mounts" :key="mount.storage_name + mount.mount_path" class="text-sm">
                                            {{ mount.storage_name }} → {{ mount.mount_path }}
                                            <span v-if="mount.sub_path" class="text-gray-500">:{{ mount.sub_path }}</span>
                                            <span v-if="mount.read_only" class="ml-2 text-yellow-600">(只读)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!--拉取密钥s -->
                <Card v-if="deploymentData.image_pull_secrets && deploymentData.image_pull_secrets.length > 0">
                    <CardHeader>
                        <CardTitle>拉取密钥</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-2">
                            <div v-for="secret in deploymentData.image_pull_secrets" :key="secret" class="font-mono text-sm">
                                {{ secret }}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Error State -->
            <Card v-else-if="error">
                <CardContent class="p-6 text-center">
                    <div class="text-red-600 dark:text-red-400">
                        <AlertCircle class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-lg font-medium">加载失败</p>
                        <p class="text-sm">{{ error }}</p>
                        <Button class="mt-4" @click="loadDeployment">重试</Button>
                    </div>
                </CardContent>
            </Card>

            <!-- 扩容对话框 -->
            <Dialog v-model:open="showScaleDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容工作负载</DialogTitle>
                        <DialogDescription> 调整 {{ deploymentData?.name }} 的副本数 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="replicas">副本数</Label>
                            <Input id="replicas" v-model.number="scaleForm.replicas" type="number" min="0" max="100" class="mt-1" />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showScaleDialog = false">取消</Button>
                        <Button @click="scaleDeployment" :disabled="scaling">
                            <Loader2 v-if="scaling" class="mr-2 h-4 w-4 animate-spin" />
                            确认扩容
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除工作负载</DialogTitle>
                        <DialogDescription> 确定要删除工作负载 "{{ deploymentData?.name }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deleteDeployment" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatK8sStatus, formatRollingUpdateStatus } from '@/lib/formatK8sStatus';
import type { Deployment, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Edit, Expand, Loader2, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    deploymentName: string;
    workspace: Workspace;
}

const workspace = computed(() => props.workspace);
const props = defineProps<Props>();

const deploymentData = ref<Deployment | null>(null);
const loading = ref(true);
const error = ref<string | null>(null);
const showScaleDialog = ref(false);
const showDeleteDialog = ref(false);
const scaling = ref(false);
const deleting = ref(false);

const scaleForm = ref({
    replicas: 1,
});

const loadDeployment = async () => {
    if (!workspace.value) {
        error.value = '请先选择一个工作空间';
        loading.value = false;
        return;
    }

    try {
        loading.value = true;
        error.value = null;
        const response = await axios.get(`/api/deployments/${props.deploymentName}`);
        deploymentData.value = response.data;
        scaleForm.value.replicas = response.data.replicas;
    } catch (err: any) {
        console.error('加载工作负载详情失败:', err);
        error.value = err.response?.data?.message || '加载工作负载详情失败';
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'failed':
            return 'destructive';
        default:
            return 'outline';
    }
};

const editDeployment = () => {
    router.visit(route('deployments.edit', { deployment: props.deploymentName }));
};

const scaleDeployment = async () => {
    if (!deploymentData.value) return;

    try {
        scaling.value = true;
        await axios.patch(`/api/deployments/${props.deploymentName}/scale`, {
            replicas: scaleForm.value.replicas,
        });

        toast.success('工作负载扩容成功');
        showScaleDialog.value = false;
        loadDeployment();
    } catch (error) {
        console.error('扩容工作负载失败:', error);
        toast.error('扩容工作负载失败');
    } finally {
        scaling.value = false;
    }
};

const deleteDeployment = async () => {
    if (!deploymentData.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/deployments/${props.deploymentName}`);

        toast.success('工作负载删除成功');
        router.visit(route('deployments.index'));
    } catch (error) {
        console.error('删除工作负载失败:', error);
        toast.error('删除工作负载失败');
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

onMounted(() => {
    loadDeployment();
});
</script>
