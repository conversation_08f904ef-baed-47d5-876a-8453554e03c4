<template>
    <AppLayout>
        <Head title="应用管理" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">应用管理</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        管理工作空间中的应用, 请前往 <Link href="/pods">Pod 管理</Link> 查看运行状态
                    </p>
                </div>
                <Button @click="router.visit(route('deployments.create'))">
                    <Plus class="mr-2 h-4 w-4" />
                    部署应用
                </Button>
            </div>

            <!-- 工作负载列表 -->
            <div v-if="loading" class="space-y-4">
                <div v-for="i in 3" :key="i" class="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                    <div class="space-y-3">
                        <Skeleton class="h-6 w-1/3" />
                        <Skeleton class="h-4 w-full" />
                        <Skeleton class="h-4 w-2/3" />
                    </div>
                </div>
            </div>

            <div v-else-if="deployments.length === 0" class="rounded-lg border border-gray-200 p-12 text-center dark:border-gray-700">
                <Container class="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">暂无工作负载</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">创建您的第一个工作负载来部署应用</p>
            </div>

            <div v-else class="space-y-3">
                <div
                    v-for="deployment in deployments"
                    :key="deployment.name"
                    :class="[
                        'group relative rounded-lg border p-4 transition-all duration-200',
                        'hover:border-gray-300 hover:shadow-md dark:hover:border-gray-600',
                        getStatusBorderClass(deployment.status),
                        getStatusBgClass(deployment.status),
                    ]"
                >
                    <!-- 状态指示器 -->
                    <div :class="['absolute top-0 left-0 h-full w-1 rounded-l-lg', getStatusIndicatorClass(deployment.status)]"></div>

                    <div class="flex items-start justify-between">
                        <div class="min-w-0 flex-1">
                            <!-- 主要信息行 -->
                            <div class="mb-2 flex items-center space-x-3">
                                <Container :class="['h-5 w-5', getStatusIconClass(deployment.status)]" />
                                <h3 class="truncate text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ deployment.name }}
                                </h3>
                                <Badge :variant="getStatusVariant(deployment.status)" class="shrink-0">
                                    {{ formatK8sStatus(deployment.status) }}
                                </Badge>
                            </div>

                            <!-- 详细信息网格 -->
                            <div class="mb-3 grid grid-cols-2 gap-4 md:grid-cols-4">
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">副本:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ deployment.replicas }}
                                    </span>
                                    <div v-if="deployment.status.toLowerCase() !== 'available'" class="text-xs text-amber-600 dark:text-amber-400">
                                        (等待就绪)
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">容器:</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ deployment.containers.length }}
                                    </span>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">策略:</span>
                                    <span class="truncate text-sm text-gray-900 dark:text-white">
                                        {{ formatStrategy(deployment.strategy) }}
                                    </span>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400">创建:</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ formatDate(deployment.created_at) }}
                                    </span>
                                </div>
                            </div>

                            <!-- 容器信息 -->
                            <div class="flex flex-wrap gap-2">
                                <div
                                    v-for="container in deployment.containers.slice(0, 3)"
                                    :key="container.name"
                                    class="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs dark:bg-gray-800"
                                >
                                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ container.name }}</span>
                                    <span class="ml-1 text-gray-500 dark:text-gray-400">{{ container.image.split(':')[1] || 'latest' }}</span>
                                </div>
                                <div
                                    v-if="deployment.containers.length > 3"
                                    class="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-500 dark:bg-gray-800 dark:text-gray-400"
                                >
                                    +{{ deployment.containers.length - 3 }} 更多
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="ml-4 flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                @click="showScaleDialog(deployment)"
                                :class="[
                                    'opacity-0 transition-opacity group-hover:opacity-100',
                                    'bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700',
                                ]"
                            >
                                <ExpandIcon class="mr-1 h-4 w-4" />
                                扩容
                            </Button>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" class="opacity-60 transition-opacity hover:opacity-100">
                                        <MoreHorizontal class="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem @click="viewDeployment(deployment)">
                                        <Eye class="mr-2 h-4 w-4" />
                                        查看详情
                                    </DropdownMenuItem>
                                    <DropdownMenuItem @click="editDeployment(deployment)">
                                        <Edit class="mr-2 h-4 w-4" />
                                        编辑
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem @click="confirmDeleteDeployment(deployment)" class="text-red-600 dark:text-red-400">
                                        <Trash2 class="mr-2 h-4 w-4" />
                                        删除
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 扩容对话框 -->
            <Dialog v-model:open="showScaleDialogFlag">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>扩容工作负载</DialogTitle>
                        <DialogDescription> 调整 {{ selectedDeployment?.name }} 的副本数 </DialogDescription>
                    </DialogHeader>

                    <div class="space-y-4">
                        <div>
                            <Label for="replicas">副本数</Label>
                            <Input id="replicas" v-model.number="scaleForm.replicas" type="number" min="0" max="100" class="mt-1" />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showScaleDialogFlag = false">取消</Button>
                        <Button @click="scaleDeployment" :disabled="scaling">
                            <Loader2 v-if="scaling" class="mr-2 h-4 w-4 animate-spin" />
                            确认扩容
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除工作负载</DialogTitle>
                        <DialogDescription> 确定要删除工作负载"{{ deploymentToDelete?.name }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deleteDeployment" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatK8sStatus, formatStrategy } from '@/lib/formatK8sStatus';
import type { Deployment } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { Container, Edit, Expand as ExpandIcon, Eye, Loader2, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const deployments = ref<Deployment[]>([]);
const loading = ref(true);
const selectedDeployment = ref<Deployment | null>(null);
const showScaleDialogFlag = ref(false);
const showDeleteDialog = ref(false);
const deploymentToDelete = ref<Deployment | null>(null);
const scaling = ref(false);
const deleting = ref(false);

const scaleForm = ref({
    replicas: 1,
});

const loadDeployments = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        loading.value = true;
        const response = await axios.get('/api/deployments');
        deployments.value = response.data;
    } catch (error) {
        console.error('加载工作负载列表失败:', error);
        toast.error('加载工作负载列表失败');
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'failed':
            return 'destructive';
        default:
            return 'outline';
    }
};

const getStatusBorderClass = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'border-green-200 dark:border-green-800';
        case 'pending':
            return 'border-yellow-200 dark:border-yellow-800';
        case 'failed':
            return 'border-red-200 dark:border-red-800';
        default:
            return 'border-gray-200 dark:border-gray-700';
    }
};

const getStatusBgClass = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'bg-green-50/50 dark:bg-green-950/20';
        case 'pending':
            return 'bg-yellow-50/50 dark:bg-yellow-950/20';
        case 'failed':
            return 'bg-red-50/50 dark:bg-red-950/20';
        default:
            return 'bg-white dark:bg-gray-900';
    }
};

const getStatusIndicatorClass = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'bg-green-500';
        case 'pending':
            return 'bg-yellow-500';
        case 'failed':
            return 'bg-red-500';
        default:
            return 'bg-gray-400';
    }
};

const getStatusIconClass = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'text-green-600 dark:text-green-400';
        case 'pending':
            return 'text-yellow-600 dark:text-yellow-400';
        case 'failed':
            return 'text-red-600 dark:text-red-400';
        default:
            return 'text-gray-400';
    }
};

const viewDeployment = (deployment: Deployment) => {
    router.visit(route('deployments.show', { deployment: deployment.name }));
};

const editDeployment = (deployment: Deployment) => {
    router.visit(route('deployments.edit', { deployment: deployment.name }));
};

const showScaleDialog = (deployment: Deployment) => {
    selectedDeployment.value = deployment;
    scaleForm.value.replicas = deployment.replicas;
    showScaleDialogFlag.value = true;
};

const scaleDeployment = async () => {
    if (!selectedDeployment.value) return;

    try {
        scaling.value = true;
        await axios.patch(`/api/deployments/${selectedDeployment.value.name}/scale`, {
            replicas: scaleForm.value.replicas,
        });

        toast.success('工作负载扩容成功');
        showScaleDialogFlag.value = false;
        loadDeployments();
    } catch (error) {
        console.error('扩容工作负载失败:', error);
        toast.error('扩容工作负载失败');
    } finally {
        scaling.value = false;
    }
};

const confirmDeleteDeployment = (deployment: Deployment) => {
    deploymentToDelete.value = deployment;
    showDeleteDialog.value = true;
};

const deleteDeployment = async () => {
    if (!deploymentToDelete.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/deployments/${deploymentToDelete.value.name}`);

        toast.success('工作负载删除成功');
        showDeleteDialog.value = false;
        loadDeployments();
    } catch (error) {
        console.error('删除工作负载失败:', error);
        toast.error('删除工作负载失败');
    } finally {
        deleting.value = false;
    }
};

onMounted(() => {
    loadDeployments();
});
</script>
