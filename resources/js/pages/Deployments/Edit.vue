<template>
    <AppLayout>
        <Head title="编辑工作负载" />

        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('deployments.show', { deployment: deploymentName }))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回详情
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">编辑工作负载</h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">修改 {{ deploymentName }} 的配置</p>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="space-y-6">
                <Card>
                    <CardContent class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-1/4" />
                            <Skeleton class="h-4 w-1/2" />
                            <Skeleton class="h-4 w-3/4" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Error State -->
            <Card v-else-if="error" class="border-red-200 dark:border-red-800">
                <CardContent class="p-6 text-center">
                    <div class="text-red-600 dark:text-red-400">
                        <AlertCircle class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-lg font-medium">加载失败</p>
                        <p class="text-sm">{{ error }}</p>
                        <Button class="mt-4" @click="loadDeployment">重试</Button>
                    </div>
                </CardContent>
            </Card>

            <!-- Edit Form -->
            <div v-else-if="form" class="space-y-6">
                <WorkloadForm v-model="form" workload-type="deployment" :is-edit="true" />

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4">
                    <Button variant="outline" @click="router.visit(route('deployments.show', { deployment: deploymentName }))"> 取消 </Button>
                    <Button type="submit" :disabled="updating" @click="updateDeployment">
                        <Loader2 v-if="updating" class="mr-2 h-4 w-4 animate-spin" />
                        更新工作负载
                    </Button>
                </div>

                <div class="p-4">
                    <DataPreview api="/api/deployments" method="PUT" :data-ref="form" />
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import WorkloadForm from '@/components/workloads/WorkloadForm.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { CreateDeploymentData } from '@/types';
import { Head, router, usePage } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Loader2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    deploymentName: string;
}

const props = defineProps<Props>();

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const form = ref<CreateDeploymentData | null>(null);
const loading = ref(true);
const updating = ref(false);
const error = ref<string | null>(null);

const loadDeployment = async () => {
    if (!workspace) {
        error.value = '请先选择一个工作空间';
        loading.value = false;
        return;
    }

    try {
        loading.value = true;
        error.value = null;
        const response = await axios.get(`/api/deployments/${props.deploymentName}`);
        const deployment = response.data;

        // 转换为表单数据格式
        form.value = {
            name: deployment.name,
            replicas: deployment.replicas,
            image_pull_secrets: deployment.image_pull_secrets || [],
            containers: deployment.containers.map((container: any) => ({
                name: container.name,
                image: container.image,
                working_dir: container.working_dir || '',
                command: container.command || [],
                args: container.args || [],
                ports: Array.isArray(container.ports)
                    ? container.ports.map((port: any) => ({
                          name: port.name || '',
                          container_port: port.container_port,
                          protocol: port.protocol || 'TCP',
                      }))
                    : [],
                env: Array.isArray(container.env) ? container.env : [],
                env_from_configmap: container.env_from_configmap || [],
                env_from_secret: container.env_from_secret || [],
                resources: container.resources || { memory: 512, cpu: 500 },
                volume_mounts: Array.isArray(container.volume_mounts)
                    ? container.volume_mounts.map((mount: any) => ({
                          mount_path: mount.mount_path,
                          storage_name: mount.storage_name,
                          sub_path: mount.sub_path || '',
                          read_only: mount.read_only || false,
                      }))
                    : [],
                configmap_mounts: container.configmap_mounts || [],
                secret_mounts: container.secret_mounts || [],
                liveness_probe: container.liveness_probe || undefined,
                readiness_probe: container.readiness_probe || undefined,
                startup_probe: container.startup_probe || undefined,
            })),
        };
    } catch (err: any) {
        console.error('加载工作负载详情失败:', err);
        error.value = err.response?.data?.message || '加载工作负载详情失败';
    } finally {
        loading.value = false;
    }
};

const updateDeployment = async () => {
    if (!form.value) return;

    try {
        updating.value = true;

        // 过滤空的配置项
        const cleanedData = {
            ...form.value,
            containers: form.value.containers.map((container) => ({
                ...container,
                working_dir: container.working_dir?.trim() || undefined,
                command: container.command?.filter((cmd) => cmd.trim()) || [],
                args: container.args?.filter((arg) => arg.trim()) || [],
                ports: container.ports?.filter((p) => p.container_port) || [],
                env: container.env?.filter((e) => e.name && e.value) || [],
                env_from_configmap: container.env_from_configmap?.filter((e) => e.configmap_name) || [],
                env_from_secret: container.env_from_secret?.filter((e) => e.secret_name) || [],
                volume_mounts:
                    container.volume_mounts
                        ?.filter((m) => m.mount_path && m.storage_name)
                        .map((m) => ({
                            mount_path: m.mount_path,
                            storage_name: m.storage_name,
                            sub_path: m.sub_path || undefined,
                            read_only: m.read_only || false,
                        })) || [],
                configmap_mounts: container.configmap_mounts?.filter((m) => m.configmap_name && m.mount_path) || [],
                secret_mounts: container.secret_mounts?.filter((m) => m.secret_name && m.mount_path) || [],
                liveness_probe: container.liveness_probe || undefined,
                readiness_probe: container.readiness_probe || undefined,
                startup_probe: container.startup_probe || undefined,
            })),
            image_pull_secrets: form.value.image_pull_secrets?.filter((s) => s.trim()) || [],
        };

        await axios.put(`/api/deployments/${props.deploymentName}`, cleanedData);

        toast.success('工作负载更新成功');
        router.visit(route('deployments.show', { deployment: props.deploymentName }));
    } catch (error: any) {
        console.error('更新工作负载失败:', error);
        const message = error.response?.data?.message || '更新工作负载失败';
        toast.error(message);
    } finally {
        updating.value = false;
    }
};

onMounted(() => {
    loadDeployment();
});
</script>
