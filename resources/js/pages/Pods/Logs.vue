<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { Pod, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Download, RefreshCw } from 'lucide-vue-next';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    podName: string;
    currentContainer?: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const pod = ref<Pod | null>(null);
const isLoadingPod = ref(true);

const selectedContainer = ref(props.currentContainer || '');
const tailLines = ref(100);
const showTimestamps = ref(true);
const showPrevious = ref(false);
const autoRefresh = ref(false);
const currentLogs = ref('');
const isLoading = ref(false);

let refreshInterval: number | null = null;

const fetchPodData = async () => {
    isLoadingPod.value = true;
    try {
        const response = await axios.get(`/api/pods/${props.podName}`);
        pod.value = response.data;
        if (!selectedContainer.value && pod.value && pod.value.containers.length > 0) {
            selectedContainer.value = pod.value.containers[0].name;
        }
    } catch (error) {
        console.error('Failed to fetch pod data:', error);
    } finally {
        isLoadingPod.value = false;
    }
};

const fetchLogs = async () => {
    if (!selectedContainer.value) return;

    isLoading.value = true;
    try {
        const params = new URLSearchParams({
            container: selectedContainer.value,
            tail_lines: tailLines.value.toString(),
            timestamps: showTimestamps.value ? '1' : '0',
            previous: showPrevious.value ? '1' : '0',
        });

        const response = await axios.get(`/api/pods/${props.podName}/logs?${params}`);
        currentLogs.value = response.data.logs;
    } catch (error) {
        console.error('获取日志失败:', error);
        currentLogs.value = '获取日志失败。';
    } finally {
        isLoading.value = false;
    }
};

const refreshLogs = () => {
    fetchLogs();
};

const downloadLogs = () => {
    if (!pod.value) return;
    const blob = new Blob([currentLogs.value], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${pod.value.name}-${selectedContainer.value}-logs.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
};

const updateAutoRefresh = (enabled: boolean) => {
    autoRefresh.value = enabled;

    if (enabled) {
        refreshInterval = window.setInterval(fetchLogs, 5000); // 每5秒刷新
    } else {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
};

watch(selectedContainer, (newContainer) => {
    if (newContainer) {
        // 更新 URL 参数
        const url = new URL(window.location.href);
        url.searchParams.set('container', newContainer);
        window.history.replaceState({}, '', url.toString());
        fetchLogs();
    }
});

onMounted(async () => {
    await fetchPodData();
    if (selectedContainer.value) {
        await fetchLogs();
    }
    // 滚动到底部
    setTimeout(() => {
        const logsContainer = document.querySelector('.logs-container');
        if (logsContainer) {
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
    }, 100);
});

onUnmounted(() => {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>

<template>
    <Head :title="`Pod 日志 - ${props.podName}`" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="outline" size="sm" @click="router.visit(route('pods.show', props.podName))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回详情
                    </Button>
                    <div>
                        <h2 class="text-3xl font-bold tracking-tight">{{ props.podName }}</h2>
                        <p class="text-muted-foreground">Pod 日志</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <Button variant="outline" @click="downloadLogs">
                        <Download class="mr-2 h-4 w-4" />
                        下载日志
                    </Button>
                    <Button variant="outline" @click="refreshLogs" :disabled="isLoading">
                        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': isLoading }" />
                        刷新
                    </Button>
                </div>
            </div>

            <div v-if="isLoadingPod">
                <Card>
                    <CardHeader>
                        <CardTitle>日志配置</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="grid grid-cols-1 items-end gap-4 md:grid-cols-5">
                            <div class="space-y-2" v-for="i in 5" :key="i">
                                <Skeleton class="h-4 w-12" />
                                <Skeleton class="h-10 w-full" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
            <Card v-else-if="pod">
                <CardHeader>
                    <CardTitle>日志配置</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 items-end gap-4 md:grid-cols-5">
                        <div class="space-y-2">
                            <Label for="container">容器</Label>
                            <Select v-model="selectedContainer">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择容器" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="container in pod.containers" :key="container.name" :value="container.name">
                                        {{ container.name }}
                                        <Badge v-if="!container.ready" variant="secondary" class="ml-2">未就绪</Badge>
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div class="space-y-2">
                            <Label for="tail-lines">显示行数</Label>
                            <Input id="tail-lines" type="number" v-model="tailLines" :min="1" :max="1000" @change="fetchLogs" />
                        </div>

                        <div class="flex items-center space-x-2">
                            <Switch
                                id="timestamps"
                                :checked="showTimestamps"
                                @update:checked="
                                    (value: boolean) => {
                                        showTimestamps = value;
                                        fetchLogs();
                                    }
                                "
                            />
                            <Label for="timestamps">显示时间戳</Label>
                        </div>

                        <div class="flex items-center space-x-2">
                            <Switch
                                id="previous"
                                :checked="showPrevious"
                                @update:checked="
                                    (value: boolean) => {
                                        showPrevious = value;
                                        fetchLogs();
                                    }
                                "
                            />
                            <Label for="previous">显示上次日志</Label>
                        </div>

                        <div class="flex items-center space-x-2">
                            <Switch id="auto-refresh" :checked="autoRefresh" @update:checked="updateAutoRefresh" />
                            <Label for="auto-refresh">自动刷新</Label>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center justify-between">
                        <span>日志内容</span>
                        <Badge variant="outline">{{ selectedContainer }}</Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent class="p-0">
                    <div
                        class="logs-container h-96 max-h-[600px] overflow-auto bg-black p-4 font-mono text-sm text-green-400"
                        style="white-space: pre-wrap; word-break: break-all"
                    >
                        <div v-if="isLoading" class="text-muted-foreground">加载中...</div>
                        <div v-else-if="!currentLogs" class="text-muted-foreground">暂无日志</div>
                        <div v-else>{{ currentLogs }}</div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>

<style scoped>
.logs-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.logs-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.logs-container::-webkit-scrollbar-track {
    background: transparent;
}

.logs-container::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
}
</style>
