<script setup lang="ts">
import ContainerStatusGrid from '@/components/ContainerStatusGrid.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatPodStatus } from '@/lib/formatK8sStatus';
import type { EnrichedPod, K8sEvent, NamespaceMetrics, Pod, PodWarningStatus, Workspace } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { AlertCircle, AlertTriangle, CheckCircle2, Clock, MoreHorizontal, RefreshCw, Trash2, XCircle } from 'lucide-vue-next';
import { computed, onMounted, ref, withDefaults } from 'vue';

const props = withDefaults(
    defineProps<{
        pods?: Pod[];
        workspace: Workspace;
    }>(),
    {
        pods: () => [],
    },
);

const loading = ref(false);
const selectedPod = ref<EnrichedPod | null>(null);
const showDeleteDialog = ref(false);

// 动态获取的 pods 数据
const dynamicPods = ref<Pod[]>([]);
const loadingPods = ref(true);

// 命名空间级别的数据
const namespaceEvents = ref<K8sEvent[]>([]);
const namespaceMetrics = ref<NamespaceMetrics>({});
// 警告事件映射
const podWarnings = ref<Record<string, PodWarningStatus>>({});

const loadingMetrics = ref(false);

// 使用动态获取的 pods 数据，如果为空则使用 props
const currentPods = computed(() => {
    return dynamicPods.value.length > 0 ? dynamicPods.value : props.pods || [];
});

// 获取已格式化的数据
const enrichedPods = computed<EnrichedPod[]>(() => {
    if (!currentPods.value || currentPods.value.length === 0) return [];

    return currentPods.value.map((pod: Pod): EnrichedPod => {
        // 查找与此Pod相关的事件
        const podEvents = namespaceEvents.value.filter(
            (event) => event.involved_object && event.involved_object.kind === 'Pod' && event.involved_object.name === pod.name,
        );

        // 查找Pod的指标
        const podMetrics = namespaceMetrics.value[pod.name];

        // 计算CPU和内存使用量
        let cpuUsage = '0m';
        let memoryUsage = '0M';

        if (podMetrics && podMetrics.containers) {
            // 计算CPU总和
            const totalCpu = podMetrics.containers.reduce((sum, container) => {
                const cpuStr = container.usage.cpu;
                if (cpuStr.endsWith('m')) {
                    return sum + parseInt(cpuStr.replace('m', ''));
                } else {
                    return sum + parseFloat(cpuStr) * 1000;
                }
            }, 0);
            cpuUsage = `${Math.round(totalCpu)}m`;

            // 计算内存总和
            let totalMemoryMi = podMetrics.containers.reduce((sum, container) => {
                const memStr = container.usage.memory;
                if (memStr.endsWith('K')) {
                    return sum + parseInt(memStr.replace('K', '')) / 1024;
                } else if (memStr.endsWith('M')) {
                    return sum + parseInt(memStr.replace('M', ''));
                } else if (memStr.endsWith('G')) {
                    return sum + parseInt(memStr.replace('G', '')) * 1024;
                } else {
                    return sum + parseInt(memStr) / (1024 * 1024);
                }
            }, 0);

            if (totalMemoryMi >= 1024) {
                memoryUsage = `${(totalMemoryMi / 1024).toFixed(1)}Gi`;
            } else {
                memoryUsage = `${Math.round(totalMemoryMi)}M`;
            }
        }

        // 查找警告事件
        const warningEvents = podEvents.filter((event) => event.type === 'Warning');
        const warningStatus = {
            has_warnings: warningEvents.length > 0,
            warning_events: warningEvents,
            warning_count: warningEvents.length,
        };

        // 保存警告状态以供其他地方使用
        podWarnings.value[pod.name] = warningStatus;

        // 创建一个新的 EnrichedPod 对象，而不是扩展原始 pod
        return {
            name: pod.name,
            namespace: pod.namespace,
            status: pod.status,
            phase: pod.phase,
            pod_ip: pod.pod_ip || null,
            containers: pod.containers,
            conditions: pod.conditions,
            start_time: pod.start_time || null,
            restart_count: pod.restart_count,
            created_at: pod.created_at || null,
            events: podEvents,
            metrics: { cpu: cpuUsage, memory: memoryUsage },
            warningStatus,
            age: formatAge(pod.created_at),
        };
    });
});

// 状态统计数据
const statusSummary = computed(() => {
    const summary = {
        running: 0,
        pending: 0,
        failed: 0,
        succeeded: 0,
        unknown: 0,
        warnings: 0,
        total: enrichedPods.value.length,
    };

    enrichedPods.value.forEach((pod) => {
        const status = pod.status.toLowerCase();
        if (status === 'running') summary.running++;
        else if (status === 'pending') summary.pending++;
        else if (status === 'failed') summary.failed++;
        else if (status === 'succeeded') summary.succeeded++;
        else summary.unknown++;

        if (pod.warningStatus.has_warnings) summary.warnings++;
    });

    return summary;
});

// 获取 pods 列表
const fetchPods = async () => {
    try {
        loadingPods.value = true;
        const response = await axios.get('/api/pods');
        dynamicPods.value = response.data;
    } catch (error) {
        console.error('获取 Pod 列表失败:', error);
    } finally {
        loadingPods.value = false;
    }
};

// 获取命名空间级别的事件和指标数据
const fetchBatchData = async () => {
    if (!currentPods.value || currentPods.value.length === 0) return;

    loadingMetrics.value = true;

    try {
        // 并行获取命名空间级别的事件和指标
        const [eventsResponse, metricsResponse] = await Promise.all([
            axios.get('/api/workspaces/current/events'),
            axios.get('/api/workspaces/current/metrics'),
        ]);

        namespaceEvents.value = eventsResponse.data;
        namespaceMetrics.value = metricsResponse.data;
    } catch (error) {
        console.error('获取命名空间数据失败:', error);
    } finally {
        loadingMetrics.value = false;
    }
};

const formatAge = (createdAt?: string): string => {
    if (!createdAt) return 'Unknown';

    const now = new Date();
    const created = new Date(createdAt);
    const diffMs = now.getTime() - created.getTime();

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays > 0) return `${diffDays}d`;
    if (diffHours > 0) return `${diffHours}h`;
    if (diffMinutes > 0) return `${diffMinutes}m`;
    return '<1m';
};

const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'default';
        case 'pending':
            return 'secondary';
        case 'succeeded':
            return 'default';
        case 'failed':
            return 'destructive';
        case 'unknown':
            return 'outline';
        default:
            return 'secondary';
    }
};

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'running':
            return 'bg-green-500';
        case 'pending':
            return 'bg-blue-500';
        case 'succeeded':
            return 'bg-green-500';
        case 'failed':
            return 'bg-red-500';
        case 'unknown':
            return 'bg-gray-500';
        default:
            return 'bg-gray-500';
    }
};

const handleDeletePod = (pod: EnrichedPod) => {
    selectedPod.value = pod;
    showDeleteDialog.value = true;
};

const confirmDelete = async () => {
    if (!selectedPod.value) return;

    try {
        loading.value = true;
        await axios.delete(`/api/pods/${selectedPod.value.name}`);

        // 重新获取数据而不是刷新页面
        await fetchPods();
    } catch (error) {
        console.error('删除 Pod 失败:', error);
        alert('删除 Pod 失败');
    } finally {
        loading.value = false;
        showDeleteDialog.value = false;
        selectedPod.value = null;
    }
};

const handleRestartPod = async (pod: EnrichedPod) => {
    try {
        loading.value = true;
        await axios.post(`/api/pods/${pod.name}/restart`);

        // 重新获取数据而不是刷新页面
        await fetchPods();
    } catch (error) {
        console.error('重启 Pod 失败:', error);
        alert('重启 Pod 失败');
    } finally {
        loading.value = false;
    }
};

const refresh = async () => {
    await fetchPods();
    await fetchBatchData();
};

// 组件挂载时获取数据
onMounted(async () => {
    await fetchPods();
    await fetchBatchData();
});
</script>

<template>
    <Head title="Pod 管理" />

    <AppLayout>
        <div class="container mx-auto px-6 py-8">
            <div class="mb-6 flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Pod 管理</h1>
                    <p class="text-muted-foreground">管理和监控您的 Pod 实例</p>
                </div>

                <div class="flex items-center space-x-2">
                    <Button variant="outline" size="sm" @click="refresh" :disabled="loadingPods || loading">
                        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loadingPods || loading }" />
                        刷新
                    </Button>
                </div>
            </div>

            <!-- 状态概览 -->
            <div v-if="!loadingPods && enrichedPods.length > 0" class="mb-8">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-5">
                    <!-- 运行中 -->
                    <div class="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
                        <div>
                            <div class="text-sm font-medium text-muted-foreground">运行中</div>
                            <div class="text-2xl font-bold">{{ statusSummary.running }}</div>
                        </div>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
                            <CheckCircle2 class="h-5 w-5 text-green-500" />
                        </div>
                    </div>

                    <!-- 等待中 -->
                    <div class="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
                        <div>
                            <div class="text-sm font-medium text-muted-foreground">等待中</div>
                            <div class="text-2xl font-bold">{{ statusSummary.pending }}</div>
                        </div>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
                            <Clock class="h-5 w-5 text-blue-500" />
                        </div>
                    </div>

                    <!-- 失败 -->
                    <div class="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
                        <div>
                            <div class="text-sm font-medium text-muted-foreground">失败</div>
                            <div class="text-2xl font-bold">{{ statusSummary.failed }}</div>
                        </div>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                            <XCircle class="h-5 w-5 text-red-500" />
                        </div>
                    </div>

                    <!-- 警告 -->
                    <div class="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
                        <div>
                            <div class="text-sm font-medium text-muted-foreground">警告</div>
                            <div class="text-2xl font-bold">{{ statusSummary.warnings }}</div>
                        </div>
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30">
                            <AlertTriangle class="h-5 w-5 text-orange-500" />
                        </div>
                    </div>

                    <!-- 总计 -->
                    <div class="flex items-center justify-between rounded-lg border bg-card p-4 shadow-sm">
                        <div>
                            <div class="text-sm font-medium text-muted-foreground">总计</div>
                            <div class="text-2xl font-bold">{{ statusSummary.total }}</div>
                        </div>
                        <div class="flex -space-x-2">
                            <div v-if="statusSummary.running > 0" class="h-5 w-5 rounded-full bg-green-500"></div>
                            <div v-if="statusSummary.pending > 0" class="h-5 w-5 rounded-full bg-blue-500"></div>
                            <div v-if="statusSummary.failed > 0" class="h-5 w-5 rounded-full bg-red-500"></div>
                            <div v-if="statusSummary.unknown > 0" class="h-5 w-5 rounded-full bg-gray-500"></div>
                        </div>
                    </div>
                </div>

                <!-- 状态分布图 -->
                <div class="mt-6 flex items-center justify-center">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <div class="h-3 w-3 rounded-full bg-green-500"></div>
                            <span class="text-sm">运行中</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="h-3 w-3 rounded-full bg-blue-500"></div>
                            <span class="text-sm">等待中</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="h-3 w-3 rounded-full bg-red-500"></div>
                            <span class="text-sm">失败</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="h-3 w-3 rounded-full bg-gray-500"></div>
                            <span class="text-sm">其他</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pod 列表 -->
            <div class="rounded-lg border bg-background shadow-sm">
                <div class="flex items-center justify-between border-b p-4">
                    <h2 class="text-lg font-medium">Pod 列表</h2>
                    <Badge variant="secondary">
                        {{ loadingPods ? '加载中...' : `${currentPods.length} 个` }}
                    </Badge>
                </div>

                <!-- 加载状态 -->
                <div v-if="loadingPods" class="flex items-center justify-center py-12">
                    <RefreshCw class="h-8 w-8 animate-spin text-muted-foreground" />
                    <span class="ml-2 text-muted-foreground">加载中...</span>
                </div>

                <!-- Pod 列表 -->
                <div v-else-if="enrichedPods.length > 0" class="overflow-x-auto">
                    <div class="min-w-[1000px]">
                        <!-- 表头 -->
                        <div class="grid grid-cols-12 gap-4 bg-muted/30 px-4 py-3 text-sm font-medium text-muted-foreground">
                            <div class="col-span-3">Pod 名称</div>
                            <div class="col-span-2">容器</div>
                            <div class="col-span-1">CPU</div>
                            <div class="col-span-1">内存</div>
                            <div class="col-span-1">重启</div>
                            <div class="col-span-1">运行时间</div>
                            <div class="col-span-2">状态</div>
                            <div class="col-span-1 text-right">操作</div>
                        </div>

                        <Separator />

                        <!-- 数据行 -->
                        <div
                            v-for="(pod, index) in enrichedPods"
                            :key="pod.name"
                            class="grid grid-cols-12 gap-4 px-4 py-3 transition-colors hover:bg-muted/50"
                            :class="{ 'bg-muted/20': index % 2 === 0 }"
                        >
                            <!-- Pod Name -->
                            <div class="col-span-3 flex items-center space-x-2">
                                <div class="h-2 w-2 rounded-full" :class="getStatusColor(pod.status)"></div>
                                <Link :href="`/pods/${pod.name}`" class="truncate font-medium text-blue-600 hover:text-blue-800">
                                    {{ pod.name }}
                                </Link>

                                <!-- 警告图标 -->
                                <TooltipProvider v-if="pod.warningStatus.has_warnings">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <AlertTriangle class="h-4 w-4 text-orange-500" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <div class="max-w-xs">
                                                <p class="font-medium">警告事件 ({{ pod.warningStatus.warning_count }})</p>
                                                <div class="mt-1 space-y-1">
                                                    <div
                                                        v-for="event in pod.warningStatus.warning_events.slice(0, 3)"
                                                        :key="event.reason"
                                                        class="text-xs"
                                                    >
                                                        <span class="font-medium">{{ event.reason }}:</span>
                                                        {{ event.message }}
                                                    </div>
                                                    <p v-if="pod.warningStatus.warning_events.length > 3" class="text-xs text-muted-foreground">
                                                        还有 {{ pod.warningStatus.warning_events.length - 3 }} 个警告...
                                                    </p>
                                                </div>
                                            </div>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>

                            <!-- Containers -->
                            <div class="col-span-2">
                                <ContainerStatusGrid :containers="pod.containers" />
                            </div>

                            <!-- CPU -->
                            <div class="col-span-1 flex items-center">
                                <span v-if="loadingMetrics" class="text-muted-foreground">...</span>
                                <span v-else class="font-mono text-sm">{{ pod.metrics.cpu }}</span>
                            </div>

                            <!-- Memory -->
                            <div class="col-span-1 flex items-center">
                                <span v-if="loadingMetrics" class="text-muted-foreground">...</span>
                                <span v-else class="font-mono text-sm">{{ pod.metrics.memory }}</span>
                            </div>

                            <!-- Restarts -->
                            <div class="col-span-1 flex items-center">
                                <Badge :variant="pod.restart_count > 0 ? 'destructive' : 'secondary'" class="text-xs">
                                    {{ pod.restart_count }}
                                </Badge>
                            </div>

                            <!-- Age -->
                            <div class="col-span-1 flex items-center">
                                <span class="text-sm text-muted-foreground">{{ pod.age }}</span>
                            </div>

                            <!-- Status -->
                            <div class="col-span-2 flex items-center">
                                <Badge :variant="getStatusBadgeVariant(pod.status)">
                                    {{ formatPodStatus(pod.status) }}
                                </Badge>
                            </div>

                            <!-- Actions -->
                            <div class="col-span-1 flex items-center justify-end">
                                <DropdownMenu>
                                    <DropdownMenuTrigger as-child>
                                        <Button variant="ghost" size="sm">
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="handleRestartPod(pod)">
                                            <RefreshCw class="mr-2 h-4 w-4" />
                                            重启
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="handleDeletePod(pod)" class="text-red-600">
                                            <Trash2 class="mr-2 h-4 w-4" />
                                            删除
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="py-12 text-center">
                    <div class="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                        <AlertCircle class="h-6 w-6 text-muted-foreground" />
                    </div>
                    <h3 class="text-lg font-medium">暂无 Pod</h3>
                    <p class="mt-2 text-muted-foreground">当前工作空间中没有运行的 Pod</p>
                </div>
            </div>
        </div>

        <!-- 删除确认对话框 -->
        <Dialog v-model:open="showDeleteDialog">
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>确认删除</DialogTitle>
                    <DialogDescription> 您确定要删除 Pod "{{ selectedPod?.name }}" 吗？此操作无法撤销。 </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button variant="outline" @click="showDeleteDialog = false"> 取消 </Button>
                    <Button variant="destructive" @click="confirmDelete" :disabled="loading"> 删除 </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    </AppLayout>
</template>
