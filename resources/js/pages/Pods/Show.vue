<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { Pod, PodEvent, PodMetrics, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { Activity, AlertCircle, ArrowLeft, CheckCircle, Clock, FileText, RotateCcw, Server, Terminal, Trash2, XCircle } from 'lucide-vue-next';
import { computed, onMounted, ref } from 'vue';
import { toast } from 'vue-sonner';
import { route } from 'ziggy-js';

interface Props {
    podName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const workspace = computed(() => props.workspace);
const pod = ref<Pod | null>(null);
const events = ref<PodEvent[]>([]);
const metrics = ref<PodMetrics | null>(null);
const isLoading = ref(true);
const apiError = ref<string | null>(null);

// Dialog state
const showRestartDialog = ref(false);
const showDeleteDialog = ref(false);

// Terminal state
const activeTab = ref('overview');

const fetchData = async () => {
    isLoading.value = true;
    apiError.value = null;
    try {
        // 使用新的API获取Pod和指标数据
        const [podResponse, metricsResponse] = await Promise.all([
            axios.get(`/api/pods/${props.podName}`),
            axios.get(`/api/pods/${props.podName}/metrics`),
        ]);
        pod.value = podResponse.data;
        metrics.value = metricsResponse.data;

        // 使用新的资源事件API获取特定Pod的事件
        const eventsResponse = await axios.get('/api/resource-events', {
            params: {
                kind: 'Pod',
                name: props.podName,
            },
        });
        events.value = eventsResponse.data;
    } catch (error) {
        console.error('Failed to fetch pod details:', error);
        apiError.value = '获取 Pod 详情失败。';
        pod.value = null;
    } finally {
        isLoading.value = false;
    }
};

onMounted(fetchData);

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'Running':
            return 'default';
        case 'Pending':
            return 'secondary';
        case 'Failed':
            return 'destructive';
        case 'Completed':
            return 'outline';
        case 'Terminating':
            return 'secondary';
        default:
            return 'secondary';
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'Running':
            return 'bg-green-500';
        case 'Pending':
            return 'bg-blue-500';
        case 'Failed':
            return 'bg-red-500';
        case 'Completed':
            return 'bg-green-500';
        case 'Terminating':
            return 'bg-yellow-500';
        default:
            return 'bg-gray-500';
    }
};

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'Running':
            return CheckCircle;
        case 'Pending':
            return Clock;
        case 'Failed':
            return XCircle;
        case 'Completed':
            return CheckCircle;
        case 'Terminating':
            return Clock;
        default:
            return AlertCircle;
    }
};

const getContainerStateBadgeVariant = (state: string) => {
    if (state.includes('Running')) return 'default';
    if (state.includes('Waiting')) return 'secondary';
    if (state.includes('Terminated')) return 'destructive';
    return 'secondary';
};

const getEventTypeBadgeVariant = (type: string) => {
    switch (type) {
        case 'Normal':
            return 'default';
        case 'Warning':
            return 'destructive';
        default:
            return 'secondary';
    }
};

const formatTimestamp = (timestamp: string) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleString();
};

const restartPod = async () => {
    try {
        await axios.post(`/api/pods/${props.podName}/restart`);
        toast({
            title: '成功',
            description: `Pod "${props.podName}" 正在重启。`,
        });
        showRestartDialog.value = false;
        setTimeout(fetchData, 1000);
    } catch (error) {
        toast({
            title: '错误',
            description: `重启 Pod "${props.podName}" 失败。`,
            variant: 'destructive',
        });
        console.error('Failed to restart pod:', error);
    }
};

const deletePod = async () => {
    try {
        await axios.delete(`/api/pods/${props.podName}`);
        toast({
            title: '成功',
            description: `Pod "${props.podName}" 已被删除。`,
        });
        showDeleteDialog.value = false;
        router.visit(route('pods.index'));
    } catch (error) {
        toast({
            title: '错误',
            description: `删除 Pod "${props.podName}" 失败。`,
            variant: 'destructive',
        });
        console.error('Failed to delete pod:', error);
    }
};

const openTerminalInNewTab = (containerName?: string) => {
    const url = route('pods.terminal', { name: props.podName, container: containerName });
    window.open(url, '_blank');
};
</script>

<template>
    <Head :title="`Pod - ${podName}`" />

    <AppLayout>
        <div class="container mx-auto px-6 py-8">
            <!-- 顶部导航和操作区 -->
            <div class="mb-8">
                <div class="mb-2 flex items-center">
                    <Button variant="ghost" size="sm" @click="router.visit(route('pods.index'))" class="mr-2">
                        <ArrowLeft class="h-4 w-4" />
                    </Button>
                    <h1 class="text-3xl font-bold tracking-tight">{{ podName }}</h1>
                </div>
                <div class="flex items-center justify-between">
                    <p class="text-muted-foreground">Pod 详情信息</p>
                    <div class="flex items-center space-x-2">
                        <Button variant="outline" size="sm" @click="router.visit(route('pods.logs', podName))">
                            <FileText class="mr-2 h-4 w-4" />
                            日志
                        </Button>
                        <Button variant="outline" size="sm" @click="openTerminalInNewTab()">
                            <Terminal class="mr-2 h-4 w-4" />
                            终端
                        </Button>
                        <Button variant="outline" size="sm" @click="showRestartDialog = true">
                            <RotateCcw class="mr-2 h-4 w-4" />
                            重启
                        </Button>
                        <Button variant="destructive" size="sm" @click="showDeleteDialog = true">
                            <Trash2 class="mr-2 h-4 w-4" />
                            删除
                        </Button>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="flex flex-col items-center justify-center py-12">
                <div class="mb-4 flex items-center space-x-2">
                    <Skeleton class="h-12 w-12 rounded-full" />
                    <div>
                        <Skeleton class="mb-2 h-6 w-32" />
                        <Skeleton class="h-4 w-48" />
                    </div>
                </div>
                <div class="grid w-full max-w-3xl grid-cols-1 gap-4 md:grid-cols-2">
                    <Skeleton class="h-24 w-full" v-for="i in 4" :key="i" />
                </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="apiError" class="flex flex-col items-center justify-center py-12">
                <div class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                    <AlertCircle class="h-8 w-8 text-red-500" />
                </div>
                <h3 class="mb-2 text-xl font-medium">获取 Pod 信息失败</h3>
                <p class="text-muted-foreground">{{ apiError }}</p>
                <Button variant="outline" class="mt-4" @click="fetchData">
                    <RefreshCw class="mr-2 h-4 w-4" />
                    重试
                </Button>
            </div>

            <!-- Pod 详情内容 -->
            <div v-else-if="pod" class="space-y-6">
                <!-- 状态概览 -->
                <div class="rounded-lg border bg-card p-6 shadow-sm">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div class="mb-4 flex items-center space-x-4 md:mb-0">
                            <div :class="`flex h-12 w-12 items-center justify-center rounded-full ${getStatusColor(pod.status)}/15`">
                                <component
                                    :is="getStatusIcon(pod.status)"
                                    class="h-6 w-6"
                                    :class="`text-${getStatusColor(pod.status).replace('bg-', '')}`"
                                />
                            </div>
                            <div>
                                <div class="flex items-center space-x-2">
                                    <Badge :variant="getStatusBadgeVariant(pod.status)">
                                        {{ pod.status }}
                                    </Badge>
                                    <span class="text-sm text-muted-foreground">阶段: {{ pod.phase }}</span>
                                </div>
                                <p class="mt-1 text-sm">
                                    <span class="text-muted-foreground">创建于:</span> {{ formatTimestamp(pod.created_at || '') }}
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-8 gap-y-2">
                            <div>
                                <span class="text-sm text-muted-foreground">Pod IP:</span>
                                <span class="ml-2 text-sm font-medium">{{ pod.pod_ip || '-' }}</span>
                            </div>
                            <div>
                                <span class="text-sm text-muted-foreground">主机 IP:</span>
                                <span class="ml-2 text-sm font-medium">{{ pod.host_ip || '-' }}</span>
                            </div>
                            <div>
                                <span class="text-sm text-muted-foreground">重启次数:</span>
                                <span class="ml-2 text-sm font-medium">{{ pod.restart_count }}</span>
                            </div>
                            <div>
                                <span class="text-sm text-muted-foreground">工作空间:</span>
                                <span class="ml-2 text-sm font-medium">{{ workspace.name }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页内容 -->
                <Tabs v-model="activeTab" default-value="overview" class="space-y-4">
                    <TabsList class="grid w-full max-w-2xl grid-cols-5">
                        <TabsTrigger value="overview">概览</TabsTrigger>
                        <TabsTrigger value="containers">容器</TabsTrigger>
                        <TabsTrigger value="conditions">状态条件</TabsTrigger>
                        <TabsTrigger value="events">事件</TabsTrigger>
                        <TabsTrigger value="metrics" v-if="metrics">指标</TabsTrigger>
                    </TabsList>

                    <!-- 概览标签页 -->
                    <TabsContent value="overview" class="space-y-4">
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <!-- 容器概览 -->
                            <div class="rounded-lg border bg-card p-4">
                                <h3 class="mb-4 flex items-center text-lg font-medium">
                                    <Server class="mr-2 h-5 w-5 text-muted-foreground" />
                                    容器概览
                                </h3>
                                <div class="space-y-4">
                                    <div v-for="container in pod.containers" :key="container.name" class="border-b pb-3 last:border-0">
                                        <div class="mb-2 flex items-center justify-between">
                                            <span class="font-medium">{{ container.name }}</span>
                                            <Badge :variant="getContainerStateBadgeVariant(container.state)">
                                                {{ container.state }}
                                            </Badge>
                                        </div>
                                        <p class="mb-1 truncate text-sm text-muted-foreground">{{ container.image }}</p>
                                        <div class="mt-2 flex items-center space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                @click="router.visit(route('pods.logs', { name: pod.name, container: container.name }))"
                                                class="h-7 text-xs"
                                            >
                                                <FileText class="mr-1 h-3 w-3" />
                                                日志
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                @click="openTerminalInNewTab(container.name)"
                                                :disabled="!container.ready"
                                                class="h-7 text-xs"
                                            >
                                                <Terminal class="mr-1 h-3 w-3" />
                                                终端
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 最近事件 -->
                            <div class="rounded-lg border bg-card p-4">
                                <h3 class="mb-4 flex items-center text-lg font-medium">
                                    <Activity class="mr-2 h-5 w-5 text-muted-foreground" />
                                    最近事件
                                </h3>
                                <div v-if="events.length === 0" class="py-4 text-center text-sm text-muted-foreground">暂无事件</div>
                                <div v-else class="max-h-[250px] space-y-3 overflow-y-auto pr-1">
                                    <div
                                        v-for="event in events.slice(0, 5)"
                                        :key="`${event.reason}-${event.first_timestamp}`"
                                        class="border-b pb-2 last:border-0"
                                    >
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2">
                                                <AlertCircle v-if="event.type === 'Warning'" class="h-4 w-4 text-destructive" />
                                                <span class="text-sm font-medium">{{ event.reason }}</span>
                                            </div>
                                            <Badge :variant="getEventTypeBadgeVariant(event.type)" class="text-xs">
                                                {{ event.type }}
                                            </Badge>
                                        </div>
                                        <p class="mt-1 text-xs text-muted-foreground">{{ event.message }}</p>
                                    </div>
                                    <div v-if="events.length > 5" class="pt-2 text-center">
                                        <Button variant="ghost" size="sm" @click="activeTab = 'events'" class="h-7 text-xs">
                                            查看全部 {{ events.length }} 个事件
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>

                    <!-- 容器标签页 -->
                    <TabsContent value="containers" class="space-y-6">
                        <div v-for="container in pod.containers" :key="container.name" class="rounded-lg border bg-card p-4">
                            <div class="mb-4 flex items-center justify-between">
                                <h3 class="flex items-center text-lg font-medium">
                                    <Server class="mr-2 h-5 w-5 text-muted-foreground" />
                                    {{ container.name }}
                                </h3>
                                <div class="flex items-center space-x-2">
                                    <Badge :variant="getContainerStateBadgeVariant(container.state)">
                                        {{ container.state }}
                                    </Badge>
                                    <Badge variant="outline"> 重启: {{ container.restartCount }} </Badge>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div>
                                    <div class="mb-4">
                                        <h4 class="mb-1 text-sm font-medium text-muted-foreground">镜像</h4>
                                        <p class="text-sm break-all">{{ container.image }}</p>
                                    </div>

                                    <div class="mb-4">
                                        <h4 class="mb-1 text-sm font-medium text-muted-foreground">就绪状态</h4>
                                        <Badge :variant="container.ready ? 'default' : 'secondary'">
                                            {{ container.ready ? '就绪' : '未就绪' }}
                                        </Badge>
                                    </div>

                                    <div v-if="container.ports.length > 0" class="mb-4">
                                        <h4 class="mb-1 text-sm font-medium text-muted-foreground">端口</h4>
                                        <div class="space-y-1">
                                            <div v-for="port in container.ports" :key="port.name || port.container_port" class="text-sm">
                                                {{ port.container_port }}/{{ port.protocol || 'TCP' }}
                                                <span v-if="port.name" class="text-muted-foreground">({{ port.name }})</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div v-if="container.env.length > 0" class="mb-4">
                                        <h4 class="mb-1 text-sm font-medium text-muted-foreground">环境变量</h4>
                                        <div class="max-h-[200px] space-y-1 overflow-y-auto pr-1">
                                            <div v-for="env in container.env" :key="env.name" class="text-sm">
                                                <span class="font-medium">{{ env.name }}</span
                                                >: {{ env.value }}
                                            </div>
                                        </div>
                                    </div>

                                    <div v-if="container.volumeMounts.length > 0">
                                        <h4 class="mb-1 text-sm font-medium text-muted-foreground">卷挂载</h4>
                                        <div class="space-y-1">
                                            <div v-for="mount in container.volumeMounts" :key="mount.name || mount.storage_name" class="text-sm">
                                                <span class="font-medium">{{ mount.name || mount.storage_name }}</span> →
                                                {{ mount.mountPath || mount.mount_path }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 flex items-center space-x-2 border-t pt-4">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    @click="router.visit(route('pods.logs', { name: pod.name, container: container.name }))"
                                >
                                    <FileText class="mr-2 h-4 w-4" />
                                    查看日志
                                </Button>
                                <Button variant="outline" size="sm" @click="openTerminalInNewTab(container.name)" :disabled="!container.ready">
                                    <Terminal class="mr-2 h-4 w-4" />
                                    打开终端
                                </Button>
                            </div>
                        </div>
                    </TabsContent>

                    <!-- 状态条件标签页 -->
                    <TabsContent value="conditions">
                        <div class="rounded-lg border bg-card p-4">
                            <h3 class="mb-4 text-lg font-medium">Pod 状态条件</h3>
                            <div v-if="pod.conditions.length === 0" class="py-8 text-center">
                                <p class="text-muted-foreground">暂无状态条件</p>
                            </div>
                            <div v-else class="space-y-4">
                                <div v-for="condition in pod.conditions" :key="condition.type" class="rounded-lg border bg-background p-3">
                                    <div class="mb-2 flex items-center justify-between">
                                        <span class="font-medium">{{ condition.type }}</span>
                                        <Badge :variant="condition.status === 'True' ? 'default' : 'secondary'">
                                            {{ condition.status }}
                                        </Badge>
                                    </div>
                                    <div v-if="condition.reason" class="mb-1 text-sm">
                                        <span class="font-medium">原因:</span> {{ condition.reason }}
                                    </div>
                                    <div v-if="condition.message" class="mb-2 text-sm text-muted-foreground">
                                        {{ condition.message }}
                                    </div>
                                    <div v-if="condition.lastTransitionTime" class="text-xs text-muted-foreground">
                                        最后转换时间: {{ formatTimestamp(condition.lastTransitionTime) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>

                    <!-- 事件标签页 -->
                    <TabsContent value="events">
                        <div class="rounded-lg border bg-card p-4">
                            <h3 class="mb-4 flex items-center text-lg font-medium">
                                <Activity class="mr-2 h-5 w-5 text-muted-foreground" />
                                事件历史
                            </h3>
                            <div v-if="events.length === 0" class="py-8 text-center">
                                <p class="text-muted-foreground">暂无事件</p>
                            </div>
                            <div v-else class="space-y-3">
                                <div
                                    v-for="event in events"
                                    :key="`${event.reason}-${event.first_timestamp}`"
                                    class="rounded-lg border bg-background p-3"
                                >
                                    <div class="mb-2 flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <AlertCircle v-if="event.type === 'Warning'" class="h-4 w-4 text-destructive" />
                                            <span class="font-medium">{{ event.reason }}</span>
                                            <Badge :variant="getEventTypeBadgeVariant(event.type)">
                                                {{ event.type }}
                                            </Badge>
                                        </div>
                                        <span v-if="event.count > 1" class="text-sm text-muted-foreground"> {{ event.count }}x </span>
                                    </div>
                                    <p class="mb-2 text-sm">{{ event.message }}</p>
                                    <div class="text-xs text-muted-foreground">
                                        时间: {{ formatTimestamp(event.last_timestamp || event.first_timestamp || '') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>

                    <!-- 指标标签页 -->
                    <TabsContent value="metrics" v-if="metrics">
                        <div class="rounded-lg border bg-card p-4">
                            <h3 class="mb-4 flex items-center text-lg font-medium">
                                <Activity class="mr-2 h-5 w-5 text-muted-foreground" />
                                资源指标
                            </h3>
                            <div v-if="metrics.timestamp" class="mb-4 text-sm text-muted-foreground">
                                采集时间: {{ formatTimestamp(metrics.timestamp) }}
                            </div>
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div v-for="container in metrics.containers" :key="container.name" class="rounded-lg border bg-background p-3">
                                    <h4 class="mb-3 font-medium">{{ container.name }}</h4>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <p class="mb-1 text-sm font-medium text-muted-foreground">CPU 使用量</p>
                                            <div class="flex items-center">
                                                <div class="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
                                                <p class="font-mono text-sm">{{ container.usage.cpu }}</p>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="mb-1 text-sm font-medium text-muted-foreground">内存使用量</p>
                                            <div class="flex items-center">
                                                <div class="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                                                <p class="font-mono text-sm">{{ container.usage.memory }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>

            <!-- 重启确认对话框 -->
            <Dialog v-model:open="showRestartDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>重启 Pod</DialogTitle>
                        <DialogDescription> 确定要重启 Pod "{{ podName }}" 吗？ </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showRestartDialog = false">取消</Button>
                        <Button @click="restartPod">确认重启</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除确认对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>删除 Pod</DialogTitle>
                        <DialogDescription> 确定要删除 Pod "{{ podName }}" 吗？此操作不可撤销。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false">取消</Button>
                        <Button variant="destructive" @click="deletePod">删除</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>
