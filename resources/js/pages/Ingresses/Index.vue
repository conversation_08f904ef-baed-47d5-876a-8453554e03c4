<template>
    <AppLayout>
        <Head title="HTTP 入口管理" />

        <div class="p-4">
            <!-- Header Section -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">HTTP 入口管理</h1>
                    <p class="mt-1 text-base text-gray-600 dark:text-gray-400">管理工作空间中的 HTTP 入口路由规则，将外部流量路由到您的服务</p>
                </div>
                <Button @click="router.visit(route('ingresses.create'))" size="lg">
                    <Plus class="mr-2 h-5 w-5" />
                    创建入口
                </Button>
            </div>

            <!-- Content Section -->
            <div class="space-y-6">
                <!-- Loading State -->
                <div v-if="loading" class="flex items-center justify-center py-16">
                    <div class="flex items-center space-x-3">
                        <Loader2 class="h-6 w-6 animate-spin text-blue-600" />
                        <span class="text-lg text-gray-600 dark:text-gray-400">正在加载入口规则...</span>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else-if="!ingresses || ingresses.length === 0" class="py-16 text-center">
                    <div class="mx-auto max-w-md">
                        <Globe class="mx-auto mb-4 h-16 w-16 text-gray-400" />
                        <h3 class="mb-2 text-lg font-semibold text-gray-900 dark:text-white">暂无入口规则</h3>
                        <p class="mb-6 text-gray-600 dark:text-gray-400">创建您的第一个路由规则，开始将外部流量引导到您的服务</p>
                        <Button @click="router.visit(route('ingresses.create'))" size="lg">
                            <Plus class="mr-2 h-5 w-5" />
                            创建入口
                        </Button>
                    </div>
                </div>

                <!-- Ingress Table -->
                <div v-else class="space-y-4">
                    <!-- Table Header -->
                    <div class="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-800/50">
                            <div class="grid grid-cols-12 items-center gap-4 text-sm font-semibold text-gray-700 dark:text-gray-300">
                                <div class="col-span-4">入口名称</div>
                                <div class="col-span-3">域名配置</div>
                                <div class="col-span-2">状态</div>
                                <div class="col-span-2">创建时间</div>
                                <div class="col-span-1 text-right">操作</div>
                            </div>
                        </div>

                        <!-- Table Body -->
                        <div class="divide-y divide-gray-200 dark:divide-gray-700">
                            <div
                                v-for="ingress in ingresses"
                                :key="ingress.name"
                                class="px-6 py-5 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50"
                            >
                                <div class="grid grid-cols-12 items-start gap-4">
                                    <!-- Name and URLs -->
                                    <div class="col-span-4 space-y-2">
                                        <div class="flex items-center space-x-3">
                                            <h3
                                                class="cursor-pointer font-semibold text-gray-900 transition-colors hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
                                                @click="viewIngress(ingress)"
                                            >
                                                {{ ingress.name }}
                                            </h3>
                                            <Badge
                                                v-if="ingress.has_tls"
                                                variant="secondary"
                                                class="border-green-300 bg-green-100 text-green-800 dark:border-green-700 dark:bg-green-900/30 dark:text-green-300"
                                            >
                                                <Shield class="mr-1 h-3 w-3" />
                                                TLS
                                            </Badge>
                                        </div>
                                        <div v-if="ingress.access_urls && ingress.access_urls.length > 0" class="space-y-1">
                                            <a
                                                v-for="url in ingress.access_urls.slice(0, 2)"
                                                :key="url"
                                                :href="url"
                                                target="_blank"
                                                class="flex items-center text-sm text-blue-600 transition-colors hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                            >
                                                <ExternalLink class="mr-1.5 h-3.5 w-3.5" />
                                                {{ url }}
                                            </a>
                                            <p v-if="ingress.access_urls.length > 2" class="text-xs text-gray-500 dark:text-gray-400">
                                                还有 {{ ingress.access_urls.length - 2 }} 个地址...
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Domains -->
                                    <div class="col-span-3">
                                        <div v-if="ingress.domains && ingress.domains.length > 0" class="space-y-2">
                                            <div class="flex flex-wrap gap-1.5">
                                                <Badge
                                                    v-for="domain in ingress.domains.slice(0, 3)"
                                                    :key="domain"
                                                    :variant="ingress.tls_domains && ingress.tls_domains.includes(domain) ? 'default' : 'outline'"
                                                    class="text-xs"
                                                >
                                                    {{ domain }}
                                                </Badge>
                                            </div>
                                            <p v-if="ingress.domains.length > 3" class="text-xs text-gray-500 dark:text-gray-400">
                                                还有 {{ ingress.domains.length - 3 }} 个域名
                                            </p>
                                        </div>
                                        <p v-else class="text-sm text-gray-500 italic dark:text-gray-400">未配置域名</p>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-span-2">
                                        <Badge :variant="getStatusVariant(ingress.status)" class="font-medium">
                                            {{ formatIngressStatus(ingress.status) }}
                                        </Badge>
                                    </div>

                                    <!-- Created At -->
                                    <div class="col-span-2">
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ formatDate(ingress.createdAt) }}
                                        </p>
                                    </div>

                                    <!-- Actions -->
                                    <div class="col-span-1 flex justify-end">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                                                    <span class="sr-only">打开菜单</span>
                                                    <MoreHorizontal class="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" class="w-48">
                                                <DropdownMenuItem @click="viewIngress(ingress)" class="cursor-pointer">
                                                    <Eye class="mr-2 h-4 w-4" />
                                                    查看详情
                                                </DropdownMenuItem>
                                                <DropdownMenuItem @click="editIngress(ingress)" class="cursor-pointer">
                                                    <Edit class="mr-2 h-4 w-4" />
                                                    编辑配置
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    @click="confirmDeleteIngress(ingress)"
                                                    class="cursor-pointer text-red-600 focus:bg-red-50 focus:text-red-700 dark:text-red-400 dark:focus:bg-red-900/50 dark:focus:text-red-300"
                                                >
                                                    <Trash2 class="mr-2 h-4 w-4" />
                                                    删除
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Confirmation Dialog -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent class="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle class="flex items-center space-x-2">
                            <Trash2 class="h-5 w-5 text-red-500" />
                            <span>删除入口</span>
                        </DialogTitle>
                        <DialogDescription class="pt-2">
                            确定要删除入口 <span class="font-semibold text-gray-900 dark:text-gray-100">"{{ ingressToDelete?.name }}"</span> 吗？
                            <br />
                            <span class="text-red-600 dark:text-red-400">此操作不可撤销，请谨慎操作。</span>
                        </DialogDescription>
                    </DialogHeader>

                    <DialogFooter class="flex space-x-2 pt-4">
                        <Button variant="outline" @click="showDeleteDialog = false" :disabled="deleting"> 取消 </Button>
                        <Button variant="destructive" @click="deleteIngress" :disabled="deleting">
                            <Loader2 v-if="deleting" class="mr-2 h-4 w-4 animate-spin" />
                            <Trash2 v-else class="mr-2 h-4 w-4" />
                            删除
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatIngressStatus } from '@/lib/formatK8sStatus';
import type { Ingress } from '@/types';
import { Head, router, usePage } from '@inertiajs/vue3';
import { Edit, ExternalLink, Eye, Globe, Loader2, MoreHorizontal, Plus, Shield, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    workspace: any;
    cluster?: {
        id: number;
        name: string;
        ingress_external_ips: string[];
    };
}

const props = defineProps<Props>();
const page = usePage();
const loading = ref(true);
const ingresses = ref<Ingress[]>([]);
const showDeleteDialog = ref(false);
const ingressToDelete = ref<Ingress | null>(null);
const deleting = ref(false);

const loadIngresses = async () => {
    try {
        loading.value = true;
        const response = await axios.get('/api/ingresses');
        ingresses.value = response.data || [];
    } catch (error: any) {
        console.error('Failed to load ingresses:', error);
        toast.error(error.response?.data?.message || '加载 Ingress 列表失败');
        ingresses.value = [];
    } finally {
        loading.value = false;
    }
};

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'ready':
            return 'default';
        case 'pending':
            return 'secondary';
        default:
            return 'secondary';
    }
};

const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    } catch {
        return dateString;
    }
};

const viewIngress = (ingress: Ingress) => {
    router.visit(route('ingresses.show', { ingress: ingress.name }));
};

const editIngress = (ingress: Ingress) => {
    router.visit(route('ingresses.edit', { ingress: ingress.name }));
};

const confirmDeleteIngress = (ingress: Ingress) => {
    ingressToDelete.value = ingress;
    showDeleteDialog.value = true;
};

const deleteIngress = async () => {
    if (!ingressToDelete.value) return;

    try {
        deleting.value = true;
        await axios.delete(`/api/ingresses/${ingressToDelete.value.name}`);

        toast.success('入口删除成功');
        showDeleteDialog.value = false;
        ingressToDelete.value = null;

        // 重新加载列表
        await loadIngresses();
    } catch (error: any) {
        console.error('Failed to delete ingress:', error);
        toast.error(error.response?.data?.message || '删除入口失败');
    } finally {
        deleting.value = false;
    }
};

onMounted(() => {
    loadIngresses();
});
</script>
