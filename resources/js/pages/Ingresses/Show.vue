<template>
    <AppLayout>
        <Head :title="`入口详情 - ${ingressName}`" />

        <div class="p-4">
            <!-- Loading State -->
            <div v-if="loading" class="flex items-center justify-center py-16">
                <div class="flex items-center space-x-3">
                    <Loader2 class="h-6 w-6 animate-spin text-blue-600" />
                    <span class="text-lg text-gray-600 dark:text-gray-400">正在加载入口详情...</span>
                </div>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="rounded-lg border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-900/20">
                <div class="flex items-center">
                    <AlertCircle class="mr-3 h-6 w-6 text-red-500" />
                    <div>
                        <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">加载失败</h3>
                        <p class="mt-1 text-red-700 dark:text-red-300">{{ error }}</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div v-else-if="ingress" class="space-y-8">
                <!-- Header Section -->
                <div class="flex items-start justify-between">
                    <div class="space-y-3">
                        <div class="flex items-center space-x-4">
                            <h1 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">{{ ingress.name }}</h1>
                            <Badge :variant="getStatusVariant(ingress.status)" class="text-sm font-medium">
                                {{ formatK8sStatus(ingress.status) }}
                            </Badge>
                            <Badge
                                v-if="ingress.has_tls"
                                variant="secondary"
                                class="border-green-300 bg-green-100 text-green-800 dark:border-green-700 dark:bg-green-900/30 dark:text-green-300"
                            >
                                <Shield class="mr-1.5 h-4 w-4" />
                                TLS 已启用
                            </Badge>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400">查看入口的详细配置和状态信息</p>
                    </div>
                    <div class="flex space-x-3">
                        <Button variant="outline" @click="router.visit(route('ingresses.edit', { ingress: ingress.name }))">
                            <Edit class="mr-2 h-4 w-4" />
                            编辑配置
                        </Button>
                        <Button variant="outline" @click="router.visit(route('ingresses.index'))">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            返回列表
                        </Button>
                    </div>
                </div>

                <!-- Quick Info Section -->
                <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div class="space-y-2">
                        <Label class="text-sm font-semibold text-gray-700 dark:text-gray-300">状态</Label>
                        <div class="flex items-center space-x-2">
                            <Badge :variant="getStatusVariant(ingress.status)">
                                {{ formatK8sStatus(ingress.status) }}
                            </Badge>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <Label class="text-sm font-semibold text-gray-700 dark:text-gray-300">创建时间</Label>
                        <p class="text-gray-900 dark:text-gray-100">{{ formatDate(ingress.createdAt) }}</p>
                    </div>
                    <div class="space-y-2">
                        <Label class="text-sm font-semibold text-gray-700 dark:text-gray-300">解析地址</Label>
                        <p class="font-mono text-sm text-gray-900 dark:text-gray-100">
                            {{ cluster?.ingress_external_ips?.join(', ') || '未配置' }}
                        </p>
                    </div>
                </div>

                <Separator />

                <!-- Access URLs Section -->
                <div v-if="accessInfo.length > 0" class="space-y-4">
                    <div>
                        <h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">访问地址</h2>
                        <p class="text-gray-600 dark:text-gray-400">您可以通过以下地址访问您的服务</p>
                    </div>

                    <div class="space-y-4">
                        <div v-for="info in accessInfo" :key="info.url" class="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                            <div class="mb-3 flex items-center justify-between">
                                <a
                                    :href="info.url"
                                    target="_blank"
                                    class="flex items-center font-mono text-lg text-blue-600 transition-colors hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                >
                                    {{ info.url }}
                                    <ExternalLink class="ml-2 h-5 w-5" />
                                </a>
                                <Button size="sm" variant="outline" @click="copyToClipboard(info.url)">
                                    <Copy class="mr-2 h-4 w-4" />
                                    复制
                                </Button>
                            </div>

                            <div v-if="info.externalAccess.length > 0" class="space-y-2">
                                <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">访问配置</Label>
                                <p class="mb-2 text-xs text-gray-500 dark:text-gray-400">如需从外部访问，请解析以下地址到您的域名</p>
                                <div class="space-y-1">
                                    <div
                                        v-for="external in info.externalAccess"
                                        :key="external.ip"
                                        class="flex items-center justify-between rounded bg-gray-100 p-3 dark:bg-gray-800"
                                    >
                                        <code class="font-mono text-sm text-gray-900 dark:text-gray-100">
                                            {{ external.ip }} {{ external.host }}
                                        </code>
                                        <Button size="sm" variant="ghost" @click="copyToClipboard(`${external.ip} ${external.host}`)">
                                            <Copy class="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <Separator />

                <!-- Rules Section -->
                <div class="space-y-6">
                    <div>
                        <h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">路由规则</h2>
                        <p class="text-gray-600 dark:text-gray-400">详细的域名和路径路由配置</p>
                    </div>

                    <div class="space-y-6">
                        <div
                            v-for="(rule, index) in ingress.rules"
                            :key="index"
                            class="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700"
                        >
                            <!-- Rule Header -->
                            <div class="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-800/50">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">规则 {{ index + 1 }}</h3>
                                    <div class="flex items-center space-x-3">
                                        <Badge variant="outline" class="font-mono">{{ rule.host }}</Badge>
                                        <Badge
                                            v-if="ingress.tls_domains?.includes(rule.host)"
                                            variant="secondary"
                                            class="border-green-300 bg-green-100 text-green-800 dark:border-green-700 dark:bg-green-900/30 dark:text-green-300"
                                        >
                                            <Shield class="mr-1 h-3 w-3" />
                                            TLS
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <!-- Paths Table -->
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 dark:bg-gray-800/30">
                                        <tr>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-semibold tracking-wider text-gray-700 uppercase dark:text-gray-300"
                                            >
                                                路径
                                            </th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-semibold tracking-wider text-gray-700 uppercase dark:text-gray-300"
                                            >
                                                类型
                                            </th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-semibold tracking-wider text-gray-700 uppercase dark:text-gray-300"
                                            >
                                                后端服务
                                            </th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-semibold tracking-wider text-gray-700 uppercase dark:text-gray-300"
                                            >
                                                端口
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr
                                            v-for="(path, pathIndex) in rule.http.paths"
                                            :key="pathIndex"
                                            class="transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/30"
                                        >
                                            <td class="px-6 py-4">
                                                <code
                                                    class="rounded bg-gray-100 px-2 py-1 font-mono text-sm text-gray-900 dark:bg-gray-800 dark:text-gray-100"
                                                >
                                                    {{ path.path }}
                                                </code>
                                            </td>
                                            <td class="px-6 py-4">
                                                <Badge variant="outline" class="text-xs">{{ path.pathType }}</Badge>
                                            </td>
                                            <td class="px-6 py-4">
                                                <code class="font-mono text-sm text-gray-900 dark:text-gray-100">{{
                                                    path.backend.service.name
                                                }}</code>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="text-sm text-gray-900 dark:text-gray-100">{{ path.backend.service.port.number }}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- TLS Configuration Section -->
                <div v-if="ingress.has_tls && ingress.tls" class="space-y-6">
                    <Separator />

                    <div>
                        <h2 class="mb-2 flex items-center text-xl font-semibold text-gray-900 dark:text-white">
                            <Shield class="mr-2 h-5 w-5" />
                            TLS 配置
                        </h2>
                        <p class="text-gray-600 dark:text-gray-400">用于加密通信的证书配置</p>
                    </div>

                    <div class="space-y-4">
                        <div v-for="(tls, index) in ingress.tls" :key="index" class="rounded-lg border border-gray-200 p-6 dark:border-gray-700">
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div class="space-y-2">
                                    <Label class="text-sm font-semibold text-gray-700 dark:text-gray-300">关联的域名</Label>
                                    <div class="flex flex-wrap gap-2">
                                        <Badge v-for="host in tls.hosts" :key="host" variant="secondary" class="font-mono">
                                            {{ host }}
                                        </Badge>
                                    </div>
                                </div>
                                <div v-if="tls.secretName" class="space-y-2">
                                    <Label class="text-sm font-semibold text-gray-700 dark:text-gray-300">证书存储名称</Label>
                                    <code
                                        class="block rounded bg-gray-100 px-3 py-2 font-mono text-sm text-gray-900 dark:bg-gray-800 dark:text-gray-100"
                                    >
                                        {{ tls.secretName }}
                                    </code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { formatK8sStatus } from '@/lib/formatK8sStatus';
import type { Ingress, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, Copy, Edit, ExternalLink, Loader2, Shield } from 'lucide-vue-next';
import { toast } from 'sonner';
import { computed, onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    ingressName: string;
    workspace: Workspace;
    cluster?: {
        id: number;
        name: string;
        ingress_external_ips: string[];
    };
}

const props = defineProps<Props>();
const workspace = computed(() => props.workspace);

// 响应式数据
const ingress = ref<Ingress | null>(null);
const loading = ref(true);
const error = ref<string | undefined>();

const accessInfo = computed(() => {
    if (!ingress.value) return [];

    const urlInfos: Array<{ url: string; externalAccess: { ip: string; host: string }[] }> = [];

    if (ingress.value.rules && Array.isArray(ingress.value.rules)) {
        ingress.value.rules.forEach((rule: any) => {
            if (!rule.host) return;

            const useTls = ingress.value!.tls_domains?.includes(rule.host);
            const protocol = useTls ? 'https' : 'http';
            const mainUrl = `${protocol}://${rule.host}`;

            const externalAccess: { ip: string; host: string }[] = [];

            if (props.cluster?.ingress_external_ips && props.cluster.ingress_external_ips.length > 0) {
                props.cluster.ingress_external_ips.forEach((ip) => {
                    externalAccess.push({ ip, host: rule.host });
                });
            } else if (ingress.value?.loadBalancerIngress) {
                externalAccess.push({ ip: ingress.value.loadBalancerIngress, host: rule.host });
            }

            urlInfos.push({
                url: mainUrl,
                externalAccess,
            });
        });
    }

    return urlInfos;
});

const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'ready':
            return 'default';
        case 'pending':
            return 'secondary';
        default:
            return 'secondary';
    }
};

const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        });
    } catch {
        return dateString;
    }
};

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        toast.success('已复制到剪贴板');
    } catch (error) {
        toast.error('复制失败');
    }
};

const openUrl = (url: string) => {
    window.open(url, '_blank');
};

// 加载 Ingress 数据
const loadIngress = async () => {
    loading.value = true;
    try {
        const response = await axios.get(`/api/ingresses/${props.ingressName}`);
        ingress.value = response.data;
        error.value = undefined;
    } catch (err: any) {
        error.value = err.response?.data?.message || '加载 Ingress 详情失败';
        console.error('加载 Ingress 失败:', err);
    } finally {
        loading.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    loadIngress();
});
</script>
