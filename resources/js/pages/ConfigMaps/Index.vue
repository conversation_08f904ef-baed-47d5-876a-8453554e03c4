<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="配置管理" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">配置管理</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">管理工作空间中的配置</p>
                </div>
                <Button @click="router.visit(route('configmaps.create'))">
                    <Plus class="mr-2 h-4 w-4" />
                    创建配置
                </Button>
            </div>

            <!-- ConfigMap 列表 -->
            <Card>
                <CardContent class="p-0">
                    <div v-if="loading" class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-full" />
                            <Skeleton class="h-4 w-3/4" />
                            <Skeleton class="h-4 w-1/2" />
                        </div>
                    </div>

                    <div v-else-if="configMaps.length === 0" class="p-6 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <FileText class="mx-auto mb-4 h-12 w-12 opacity-50" />
                            <p class="text-lg font-medium">暂无配置</p>
                            <p class="text-sm">创建您的第一个配置来存储配置数据</p>
                        </div>
                    </div>

                    <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div
                            v-for="configMap in configMaps"
                            :key="configMap.name"
                            class="p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50"
                        >
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <FileText class="h-5 w-5 text-gray-400" />
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ configMap.name }}
                                            </h3>
                                            <div class="mt-1 flex items-center space-x-4">
                                                <span class="text-xs text-gray-500 dark:text-gray-400"> {{ configMap.data_count }} 个配置项 </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ formatDate(configMap.created_at) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="viewConfigMap(configMap)">
                                            <Eye class="mr-2 h-4 w-4" />
                                            查看详情
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="router.visit(route('configmaps.edit', configMap.name))">
                                            <Edit class="mr-2 h-4 w-4" />
                                            编辑
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem @click="confirmDeleteConfigMap(configMap)" class="text-red-600 dark:text-red-400">
                                            <Trash2 class="mr-2 h-4 w-4" />
                                            删除
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- 查看 ConfigMap 对话框 -->
            <Dialog v-model:open="showViewDialog">
                <DialogContent class="max-w-4xl">
                    <DialogHeader>
                        <DialogTitle>配置详情</DialogTitle>
                    </DialogHeader>

                    <div v-if="selectedConfigMap" class="space-y-4">
                        <div>
                            <Label>名称</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ selectedConfigMap.name }}</p>
                        </div>
                        <div>
                            <Label>创建时间</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedConfigMap.created_at) }}</p>
                        </div>
                        <div>
                            <Label>配置数据</Label>
                            <div class="mt-2 space-y-4">
                                <div
                                    v-for="(value, key) in selectedConfigMap.data"
                                    :key="key"
                                    class="rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                                >
                                    <div class="mb-2 flex items-center justify-between">
                                        <Label class="text-sm font-medium">{{ key }}</Label>
                                        <Button variant="outline" size="sm" @click="copyToClipboard(value)">
                                            <Copy class="mr-2 h-4 w-4" />
                                            复制
                                        </Button>
                                    </div>
                                    <pre class="overflow-x-auto rounded bg-gray-50 p-2 text-xs text-gray-700 dark:bg-gray-800 dark:text-gray-300">{{
                                        value
                                    }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button variant="outline" @click="showViewDialog = false"> 关闭 </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <!-- 删除 ConfigMap 对话框 -->
            <Dialog v-model:open="showDeleteDialog">
                <DialogContent class="max-w-4xl">
                    <DialogHeader>
                        <DialogTitle>确认删除配置</DialogTitle>
                        <DialogDescription> 确定要删除配置 "{{ configMapToDelete?.name }}" 吗？此操作不可恢复。 </DialogDescription>
                    </DialogHeader>

                    <DialogFooter>
                        <Button variant="outline" @click="showDeleteDialog = false"> 取消 </Button>
                        <Button @click="deleteConfigMap" :disabled="deleting">
                            {{ deleting ? '删除中...' : '删除' }}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, router, usePage } from '@inertiajs/vue3';
import { Copy, Edit, Eye, FileText, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface ConfigMap {
    name: string;
    data: Record<string, string>;
    binary_data: Record<string, string>;
    data_count: number;
    created_at: string;
}

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
    // 可以重定向到工作空间选择页面或显示错误信息
}

const configMaps = ref<ConfigMap[]>([]);
const loading = ref(true);
const showViewDialog = ref(false);
const selectedConfigMap = ref<ConfigMap | null>(null);
const showDeleteDialog = ref(false);
const configMapToDelete = ref<ConfigMap | null>(null);
const deleting = ref(false);

const loadConfigMaps = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        loading.value = true;
        const response = await axios.get('/api/configmaps');
        configMaps.value = response.data;
    } catch (error) {
        console.error('加载配置表 列表失败:', error);
        toast.error('加载配置表 列表失败');
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const viewConfigMap = (configMap: ConfigMap) => {
    selectedConfigMap.value = configMap;
    showViewDialog.value = true;
};

const confirmDeleteConfigMap = (configMap: ConfigMap) => {
    configMapToDelete.value = configMap;
    showDeleteDialog.value = true;
};

const deleteConfigMap = async () => {
    if (!workspace || !configMapToDelete.value) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        deleting.value = true;
        await axios.delete(`/api/configmaps/${configMapToDelete.value.name}`);
        toast.success('配置表 删除成功');
        showDeleteDialog.value = false;
        configMapToDelete.value = null;
        loadConfigMaps();
    } catch (error) {
        console.error('删除配置表 失败:', error);
        toast.error('删除配置表 失败');
    } finally {
        deleting.value = false;
    }
};

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        toast.success('已复制到剪贴板');
    } catch (error) {
        console.error('复制失败:', error);
        toast.error('复制失败');
    }
};

onMounted(() => {
    loadConfigMaps();
});
</script>
