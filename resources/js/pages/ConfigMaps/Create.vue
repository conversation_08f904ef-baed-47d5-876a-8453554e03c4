<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="创建配置表" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">创建配置表</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">创建新的配置表来存储配置数据</p>
                </div>
                <Button variant="outline" @click="router.visit(route('configmaps.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <Card>
                <CardContent class="p-6">
                    <div class="space-y-6">
                        <div>
                            <Label for="configMapName">配置名称</Label>
                            <Input id="configMapName" v-model="form.name" placeholder="输入配置名称" />
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <Label>配置数据</Label>
                                <Button type="button" variant="outline" size="sm" @click="addConfigItem">
                                    <Plus class="mr-2 h-4 w-4" />
                                    添加配置项
                                </Button>
                            </div>

                            <div
                                v-for="(item, index) in form.data"
                                :key="index"
                                class="space-y-2 rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                            >
                                <div class="flex items-center justify-between">
                                    <Input v-model="item.key" placeholder="配置键名" class="mr-2 flex-1" />
                                    <Button type="button" variant="outline" size="sm" @click="removeConfigItem(index)">
                                        <X class="h-4 w-4" />
                                    </Button>
                                </div>
                                <textarea
                                    v-model="item.value"
                                    placeholder="配置值"
                                    class="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                        </div>

                        <div class="flex space-x-4">
                            <Button @click="createConfigMap" :disabled="creating">
                                {{ creating ? '创建中...' : '创建配置表' }}
                            </Button>
                            <Button variant="outline" @click="router.visit(route('configmaps.index'))"> 取消 </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <div class="p-4">
            <DataPreview api="/api/configmaps" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ArrowLeft, Plus, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
    // 可以重定向到工作空间选择页面或显示错误信息
}

const creating = ref(false);

const form = ref({
    name: '',
    data: [{ key: '', value: '' }],
});

const addConfigItem = () => {
    form.value.data.push({ key: '', value: '' });
};

const removeConfigItem = (index: number) => {
    form.value.data.splice(index, 1);
};

const createConfigMap = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        creating.value = true;

        const data: Record<string, string> = {};
        form.value.data.forEach((item) => {
            if (item.key && item.value) {
                data[item.key] = item.value;
            }
        });

        if (Object.keys(data).length === 0) {
            toast.error('请至少添加一个配置项');
            return;
        }

        await axios.post('/api/configmaps', {
            name: form.value.name,
            data: data,
        });

        toast.success('配置表创建成功');
        router.visit(route('configmaps.index'));
    } catch (error) {
        console.error('创建配置表 失败:', error);
        toast.error('创建配置表 失败');
    } finally {
        creating.value = false;
    }
};
</script>
