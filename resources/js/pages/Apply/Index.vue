<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useYamlApply } from '@/composables/useYamlApply';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { FileText, Loader2, Send } from 'lucide-vue-next';
import { onMounted } from 'vue';

const {
    yamlContent,
    isSubmitting,
    hasWorkspace,
    currentProgress,
    totalProgress,
    progressPercentage,
    currentApplyingItem,
    initializeDefaultYaml,
    submitYaml,
} = useYamlApply();

// 初始化默认 YAML
onMounted(() => {
    initializeDefaultYaml();
});

// 提交 YAML
const handleSubmit = async () => {
    await submitYaml();
};
</script>

<template>
    <Head title="YAML 资源应用" />

    <AppLayout>
        <div class="container mx-auto p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">应用更改</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {{ hasWorkspace ? '输入 YAML 配置来创建或更新资源' : '请先选择工作空间' }}， 使用
                        <code>---</code> 分隔多个资源配置。每个配置必须包含 <code>api</code> 和 <code>method</code> 字段。
                    </p>
                </div>
                <Button @click="handleSubmit" :disabled="isSubmitting || !hasWorkspace">
                    <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
                    <Send v-else class="mr-2 h-4 w-4" />
                    {{ isSubmitting ? '应用中...' : '应用资源' }}
                </Button>
            </div>

            <!-- 进度显示 -->
            <div v-if="isSubmitting" class="my-6 space-y-3 rounded-lg border bg-card p-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="font-medium">应用进度</span>
                    <span class="text-muted-foreground">{{ currentProgress }} / {{ totalProgress }}</span>
                </div>
                <Progress :value="progressPercentage" class="h-2" />
                <div v-if="currentApplyingItem" class="text-sm text-muted-foreground">
                    正在处理: <code class="rounded bg-muted px-1.5 py-0.5">{{ currentApplyingItem.method }} {{ currentApplyingItem.api }}</code>
                </div>
            </div>

            <!-- 无工作空间提示 -->
            <div v-if="!hasWorkspace" class="flex items-center justify-center py-12">
                <div class="text-center">
                    <FileText class="mx-auto h-12 w-12 text-muted-foreground" />
                    <p class="mt-4 text-sm text-muted-foreground">请先选择一个工作空间来应用资源</p>
                </div>
            </div>

            <!-- YAML 编辑器 -->
            <div v-else class="space-y-4">
                <ScrollArea class="rounded-md border">
                    <textarea
                        id="yaml-content"
                        v-model="yamlContent"
                        class="h-[600px] w-full resize-none border-0 bg-transparent p-4 font-mono text-sm focus:ring-0 focus:outline-none"
                        placeholder="输入 YAML 配置..."
                        spellcheck="false"
                        :disabled="isSubmitting"
                    />
                </ScrollArea>

                <!-- 操作按钮 -->
                <div class="flex justify-end">
                    <Button @click="handleSubmit" :disabled="isSubmitting || !hasWorkspace">
                        <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
                        <Send v-else class="mr-2 h-4 w-4" />
                        {{ isSubmitting ? '应用中...' : '应用资源' }}
                    </Button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
