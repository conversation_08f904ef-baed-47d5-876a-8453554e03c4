<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="编辑密钥" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">编辑密钥</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">修改密钥的配置数据</p>
                </div>
                <Button variant="outline" @click="router.visit(route('secrets.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <Card>
                <CardContent class="p-6">
                    <div class="space-y-6">
                        <div>
                            <Label>密钥名称</Label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ secret?.name }}</p>
                        </div>

                        <div>
                            <Label>密钥类型</Label>
                            <Badge :variant="getSecretTypeVariant(secret?.type || '')">
                                {{ getSecretTypeLabel(secret?.type || '') }}
                            </Badge>
                        </div>

                        <!-- 简单的键值对编辑表单，适用于所有类型的 Secret -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <Label>键值对</Label>
                                <Button type="button" variant="outline" size="sm" @click="addKeyValue">
                                    <Plus class="mr-2 h-4 w-4" />
                                    添加
                                </Button>
                            </div>
                            <div
                                v-for="(item, index) in form.data"
                                :key="index"
                                class="space-y-2 rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                            >
                                <div class="flex items-center justify-between">
                                    <Input v-model="item.key" placeholder="键" class="mr-2 flex-1" />
                                    <Button type="button" variant="outline" size="sm" @click="removeKeyValue(index)">
                                        <X class="h-4 w-4" />
                                    </Button>
                                </div>
                                <textarea
                                    v-model="item.value"
                                    placeholder="值"
                                    class="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                        </div>

                        <div class="flex space-x-4">
                            <Button @click="updateSecret" :disabled="updating">
                                {{ updating ? '更新中...' : '更新密钥' }}
                            </Button>
                            <Button variant="outline" @click="router.visit(route('secrets.index'))"> 取消 </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <div class="p-4">
            <DataPreview api="/api/secrets" method="PUT" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ArrowLeft, Plus, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Secret {
    name: string;
    type: string;
    data_keys: string[];
    data_count: number;
    created_at: string;
}

interface SecretData {
    [key: string]: string;
}

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
}

const secret = ref<Secret | null>(page.props.secret as Secret);
const updating = ref(false);

const form = ref({
    name: secret.value?.name || '',
    data: [] as { key: string; value: string }[],
});

const getSecretTypeLabel = (type: string): string => {
    const types: Record<string, string> = {
        Opaque: '通用',
        'kubernetes.io/dockerconfigjson': 'Docker Registry',
        'kubernetes.io/tls': 'TLS 证书',
        'kubernetes.io/basic-auth': '基础认证',
        'kubernetes.io/ssh-auth': 'SSH 认证',
    };
    return types[type] || type;
};

const getSecretTypeVariant = (type: string): 'default' | 'destructive' | 'outline' | 'secondary' => {
    const variants: Record<string, 'default' | 'destructive' | 'outline' | 'secondary'> = {
        Opaque: 'secondary',
        'kubernetes.io/dockerconfigjson': 'default',
        'kubernetes.io/tls': 'secondary',
        'kubernetes.io/basic-auth': 'secondary',
        'kubernetes.io/ssh-auth': 'secondary',
    };
    return variants[type] || 'secondary';
};

const addKeyValue = () => {
    form.value.data.push({ key: '', value: '' });
};

const removeKeyValue = (index: number) => {
    form.value.data.splice(index, 1);
};

const updateSecret = async () => {
    if (!workspace || !secret.value) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        updating.value = true;

        // 构建键值对数据
        const data: Record<string, string> = {};
        form.value.data.forEach((item) => {
            if (item.key && item.value) {
                data[item.key] = item.value;
            }
        });

        if (Object.keys(data).length === 0) {
            toast.error('请至少添加一个键值对');
            return;
        }

        // 使用统一的更新接口
        await axios.put(`/api/secrets/${secret.value.name}`, { data });

        toast.success('密钥更新成功');
        router.visit(route('secrets.index'));
    } catch (error) {
        console.error('更新密钥 失败:', error);
        toast.error('更新密钥 失败');
    } finally {
        updating.value = false;
    }
};

// 初始化表单数据
onMounted(async () => {
    if (!workspace || !secret.value) {
        return;
    }

    try {
        // 获取 Secret 的详细数据
        const response = await axios.get(`/api/secrets/${secret.value.name}/data?edit=true`);
        const secretData: SecretData = response.data;

        // 转换为键值对格式
        form.value.data = Object.entries(secretData).map(([key, value]) => ({
            key,
            value,
        }));
    } catch (error) {
        console.error('获取密钥数据失败:', error);
        toast.error('获取密钥数据失败');
    }
});
</script>
