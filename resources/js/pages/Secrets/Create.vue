<template>
    <AppLayout>
        <div class="space-y-6 p-4">
            <Head title="创建密钥" />

            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">创建密钥</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">创建新的密钥来存储敏感数据</p>
                </div>
                <Button variant="outline" @click="router.visit(route('secrets.index'))">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回列表
                </Button>
            </div>

            <Card>
                <CardContent class="p-6">
                    <div class="space-y-6">
                        <div>
                            <Label for="secretType">密钥类型</Label>
                            <Select v-model="form.type">
                                <SelectTrigger>
                                    <SelectValue placeholder="选择密钥类型" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="generic">通用密钥 (Opaque)</SelectItem>
                                    <SelectItem value="docker-registry">Docker Registry</SelectItem>
                                    <SelectItem value="tls">TLS 证书</SelectItem>
                                    <SelectItem value="basic-auth">基础认证</SelectItem>
                                    <SelectItem value="ssh-auth">SSH 认证</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label for="secretName">密钥名称</Label>
                            <Input id="secretName" v-model="form.name" placeholder="输入密钥名称" />
                        </div>

                        <!-- 通用 Secret 表单 -->
                        <div v-if="form.type === 'generic'" class="space-y-4">
                            <div class="flex items-center justify-between">
                                <Label>键值对</Label>
                                <Button type="button" variant="outline" size="sm" @click="addKeyValue">
                                    <Plus class="mr-2 h-4 w-4" />
                                    添加
                                </Button>
                            </div>
                            <div v-for="(item, index) in form.data" :key="index" class="flex space-x-2">
                                <Input v-model="item.key" placeholder="键" class="flex-1" />
                                <Input v-model="item.value" placeholder="值" type="password" class="flex-1" />
                                <Button type="button" variant="outline" size="sm" @click="removeKeyValue(index)">
                                    <X class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        <!-- Docker Registry 表单 -->
                        <div v-if="form.type === 'docker-registry'" class="space-y-4">
                            <div>
                                <Label for="server">服务器地址</Label>
                                <Input id="server" v-model="form.server" placeholder="例如: docker.io" />
                            </div>
                            <div>
                                <Label for="username">用户名</Label>
                                <Input id="username" v-model="form.username" />
                            </div>
                            <div>
                                <Label for="password">密码</Label>
                                <Input id="password" v-model="form.password" type="password" />
                            </div>
                            <div>
                                <Label for="email">邮箱 (可选)</Label>
                                <Input id="email" v-model="form.email" type="email" />
                            </div>
                        </div>

                        <!-- TLS 表单 -->
                        <div v-if="form.type === 'tls'" class="space-y-4">
                            <div>
                                <Label for="cert">证书 (tls.crt)</Label>
                                <textarea
                                    id="cert"
                                    v-model="form.cert"
                                    class="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700"
                                    placeholder="-----BEGIN CERTIFICATE-----"
                                />
                            </div>
                            <div>
                                <Label for="key">私钥 (tls.key)</Label>
                                <textarea
                                    id="key"
                                    v-model="form.key"
                                    class="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700"
                                    placeholder="-----BEGIN PRIVATE KEY-----"
                                />
                            </div>
                        </div>

                        <!-- Basic Auth 表单 -->
                        <div v-if="form.type === 'basic-auth'" class="space-y-4">
                            <div>
                                <Label for="basicUsername">用户名</Label>
                                <Input id="basicUsername" v-model="form.username" />
                            </div>
                            <div>
                                <Label for="basicPassword">密码</Label>
                                <Input id="basicPassword" v-model="form.password" type="password" />
                            </div>
                        </div>

                        <!-- SSH Auth 表单 -->
                        <div v-if="form.type === 'ssh-auth'" class="space-y-4">
                            <div>
                                <Label for="sshKey">SSH 私钥</Label>
                                <textarea
                                    id="sshKey"
                                    v-model="form.ssh_private_key"
                                    class="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-700"
                                    placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
                                />
                            </div>
                        </div>

                        <div class="flex space-x-4">
                            <Button @click="createSecret" :disabled="creating">
                                {{ creating ? '创建中...' : '创建密钥' }}
                            </Button>
                            <Button variant="outline" @click="router.visit(route('secrets.index'))"> 取消 </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <div class="p-4">
            <DataPreview api="/api/secrets" method="POST" :data-ref="form" />
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { Head, router, usePage } from '@inertiajs/vue3';
import { ArrowLeft, Plus, X } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';
import { route } from 'ziggy-js';

const page = usePage();
const user = (page.props.auth as any).user;
const workspace = user?.current_workspace;

if (!workspace) {
    console.error('No current workspace found');
    // 可以重定向到工作空间选择页面或显示错误信息
}

const creating = ref(false);

const form = ref({
    type: 'generic',
    name: '',
    data: [{ key: '', value: '' }],
    server: '',
    username: '',
    password: '',
    email: '',
    cert: '',
    key: '',
    ssh_private_key: '',
});

const addKeyValue = () => {
    form.value.data.push({ key: '', value: '' });
};

const removeKeyValue = (index: number) => {
    form.value.data.splice(index, 1);
};

const createSecret = async () => {
    if (!workspace) {
        toast.error('请先选择一个工作空间');
        return;
    }

    try {
        creating.value = true;

        const endpoints: Record<string, string> = {
            generic: '/secrets/generic',
            'docker-registry': '/secrets/docker-registry',
            tls: '/secrets/tls',
            'basic-auth': '/secrets/basic-auth',
            'ssh-auth': '/secrets/ssh-auth',
        };

        let payload: any = { name: form.value.name };

        switch (form.value.type) {
            case 'generic':
                const data: Record<string, string> = {};
                form.value.data.forEach((item) => {
                    if (item.key && item.value) {
                        data[item.key] = item.value;
                    }
                });
                payload.data = data;
                break;
            case 'docker-registry':
                payload = {
                    ...payload,
                    server: form.value.server,
                    username: form.value.username,
                    password: form.value.password,
                    email: form.value.email || undefined,
                };
                break;
            case 'tls':
                payload = {
                    ...payload,
                    cert: form.value.cert,
                    key: form.value.key,
                };
                break;
            case 'basic-auth':
                payload = {
                    ...payload,
                    username: form.value.username,
                    password: form.value.password,
                };
                break;
            case 'ssh-auth':
                payload = {
                    ...payload,
                    ssh_private_key: form.value.ssh_private_key,
                };
                break;
        }

        const endpoint = endpoints[form.value.type];
        if (!endpoint) {
            throw new Error('未知的密钥类型');
        }

        await axios.post(`/api${endpoint}`, payload);

        toast.success('密钥创建成功');
        router.visit(route('secrets.index'));
    } catch (error) {
        console.error('创建密钥 失败:', error);
        toast.error('创建密钥 失败');
    } finally {
        creating.value = false;
    }
};
</script>
