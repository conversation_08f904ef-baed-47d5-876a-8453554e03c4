<template>
    <div class="space-y-6 p-4">
        <Head title="欢迎" />

        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">欢迎</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">嗯，这就是一个简单首页，因为只有内测用户知道是什么。</p>
                <div class="mt-4 flex items-center gap-2">
                    <Button @click="router.visit(route('login'))">
                        <LogIn class="mr-2 h-4 w-4" />
                        登录
                    </Button>
                    <Button @click="router.visit(route('register'))">
                        <UserPlus class="mr-2 h-4 w-4" />
                        注册
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { router } from '@inertiajs/vue3';
import { LogIn, UserPlus } from 'lucide-vue-next';
import { route } from 'ziggy-js';
</script>
