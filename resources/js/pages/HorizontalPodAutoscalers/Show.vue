<template>
    <Head :title="`弹性伸缩: ${hpaData?.name || 'Loading...'}`" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="ghost" @click="router.visit(route('hpas.index'))">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        返回列表
                    </Button>
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ hpaData?.name || 'Loading...' }}
                        </h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">弹性伸缩详情</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2" v-if="hpaData">
                    <Button variant="outline" @click="editHPA">
                        <Edit class="mr-2 h-4 w-4" />
                        编辑
                    </Button>
                    <Button variant="destructive" @click="showDeleteDialog = true">
                        <Trash2 class="mr-2 h-4 w-4" />
                        删除
                    </Button>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="space-y-6">
                <Card>
                    <CardContent class="p-6">
                        <div class="space-y-4">
                            <Skeleton class="h-4 w-1/4" />
                            <Skeleton class="h-4 w-1/2" />
                            <Skeleton class="h-4 w-3/4" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Error State -->
            <Card v-else-if="error" class="border-red-200 dark:border-red-800">
                <CardContent class="p-6 text-center">
                    <div class="text-red-600 dark:text-red-400">
                        <AlertCircle class="mx-auto mb-4 h-12 w-12" />
                        <p class="text-lg font-medium">加载失败</p>
                        <p class="text-sm">{{ error }}</p>
                        <Button class="mt-4" @click="loadHPA">重试</Button>
                    </div>
                </CardContent>
            </Card>

            <!-- HPA Details -->
            <div v-else-if="hpaData" class="space-y-6">
                <!-- 基本信息和状态 -->
                <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <!-- 基本信息 -->
                    <Card class="lg:col-span-2">
                        <CardHeader>
                            <CardTitle>基本信息</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">弹性伸缩名称</Label>
                                    <p class="font-mono text-sm">{{ hpaData.name }}</p>
                                </div>
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">工作空间</Label>
                                    <p class="font-mono text-sm">{{ hpaData.namespace }}</p>
                                </div>
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">目标工作负载</Label>
                                    <p class="text-sm">{{ hpaData.scale_target_ref.kind }}/{{ hpaData.scale_target_ref.name }}</p>
                                </div>
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">状态</Label>
                                    <Badge :variant="getStatusBadgeVariant(hpaData.status)">
                                        <component :is="getStatusIcon(hpaData.status)" class="mr-1 h-3 w-3" />
                                        {{ hpaData.status }}
                                    </Badge>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">副本数范围</Label>
                                    <p class="text-sm">{{ hpaData.min_replicas }} - {{ hpaData.max_replicas }}</p>
                                </div>
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">当前副本数</Label>
                                    <p class="text-sm">{{ hpaData.current_replicas || '-' }}</p>
                                </div>
                                <div>
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">期望副本数</Label>
                                    <p class="text-sm">{{ hpaData.desired_replicas || '-' }}</p>
                                </div>
                            </div>
                            <div>
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间</Label>
                                <p class="text-sm">{{ formatDate(hpaData.created_at) }}</p>
                            </div>
                            <div v-if="hpaData.last_scale_time">
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">最后扩缩容时间</Label>
                                <p class="text-sm">{{ formatDate(hpaData.last_scale_time) }}</p>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- 当前指标状态 -->
                    <Card>
                        <CardHeader>
                            <CardTitle>当前指标</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div v-if="hpaData.current_cpu_utilization !== null && hpaData.current_cpu_utilization !== undefined">
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">CPU 使用率</Label>
                                <div class="space-y-1">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm">当前: {{ hpaData.current_cpu_utilization }}%</span>
                                        <span class="text-sm text-gray-500">目标: {{ hpaData.target_cpu_utilization }}%</span>
                                    </div>
                                    <div class="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                                        <div
                                            class="h-2 rounded-full transition-all duration-300"
                                            :class="getCpuUtilizationColor(hpaData.current_cpu_utilization!, hpaData.target_cpu_utilization)"
                                            :style="{
                                                width: `${Math.min(100, (hpaData.current_cpu_utilization! / (hpaData.target_cpu_utilization || 100)) * 100)}%`,
                                            }"
                                        ></div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="hpaData.current_memory_utilization !== null && hpaData.current_memory_utilization !== undefined">
                                <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">内存使用率</Label>
                                <div class="space-y-1">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm">当前: {{ hpaData.current_memory_utilization }}%</span>
                                        <span class="text-sm text-gray-500">目标: {{ hpaData.target_memory_utilization }}%</span>
                                    </div>
                                    <div class="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                                        <div
                                            class="h-2 rounded-full transition-all duration-300"
                                            :class="getMemoryUtilizationColor(hpaData.current_memory_utilization!, hpaData.target_memory_utilization)"
                                            :style="{
                                                width: `${Math.min(100, (hpaData.current_memory_utilization! / (hpaData.target_memory_utilization || 100)) * 100)}%`,
                                            }"
                                        ></div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="!hpaData.current_cpu_utilization && !hpaData.current_memory_utilization" class="py-4 text-center">
                                <div class="text-gray-500">
                                    <TrendingDown class="mx-auto mb-2 h-8 w-8 opacity-50" />
                                    <p class="text-sm">暂无指标数据</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <!-- 度量指标配置 -->
                <Card>
                    <CardHeader>
                        <CardTitle>度量指标配置</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-4">
                            <div v-for="(metric, index) in hpaData.metrics" :key="index" class="rounded-lg border p-4">
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">类型</Label>
                                        <Badge variant="outline">{{ metric.type }}</Badge>
                                    </div>
                                    <div v-if="metric.type === 'Resource'">
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">资源</Label>
                                        <p class="text-sm">{{ (metric as any).resource?.name || metric.resource_name }}</p>
                                    </div>
                                    <div v-else>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">度量名称</Label>
                                        <p class="font-mono text-sm">{{ (metric as any).metric?.name || metric.metric_name }}</p>
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">目标类型</Label>
                                        <p class="text-sm">{{ getMetricTargetType(metric) }}</p>
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">目标值</Label>
                                        <p class="text-sm">{{ getMetricTargetValue(metric) }}</p>
                                    </div>
                                </div>

                                <!-- Object 类型的额外信息 -->
                                <div v-if="metric.type === 'Object'" class="mt-4 border-t pt-4">
                                    <Label class="text-sm font-medium text-gray-500 dark:text-gray-400">目标对象</Label>
                                    <p class="text-sm">
                                        {{ (metric as any).describedObject?.kind || metric.object_kind }}/{{
                                            (metric as any).describedObject?.name || metric.object_name
                                        }}
                                        <span class="text-gray-500"
                                            >({{ (metric as any).describedObject?.apiVersion || metric.object_api_version }})</span
                                        >
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 状态条件 -->
                <Card v-if="hpaData.conditions && hpaData.conditions.length > 0">
                    <CardHeader>
                        <CardTitle>状态条件</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-3">
                            <div
                                v-for="(condition, index) in hpaData.conditions"
                                :key="index"
                                class="flex items-start space-x-3 rounded-lg border p-3"
                            >
                                <component
                                    :is="getConditionIcon(condition.type, condition.status)"
                                    :class="getConditionIconColor(condition.status)"
                                    class="mt-0.5 h-5 w-5 flex-shrink-0"
                                />
                                <div class="min-w-0 flex-1">
                                    <div class="flex items-center space-x-2">
                                        <Badge :variant="getConditionBadgeVariant(condition.status)">
                                            {{ condition.type }}
                                        </Badge>
                                        <span class="text-sm text-gray-500">{{ condition.status }}</span>
                                    </div>
                                    <p v-if="condition.message" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        {{ condition.message }}
                                    </p>
                                    <p v-if="condition.lastTransitionTime" class="mt-1 text-xs text-gray-500">
                                        {{ formatDate(condition.lastTransitionTime) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="showDeleteDialog">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除</AlertDialogTitle>
                        <AlertDialogDescription>
                            您确定要删除弹性伸缩 "{{ hpaData?.name }}" 吗？此操作无法撤销，删除后工作负载将失去弹性伸缩功能。
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                            @click="deleteHPA"
                            :disabled="deleting"
                            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {{ deleting ? '删除中...' : '删除' }}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { HorizontalPodAutoscaler, Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { AlertCircle, ArrowLeft, CheckCircle, Clock, Edit, Minus, Trash2, TrendingDown, XCircle, Zap } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref } from 'vue';
import { route } from 'ziggy-js';

interface Props {
    hpaName: string;
    workspace: Workspace;
}

const props = defineProps<Props>();

const hpaData = ref<HorizontalPodAutoscaler | null>(null);
const loading = ref(true);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);
const deleting = ref(false);

const loadHPA = async () => {
    try {
        loading.value = true;
        error.value = null;
        const response = await axios.get(`/api/hpas/${props.hpaName}`);
        hpaData.value = response.data;
    } catch (err: any) {
        console.error('加载 HPA 详情失败:', err);
        error.value = err.response?.data?.message || '加载 HPA 详情失败';
    } finally {
        loading.value = false;
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'Active':
            return 'default';
        case 'Limited':
            return 'secondary';
        case 'Inactive':
            return 'outline';
        case 'Unable to Scale':
            return 'destructive';
        default:
            return 'outline';
    }
};

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'Active':
            return Zap;
        case 'Limited':
            return Minus;
        case 'Inactive':
            return TrendingDown;
        case 'Unable to Scale':
            return TrendingDown;
        default:
            return Minus;
    }
};

const getCpuUtilizationColor = (current: number, target: number | undefined) => {
    if (!target) return 'bg-gray-400';
    const ratio = current / target;
    if (ratio < 0.8) return 'bg-green-500';
    if (ratio < 1.0) return 'bg-yellow-500';
    return 'bg-red-500';
};

const getMemoryUtilizationColor = (current: number, target: number | undefined) => {
    if (!target) return 'bg-gray-400';
    const ratio = current / target;
    if (ratio < 0.8) return 'bg-blue-500';
    if (ratio < 1.0) return 'bg-yellow-500';
    return 'bg-red-500';
};

const getMetricTargetType = (metric: any) => {
    if (metric.resource?.target) {
        return metric.resource.target.type;
    }
    if (metric.pods?.target) {
        return metric.pods.target.type;
    }
    if (metric.object?.target) {
        return metric.object.target.type;
    }
    if (metric.external?.target) {
        return metric.external.target.type;
    }
    return metric.target_type || '-';
};

const getMetricTargetValue = (metric: any) => {
    if (metric.resource?.target) {
        if (metric.resource.target.averageUtilization) {
            return `${metric.resource.target.averageUtilization}%`;
        }
        if (metric.resource.target.averageValue) {
            return metric.resource.target.averageValue;
        }
    }
    if (metric.pods?.target?.averageValue) {
        return metric.pods.target.averageValue;
    }
    if (metric.object?.target?.value) {
        return metric.object.target.value;
    }
    if (metric.external?.target) {
        return metric.external.target.value || metric.external.target.averageValue;
    }
    return metric.target_value || '-';
};

const getConditionIcon = (type: string, status: string) => {
    if (status === 'True') {
        return CheckCircle;
    } else if (status === 'False') {
        return XCircle;
    }
    return Clock;
};

const getConditionIconColor = (status: string) => {
    switch (status) {
        case 'True':
            return 'text-green-500';
        case 'False':
            return 'text-red-500';
        default:
            return 'text-yellow-500';
    }
};

const getConditionBadgeVariant = (status: string) => {
    switch (status) {
        case 'True':
            return 'default';
        case 'False':
            return 'destructive';
        case 'Unknown':
            return 'secondary';
        default:
            return 'outline';
    }
};

const editHPA = () => {
    router.visit(route('hpas.edit', { hpa: props.hpaName }));
};

const deleteHPA = async () => {
    try {
        deleting.value = true;
        await axios.delete(`/api/hpas/${props.hpaName}`);
        toast.success('HPA 删除成功');
        router.visit(route('hpas.index'));
    } catch (error: any) {
        console.error('删除 HPA 失败:', error);
        const message = error.response?.data?.message || '删除 HPA 失败';
        toast.error(message);
    } finally {
        deleting.value = false;
        showDeleteDialog.value = false;
    }
};

onMounted(() => {
    loadHPA();
});
</script>
