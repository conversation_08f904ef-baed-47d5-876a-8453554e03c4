<script setup lang="ts">
import DataPreview from '@/components/DataPreview.vue';
import WorkloadSelector from '@/components/selectors/WorkloadSelector.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { CreateHPAData, HPAMetric, Workspace } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { ArrowLeft, Plus, Trash2, Zap } from 'lucide-vue-next';
import { toast } from 'sonner';
import { onMounted, ref, watch } from 'vue';

interface Props {
    workspace: Workspace;
}

const props = defineProps<Props>();

const submitting = ref(false);

// 使用响应式对象而不是 useForm
const form = ref<CreateHPAData>({
    name: '',
    scale_target_ref: {
        kind: '',
        name: '',
        api_version: 'apps/v1',
    },
    target_type: '',
    target_name: '',
    min_replicas: 1,
    max_replicas: 10,
    metrics: [],
    behavior: {
        scale_up: {
            stabilization_window_seconds: 0,
        },
        scale_down: {
            stabilization_window_seconds: 300,
        },
    },
});

const errors = ref<Record<string, string>>({});

// 选中的工作负载
const selectedWorkload = ref<{ type: string; name: string } | null>(null);

const metricTypes = [
    { value: 'Resource', label: '资源指标 (CPU/内存)' },
    // { value: 'Pods', label: 'Pod 指标' },
    // { value: 'Object', label: '对象指标' },
    // { value: 'External', label: '外部指标' }
];

const resourceNames = [
    { value: 'cpu', label: 'CPU' },
    { value: 'memory', label: '内存' },
];

const targetTypes = [
    { value: 'Utilization', label: '使用率 (%)' },
    { value: 'AverageValue', label: '平均值' },
];

const addMetric = () => {
    const newMetric: HPAMetric = {
        type: 'Resource',
        resource: {
            name: 'cpu',
            target: {
                type: 'Utilization',
                average_utilization: 80,
            },
        },
    };
    form.value.metrics.push(newMetric);
};

const removeMetric = (index: number) => {
    form.value.metrics.splice(index, 1);
};

// 监听工作负载选择变化
watch(
    selectedWorkload,
    (newWorkload) => {
        if (newWorkload) {
            form.value.scale_target_ref.kind = newWorkload.type;
            form.value.scale_target_ref.name = newWorkload.name;
            form.value.target_type = newWorkload.type;
            form.value.target_name = newWorkload.name;

            // 自动生成 HPA 名称
            if (!form.value.name) {
                form.value.name = `${newWorkload.name}-hpa`;
            }
        }
    },
    { deep: true },
);

const updateMetricResourceName = (index: number, value: string) => {
    const metric = form.value.metrics[index];
    if (metric.resource) {
        metric.resource.name = value;
    }
};

const updateMetricTargetType = (index: number, value: string) => {
    const metric = form.value.metrics[index];
    if (metric.resource) {
        const target = metric.resource.target;
        target.type = value as 'Utilization' | 'AverageValue';

        // 清理不相关的字段
        if (value === 'Utilization') {
            delete target.average_value;
            if (!target.average_utilization) {
                target.average_utilization = 80;
            }
        } else {
            delete target.average_utilization;
            if (!target.average_value) {
                target.average_value = metric.resource.name === 'cpu' ? 500 : 512;
            }
        }
    }
};

const submit = async () => {
    // 验证至少有一个指标
    if (form.value.metrics.length === 0) {
        toast.error('至少需要配置一个指标');
        return;
    }

    try {
        submitting.value = true;
        errors.value = {};

        // 转换表单数据为 API 格式
        const payload = {
            name: form.value.name,
            target_type: form.value.scale_target_ref.kind,
            target_name: form.value.scale_target_ref.name,
            min_replicas: form.value.min_replicas,
            max_replicas: form.value.max_replicas,
            metrics: form.value.metrics.map((metric) => {
                if (metric.type === 'Resource' && metric.resource) {
                    return {
                        type: 'Resource',
                        resource_name: metric.resource.name,
                        target_type: metric.resource.target.type,
                        target_value:
                            metric.resource.target.type === 'Utilization'
                                ? metric.resource.target.average_utilization?.toString()
                                : metric.resource.target.average_value,
                    };
                }
                return metric;
            }),
            behavior: form.value.behavior,
        };

        await axios.post('/api/hpas', payload);
        toast.success('HPA 创建成功');
        router.visit(route('hpas.index'));
    } catch (error: any) {
        console.error('创建 HPA 失败:', error);
        if (error.response?.data?.errors) {
            errors.value = error.response.data.errors;
        }
        const message = error.response?.data?.message || '创建 HPA 失败，请检查表单';
        toast.error(message);
    } finally {
        submitting.value = false;
    }
};

onMounted(() => {
    // 添加默认指标
    addMetric();
});
</script>

<template>
    <Head title="创建弹性伸缩" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题 -->
            <div class="flex items-center space-x-4">
                <Link :href="route('hpas.index')">
                    <Button variant="ghost" size="sm">
                        <ArrowLeft class="h-4 w-4" />
                    </Button>
                </Link>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">创建弹性伸缩</h1>
                    <p class="text-muted-foreground">为工作负载配置自动扩缩容策略</p>
                </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <!-- 基本信息 -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center space-x-2">
                            <Zap class="h-5 w-5" />
                            <span>基本信息</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label>弹性伸缩名称</Label>
                                <Input v-model="form.name" placeholder="输入弹性伸缩名称" :class="{ 'border-destructive': errors.name }" />
                                <div v-if="errors.name" class="mt-1 text-sm text-destructive">
                                    {{ errors.name }}
                                </div>
                            </div>

                            <div>
                                <Label>最小副本数</Label>
                                <Input
                                    v-model.number="form.min_replicas"
                                    type="number"
                                    min="1"
                                    :class="{ 'border-destructive': errors.min_replicas }"
                                />
                                <div v-if="errors.min_replicas" class="mt-1 text-sm text-destructive">
                                    {{ errors.min_replicas }}
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <Label>最大副本数</Label>
                                <Input
                                    v-model.number="form.max_replicas"
                                    type="number"
                                    min="1"
                                    :class="{ 'border-destructive': errors.max_replicas }"
                                />
                                <div v-if="errors.max_replicas" class="mt-1 text-sm text-destructive">
                                    {{ errors.max_replicas }}
                                </div>
                            </div>
                        </div>

                        <!-- 工作负载选择器 -->
                        <div>
                            <WorkloadSelector
                                v-model="selectedWorkload"
                                label="目标工作负载"
                                description="选择要进行弹性伸缩的工作负载"
                                :show-ports="false"
                            />
                            <div v-if="errors['target_type'] || errors['target_name']" class="mt-1 text-sm text-destructive">
                                {{ errors['target_type'] || errors['target_name'] }}
                            </div>
                        </div>

                        <!-- <div v-if="selectedWorkload" class="rounded-md bg-muted p-3">
                            <div class="space-y-1 text-sm">
                                <div v-if="false">已选择: {{ selectedWorkload.type }}/{{ selectedWorkload.name }}</div>
                            </div>
                        </div> -->
                    </CardContent>
                </Card>

                <!-- 扩缩容指标 -->
                <Card>
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <CardTitle>扩缩容指标</CardTitle>
                            <Button type="button" variant="outline" size="sm" @click="addMetric">
                                <Plus class="mr-2 h-4 w-4" />
                                添加指标
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div v-if="form.metrics.length === 0" class="py-8 text-center text-muted-foreground">
                            <p>请添加至少一个扩缩容指标</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div v-for="(metric, index) in form.metrics" :key="index" class="space-y-4 rounded-lg border p-4">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium">指标 {{ index + 1 }}</h4>
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        @click="removeMetric(index)"
                                        class="text-destructive hover:text-destructive"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </div>

                                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div>
                                        <Label>指标类型</Label>
                                        <Select
                                            :model-value="metric.type"
                                            @update:model-value="(value) => (metric.type = String(value || 'Resource') as any)"
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in metricTypes" :key="type.value" :value="type.value">
                                                    {{ type.label }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <template v-if="metric.type === 'Resource' && metric.resource">
                                        <div>
                                            <Label>资源类型</Label>
                                            <Select
                                                :model-value="metric.resource.name"
                                                @update:model-value="(value) => updateMetricResourceName(index, String(value || 'cpu'))"
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="resource in resourceNames" :key="resource.value" :value="resource.value">
                                                        {{ resource.label }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label>目标类型</Label>
                                            <Select
                                                :model-value="metric.resource.target.type"
                                                @update:model-value="(value) => updateMetricTargetType(index, String(value || 'Utilization'))"
                                            >
                                                <SelectTrigger>
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="target in targetTypes" :key="target.value" :value="target.value">
                                                        {{ target.label }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </template>
                                </div>

                                <template v-if="metric.type === 'Resource' && metric.resource">
                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div v-if="metric.resource.target.type === 'Utilization'">
                                            <Label>目标使用率 (%)</Label>
                                            <Input
                                                v-model.number="metric.resource.target.average_utilization"
                                                type="number"
                                                min="1"
                                                max="100"
                                                placeholder="80"
                                            />
                                        </div>

                                        <div v-if="metric.resource.target.type === 'AverageValue'">
                                            <Label>
                                                目标平均值
                                                <span class="text-sm text-muted-foreground"> (单位 MB) </span>
                                            </Label>
                                            <Input
                                                v-model.number="metric.resource.target.average_value"
                                                type="number"
                                                :min="metric.resource.name === 'cpu' ? 500 : 512"
                                                :step="metric.resource.name === 'cpu' ? 500 : 512"
                                                :placeholder="metric.resource.name === 'cpu' ? '500' : '512'"
                                            />
                                            <p class="mt-1 text-xs text-gray-500">
                                                {{
                                                    metric.resource.name === 'cpu'
                                                        ? 'CPU 必须是 500 的倍数，最低 500'
                                                        : '内存必须是 512 的倍数，最低 512'
                                                }}
                                            </p>
                                        </div>
                                    </div>
                                </template>

                                <!-- 其他指标类型的配置 -->
                                <template v-if="metric.type === 'Pods'">
                                    <div class="text-sm text-muted-foreground">Pod 指标配置 (需要自定义指标服务器支持)</div>
                                </template>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- 提交按钮 -->
                <div class="flex items-center justify-end space-x-4">
                    <Link :href="route('hpas.index')">
                        <Button type="button" variant="outline">取消</Button>
                    </Link>
                    <Button type="submit" :disabled="submitting">
                        {{ submitting ? '创建中...' : '创建弹性伸缩' }}
                    </Button>
                </div>
            </form>

            <div class="p-4">
                <DataPreview api="/api/hpas" method="POST" :data-ref="form" />
            </div>
        </div>
    </AppLayout>
</template>
