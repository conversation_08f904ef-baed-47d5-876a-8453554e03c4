<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import { getClusterPricingInfo } from '@/lib/pricing';
import type { Cluster, ClusterPricing, CreateWorkspaceData } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Plus, Server } from 'lucide-vue-next';
import { toast } from 'sonner';
import { reactive, ref } from 'vue';

interface Props {
    clusters: Cluster[];
}

const props = defineProps<Props>();

const form = reactive<CreateWorkspaceData>({
    name: '',
    cluster_id: 0,
});

const errors = ref<Record<string, string>>({});
const isSubmitting = ref(false);

const validateForm = () => {
    errors.value = {};

    if (!form.name.trim()) {
        errors.value.name = '工作空间名称不能为空';
    } else if (form.name.length < 3) {
        errors.value.name = '工作空间名称至少需要3个字符';
    } else if (form.name.length > 50) {
        errors.value.name = '工作空间名称不能超过50个字符';
    } else if (!/^[a-zA-Z0-9\-_]+$/.test(form.name)) {
        errors.value.name = '工作空间名称只能包含字母、数字、连字符和下划线';
    }

    if (!form.cluster_id) {
        errors.value.cluster_id = '请选择一个集群';
    }

    return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
    if (!validateForm()) {
        return;
    }

    isSubmitting.value = true;

    try {
        const response = await axios.post('/api/workspaces', form);

        toast.success('工作空间创建成功！');
        router.visit(route('workspaces.index'));
    } catch (error: any) {
        if (error.response?.status === 422) {
            // 验证错误
            errors.value = error.response.data.errors || {};
            toast.error('创建失败，请检查表单信息');
        } else {
            // 其他错误
            toast.error(error.response?.data?.message || '创建失败，请稍后重试');
        }
    } finally {
        isSubmitting.value = false;
    }
};

const goBack = () => {
    router.visit(route('workspaces.index'));
};

const selectedCluster = ref<Cluster | null>(null);

const handleClusterChange = (value: any) => {
    if (!value) return;
    const id = typeof value === 'string' ? parseInt(value) : Number(value);
    form.cluster_id = id;
    selectedCluster.value = props.clusters.find((c) => c.id === id) || null;
    if (errors.value.cluster_id) {
        delete errors.value.cluster_id;
    }
};

const getPricingInfo = (pricing: ClusterPricing) => {
    return getClusterPricingInfo(pricing);
};
</script>

<template>
    <Head title="创建工作空间" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题和返回按钮 -->
            <div class="flex items-center gap-4">
                <Button variant="ghost" size="sm" @click="goBack">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">创建工作空间</h1>
                    <p class="text-muted-foreground">创建一个新的工作空间</p>
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-3">
                <!-- 创建表单 -->
                <div class="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>工作空间配置</CardTitle>
                            <CardDescription> 填写工作空间的基本信息 </CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-6">
                            <form @submit.prevent="handleSubmit" class="space-y-4">
                                <!-- 工作空间名称 -->
                                <div class="space-y-2">
                                    <Label for="name">工作空间名称 *</Label>
                                    <Input
                                        id="name"
                                        v-model="form.name"
                                        placeholder="输入工作空间名称"
                                        :class="{ 'border-red-500': errors.name }"
                                        @input="errors.name && delete errors.name"
                                    />
                                    <p v-if="errors.name" class="text-sm text-red-500">{{ errors.name }}</p>
                                    <p class="text-sm text-muted-foreground">只能包含字母、数字、连字符和下划线，3-50个字符</p>
                                </div>

                                <!-- 集群选择 -->
                                <div class="space-y-2">
                                    <Label for="cluster">选择集群 *</Label>
                                    <Select @update:modelValue="handleClusterChange">
                                        <SelectTrigger :class="{ 'border-red-500': errors.cluster_id }">
                                            <SelectValue placeholder="选择一个集群" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="cluster in clusters" :key="cluster.id" :value="cluster.id.toString()">
                                                <div class="flex items-center gap-2">
                                                    <Server class="h-4 w-4" />
                                                    <div class="font-medium">{{ cluster.name }}</div>
                                                </div>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <p v-if="errors.cluster_id" class="text-sm text-red-500">{{ errors.cluster_id }}</p>
                                    <p class="text-sm text-muted-foreground">您的工作负载要部署到哪里？</p>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="flex gap-3 pt-4">
                                    <Button type="submit" :disabled="isSubmitting" class="flex-1">
                                        <Plus v-if="!isSubmitting" class="mr-2 h-4 w-4" />
                                        {{ isSubmitting ? '创建中...' : '创建工作空间' }}
                                    </Button>
                                    <Button type="button" variant="outline" @click="goBack" :disabled="isSubmitting"> 取消 </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                <!-- 信息面板 -->
                <div class="space-y-6">
                    <!-- 预览信息 -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="text-lg">预览信息</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-muted-foreground">工作空间名称:</span>
                                    <span class="font-medium">{{ form.name || '未填写' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-muted-foreground">选择的集群:</span>
                                    <span class="font-medium">{{ selectedCluster?.name || '未选择' }}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- 帮助信息 -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="text-lg">关于工作空间</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3 text-sm">
                            <div class="space-y-2">
                                <p class="text-muted-foreground">工作区是存放您的应用的独立环境，用于部署和管理您的应用程序。</p>
                                <ul class="list-inside list-disc space-y-1 text-muted-foreground">
                                    <li>工作区一旦创建，无法更改集群</li>
                                    <li>每个集群的定价策略不同，配置也有可能不同。</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- 集群信息 -->
                    <Card v-if="selectedCluster">
                        <CardHeader>
                            <CardTitle class="text-lg">集群信息</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4 text-sm">
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">集群名称:</span>
                                <span class="font-medium">{{ selectedCluster.name }}</span>
                            </div>

                            <!-- 定价信息 -->
                            <div v-if="selectedCluster.pricing && selectedCluster.pricing.billing_enabled" class="space-y-3">
                                <div class="border-t pt-3">
                                    <h4 class="mb-2 font-medium text-gray-900 dark:text-white">资源定价</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between">
                                            <span class="text-muted-foreground">内存 (每GB/月):</span>
                                            <div class="text-right">
                                                <div class="font-medium">{{ getPricingInfo(selectedCluster.pricing).memory.monthly }}</div>
                                                <div class="text-xs text-muted-foreground">
                                                    约 {{ getPricingInfo(selectedCluster.pricing).memory.hourly }}/小时
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-muted-foreground">CPU (每核/月):</span>
                                            <div class="text-right">
                                                <div class="font-medium">{{ getPricingInfo(selectedCluster.pricing).cpu.monthly }}</div>
                                                <div class="text-xs text-muted-foreground">
                                                    约 {{ getPricingInfo(selectedCluster.pricing).cpu.hourly }}/小时
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-muted-foreground">存储 (每GB/月):</span>
                                            <div class="text-right">
                                                <div class="font-medium">{{ getPricingInfo(selectedCluster.pricing).storage.monthly }}</div>
                                                <div class="text-xs text-muted-foreground">
                                                    约 {{ getPricingInfo(selectedCluster.pricing).storage.hourly }}/小时
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-muted-foreground">负载均衡器 (每个/月):</span>
                                            <div class="text-right">
                                                <div class="font-medium">{{ getPricingInfo(selectedCluster.pricing).loadbalancer.monthly }}</div>
                                                <div class="text-xs text-muted-foreground">
                                                    约 {{ getPricingInfo(selectedCluster.pricing).loadbalancer.hourly }}/小时
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p v-if="selectedCluster.pricing.description" class="mt-2 text-xs text-muted-foreground">
                                        {{ selectedCluster.pricing.description }}
                                    </p>
                                </div>
                            </div>

                            <!-- 无定价策略提示 -->
                            <div v-else-if="selectedCluster.pricing && !selectedCluster.pricing.billing_enabled" class="space-y-2">
                                <div class="border-t pt-3">
                                    <p class="text-muted-foreground">当前集群暂时没有定价策略</p>
                                </div>
                            </div>

                            <!-- 未配置定价 -->
                            <div v-else class="space-y-2">
                                <div class="border-t pt-3">
                                    <p class="text-muted-foreground">当前集群暂时没有定价策略</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
