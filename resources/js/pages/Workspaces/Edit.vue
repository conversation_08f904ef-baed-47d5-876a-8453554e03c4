<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import axios from '@/lib/axios';
import type { Workspace } from '@/types';
import { Head, router } from '@inertiajs/vue3';
import { ArrowLeft, Save } from 'lucide-vue-next';
import { toast } from 'sonner';
import { reactive, ref } from 'vue';

interface Props {
    workspace: {
        data: Workspace;
    };
}

const props = defineProps<Props>();

// 提取实际的 workspace 数据
const workspace = props.workspace.data;

const form = reactive({
    name: workspace.name,
    cluster_id: workspace.cluster.id,
});

const errors = ref<Record<string, string>>({});
const isSubmitting = ref(false);

const validateForm = () => {
    errors.value = {};

    if (!form.name.trim()) {
        errors.value.name = '工作空间名称不能为空';
    } else if (form.name.length < 3) {
        errors.value.name = '工作空间名称至少需要3个字符';
    } else if (form.name.length > 50) {
        errors.value.name = '工作空间名称不能超过50个字符';
    } else if (!/^[a-zA-Z0-9\-_]+$/.test(form.name)) {
        errors.value.name = '工作空间名称只能包含字母、数字、连字符和下划线';
    }

    if (!form.cluster_id) {
        errors.value.cluster_id = '请选择一个集群';
    }

    return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
    if (!validateForm()) {
        return;
    }

    isSubmitting.value = true;

    try {
        await axios.put(`/api/workspaces/${workspace.id}`, form);

        toast.success('工作空间更新成功！');
        router.visit(route('workspaces.show', { workspace: workspace.id }));
    } catch (error: any) {
        if (error.response?.status === 422) {
            // 验证错误
            errors.value = error.response.data.errors || {};
            toast.error('更新失败，请检查表单信息');
        } else {
            // 其他错误
            toast.error(error.response?.data?.message || '更新失败，请稍后重试');
        }
    } finally {
        isSubmitting.value = false;
    }
};

const goBack = () => {
    router.visit(route('workspaces.show', { workspace: workspace.id }));
};
</script>

<template>
    <Head :title="`编辑 ${workspace.name}`" />

    <AppLayout>
        <div class="space-y-6 p-4">
            <!-- 页面标题和返回按钮 -->
            <div class="flex items-center gap-4">
                <Button variant="ghost" size="sm" @click="goBack">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    返回
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">编辑工作空间</h1>
                    <p class="text-muted-foreground">修改工作空间 "{{ workspace.name }}" 的配置</p>
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-3">
                <!-- 编辑表单 -->
                <div class="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>工作空间配置</CardTitle>
                            <CardDescription> 修改工作空间的基本信息 </CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-6">
                            <form @submit.prevent="handleSubmit" class="space-y-4">
                                <!-- 工作空间名称 -->
                                <div class="space-y-2">
                                    <Label for="name">工作空间名称 *</Label>
                                    <Input
                                        id="name"
                                        v-model="form.name"
                                        placeholder="输入工作空间名称"
                                        :class="{ 'border-red-500': errors.name }"
                                        @input="errors.name && delete errors.name"
                                    />
                                    <p v-if="errors.name" class="text-sm text-red-500">{{ errors.name }}</p>
                                    <p class="text-sm text-muted-foreground">只能包含字母、数字、连字符和下划线，3-50个字符</p>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="flex gap-3 pt-4">
                                    <Button type="submit" :disabled="isSubmitting" class="flex-1">
                                        <Save v-if="!isSubmitting" class="mr-2 h-4 w-4" />
                                        {{ isSubmitting ? '更新中...' : '保存更改' }}
                                    </Button>
                                    <Button type="button" variant="outline" @click="goBack" :disabled="isSubmitting"> 取消 </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                <!-- 信息面板 -->
                <div class="space-y-6">
                    <!-- 注意事项 -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="text-lg">注意事项</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-3 text-sm">
                            <div class="space-y-2">
                                <p class="text-muted-foreground">工作空间创建后，无法更改集群</p>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- 原始信息 -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="text-lg">原始信息</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">原名称:</span>
                                <span class="font-medium">{{ workspace.name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">原集群:</span>
                                <span class="font-medium">{{ workspace.cluster.name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-muted-foreground">创建时间:</span>
                                <span class="text-xs">{{ new Date(workspace.created_at).toLocaleString('zh-CN') }}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
