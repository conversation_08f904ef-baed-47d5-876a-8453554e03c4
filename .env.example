APP_NAME=LeaflowRun
APP_DISPLAY_NAME="Leaflow Run"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_STACK=daily

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=leaflow_run
DB_USERNAME=root
DB_PASSWORD=

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Terminal WebSocket 主机（在本地环境和生产环境是不同的）
# 比如本地一般是在 127.0.0.1
WEBSOCKET_URL=ws://127.0.0.1:8282
# 但是生产环境可能被 Nginx 转发后变成了一个 path
# WEBSOCKET_HOST=wss://paasnew.test/websocket

REVERB_APP_ID=656939
REVERB_APP_KEY=mheraksw6ymyncyrntn1
REVERB_APP_SECRET=slykvew3teu8k8e6k8bq
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"

# App Name
VITE_APP_NAME="${APP_DISPLAY_NAME}"

# 容器镜像大小验证
K8S_IMAGE_VALIDATION_ENABLED=false