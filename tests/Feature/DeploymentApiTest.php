<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeploymentApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建测试集群
        $this->cluster = Cluster::factory()->token()->create([
            'name' => 'test-cluster',
        ]);

        // 创建测试工作空间
        $this->workspace = Workspace::factory()->create([
            'name' => 'test-workspace',
            'cluster_id' => $this->cluster->id,
            'namespace' => 'test-namespace',
        ]);

        // 设置用户当前工作空间
        $this->user->current_workspace_id = $this->workspace->id;
        $this->user->save();
    }

    public function test_can_list_deployments()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/deployments');

        // 如果集群不可用，期望 422 错误，否则期望 200 成功
        $statusCode = $response->getStatusCode();
        $this->assertTrue(in_array($statusCode, [200, 422, 500]), "Unexpected status code: {$statusCode}. Response: ".$response->getContent());

        if ($response->getStatusCode() === 200) {
            $response->assertJsonStructure([
                '*' => [
                    'name',
                    'namespace',
                    'replicas',
                    'readyReplicas',
                    'status',
                    'containers',
                ],
            ]);
        }
    }

    public function test_can_create_deployment()
    {
        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'env' => [
                        [
                            'name' => 'ENV_VAR',
                            'value' => 'test-value',
                        ],
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $deploymentData);

        // 如果集群不可用，期望 422 或 500 错误，否则期望 201 成功
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422, 500]));
    }

    public function test_validate_deployment_name()
    {
        $deploymentData = [
            'name' => 'INVALID_NAME',  // 无效的名称（大写）
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $deploymentData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_validate_resource_requirements()
    {
        $deploymentData = [
            'name' => 'test-deployment',
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 300,  // 无效：不是 512 的倍数
                        'cpu' => 100,     // 无效：不是 500 的倍数
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $deploymentData);

        $response->assertStatus(422);
    }

    public function test_requires_workspace()
    {
        // 创建没有工作空间的用户
        $userWithoutWorkspace = User::factory()->create();

        $response = $this->actingAs($userWithoutWorkspace)
            ->getJson('/api/deployments');

        $response->assertStatus(422)
            ->assertJson(['message' => '请先选择工作空间']);
    }

    /** @test */
    public function can_create_deployment_with_volume_mounts()
    {
        $data = [
            'name' => 'test-with-volumes',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'volume_mounts' => [
                        [
                            'mount_path' => '/var/www/html',
                            'storage_name' => 'web-content',
                            'sub_path' => 'public',
                            'read_only' => false,
                        ],
                        [
                            'mount_path' => '/etc/nginx/conf.d',
                            'storage_name' => 'nginx-config',
                            'read_only' => true,
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $data);

        // 如果集群不可用，期望 422 或 500 错误，否则期望 201 成功
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422, 500]));

        // 如果成功创建，验证返回的数据结构正确
        if ($response->getStatusCode() === 201) {
            $response->assertJsonStructure([
                'name',
                'namespace',
                'replicas',
                'containers' => [
                    '*' => [
                        'name',
                        'image',
                        'volume_mounts' => [
                            '*' => [
                                'mount_path',
                                'storage_name',
                                'sub_path',
                                'read_only',
                            ],
                        ],
                    ],
                ],
            ]);

            // 验证返回的数据结构正确
            $container = $response->json('containers.0');
            $this->assertCount(2, $container['volume_mounts']);

            $volumeMount1 = $container['volume_mounts'][0];
            $this->assertEquals('/var/www/html', $volumeMount1['mount_path']);
            $this->assertEquals('web-content', $volumeMount1['storage_name']);
            $this->assertEquals('public', $volumeMount1['sub_path']);
            $this->assertFalse($volumeMount1['read_only']);

            $volumeMount2 = $container['volume_mounts'][1];
            $this->assertEquals('/etc/nginx/conf.d', $volumeMount2['mount_path']);
            $this->assertEquals('nginx-config', $volumeMount2['storage_name']);
            $this->assertNull($volumeMount2['sub_path']);
            $this->assertTrue($volumeMount2['read_only']);
        }
    }

    /**
     * 测试卷挂载数据结构正确性
     */
    public function test_volume_mounts_data_structure_is_correct()
    {
        $data = [
            'name' => 'test-volume-structure',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'volume_mounts' => [
                        [
                            'mount_path' => '/var/www/html',
                            'storage_name' => 'web-content',
                            'sub_path' => 'public',
                            'read_only' => false,
                        ],
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $data);

        // 如果集群不可用，期望 422 或 500 错误，否则期望 201 成功
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422, 500]));

        // 如果成功创建，验证返回的数据结构
        if ($response->getStatusCode() === 201) {
            $responseData = $response->json();

            // 验证容器的卷挂载数据结构
            $this->assertArrayHasKey('containers', $responseData);
            $this->assertIsArray($responseData['containers']);
            $this->assertGreaterThan(0, count($responseData['containers']));

            $container = $responseData['containers'][0];
            $this->assertArrayHasKey('volume_mounts', $container);
            $this->assertIsArray($container['volume_mounts']);

            if (count($container['volume_mounts']) > 0) {
                $volumeMount = $container['volume_mounts'][0];

                // 验证正确的字段存在
                $this->assertArrayHasKey('mount_path', $volumeMount);
                $this->assertArrayHasKey('storage_name', $volumeMount);
                $this->assertArrayHasKey('sub_path', $volumeMount);
                $this->assertArrayHasKey('read_only', $volumeMount);

                // 验证字段值正确
                $this->assertEquals('/var/www/html', $volumeMount['mount_path']);
                $this->assertEquals('web-content', $volumeMount['storage_name']);
                $this->assertEquals('public', $volumeMount['sub_path']);
                $this->assertFalse($volumeMount['read_only']);

                // 重要：验证不包含 name 字段
                $this->assertArrayNotHasKey('name', $volumeMount);

                // 验证不包含其他遗留字段
                $this->assertArrayNotHasKey('mountPath', $volumeMount);
                $this->assertArrayNotHasKey('storageName', $volumeMount);
                $this->assertArrayNotHasKey('subPath', $volumeMount);
                $this->assertArrayNotHasKey('readOnly', $volumeMount);
            }
        }
    }

    /** @test */
    public function test_can_create_deployment_with_command_and_env_references()
    {
        $data = [
            'name' => 'test-with-advanced-features',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'command' => ['/bin/sh', '-c'],
                    'args' => ['nginx -g "daemon off;"'],
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'env' => [
                        [
                            'name' => 'ENV_VAR',
                            'value' => 'test-value',
                        ],
                    ],
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'my-config',
                            'key' => 'database_url',
                            'env_name' => 'DATABASE_URL',
                        ],
                        [
                            'configmap_name' => 'app-config',
                            // 当 key 为空时，引用整个 ConfigMap
                        ],
                    ],
                    'env_from_secret' => [
                        [
                            'secret_name' => 'my-secret',
                            'key' => 'password',
                            'env_name' => 'DB_PASSWORD',
                        ],
                    ],
                    'configmap_mounts' => [
                        [
                            'configmap_name' => 'nginx-config',
                            'mount_path' => '/etc/nginx/conf.d',
                            'default_mode' => '644', // 八进制格式
                            'items' => [
                                [
                                    'key' => 'nginx.conf',
                                    'path' => 'default.conf',
                                ],
                            ],
                        ],
                    ],
                    'secret_mounts' => [
                        [
                            'secret_name' => 'tls-secret',
                            'mount_path' => '/etc/ssl/certs',
                            'default_mode' => '600', // 八进制格式
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/deployments', $data);

        // 如果集群不可用，期望 422 或 500 错误，否则期望 201 成功
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422, 500]));

        // 如果成功创建，验证返回的数据结构正确
        if ($response->getStatusCode() === 201) {
            $response->assertJsonStructure([
                'name',
                'namespace',
                'replicas',
                'containers' => [
                    '*' => [
                        'name',
                        'image',
                        'command',
                        'args',
                        'ports',
                        'env',
                        'resources',
                    ],
                ],
            ]);

            // 验证容器配置是否正确返回
            $responseData = $response->json();
            $container = $responseData['containers'][0];

            $this->assertEquals(['sh', '-c'], $container['command'] ?? []);
            $this->assertEquals(['nginx -g "daemon off;"'], $container['args'] ?? []);
            $this->assertNotEmpty($container['env']);
        }
    }
}
