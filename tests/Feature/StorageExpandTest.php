<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StorageExpandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_can_expand_storage()
    {
        // 创建用户和工作空间
        $user = User::factory()->create();
        $workspace = Workspace::factory()->active()->create([
            'user_id' => $user->id,
        ]);

        if (! $workspace) {
            $this->markTestSkipped('Cannot create workspace for testing');
        }

        $user->setCurrentWorkspace($workspace);

        // 首先创建一个 Storage
        $createResponse = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-expand-storage',
                'size' => 512,
            ]);

        if ($createResponse->status() !== 201) {
            $this->markTestSkipped('Cannot create storage: '.$createResponse->getContent());
        }

        // 等待一会儿让 PVC 创建完成
        sleep(2);

        // 然后扩容
        $expandResponse = $this->actingAs($user)
            ->patchJson('/api/storages/test-expand-storage/expand', [
                'size' => 1024,
            ]);

        $expandResponse->assertStatus(200);
        $expandResponse->assertJsonStructure([
            'name',
            'size',
            'storage_class',
            'access_modes',
            'status',
            'formatted_size',
            'created_at',
        ]);

        // 验证新容量
        $expandResponse->assertJson([
            'name' => 'test-expand-storage',
            'formatted_size' => '1024Mi',
        ]);
    }

    public function test_validates_expand_size_minimum()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace || $workspace->status !== 'active') {
            $this->markTestSkipped('No active workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        // 首先创建一个 Storage
        $createResponse = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-expand-small',
                'size' => 512,
            ]);

        if ($createResponse->status() !== 201) {
            $this->markTestSkipped('Cannot create storage: '.$createResponse->getContent());
        }

        // 尝试扩容到小于最小值
        $response = $this->actingAs($user)
            ->patchJson('/api/storages/test-expand-small/expand', [
                'size' => 256, // 小于最小值 512
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);
    }

    public function test_validates_expand_size_multiple_of_512()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace || $workspace->status !== 'active') {
            $this->markTestSkipped('No active workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        // 首先创建一个 Storage
        $createResponse = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-expand-invalid',
                'size' => 512,
            ]);

        if ($createResponse->status() !== 201) {
            $this->markTestSkipped('Cannot create storage: '.$createResponse->getContent());
        }

        // 尝试扩容到不是 512 的倍数
        $response = $this->actingAs($user)
            ->patchJson('/api/storages/test-expand-invalid/expand', [
                'size' => 700, // 不是 512 的倍数
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);
    }

    public function test_cannot_expand_nonexistent_storage()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->patchJson('/api/storages/nonexistent-storage/expand', [
                'size' => 1024,
            ]);

        $response->assertStatus(422);
        $response->assertJsonStructure(['message']);
    }

    public function test_cannot_expand_storage_without_workspace()
    {
        $user = User::first();
        // 清除当前工作空间设置
        $user->current_workspace_id = null;
        $user->save();

        $response = $this->actingAs($user)
            ->patchJson('/api/storages/test-storage/expand', [
                'size' => 1024,
            ]);

        $response->assertStatus(422); // 中间件返回 422 状态码
    }
}
