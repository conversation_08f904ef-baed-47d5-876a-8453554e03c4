<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\JwtService;
use App\Service\PodTerminalService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class PodTerminalConnectionTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 生成 JWT 密钥对
        $this->artisan('websocket:generate-jwt-keys --force');

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create([
            'auth_type' => 'token',
            'token' => 'test-token',
            'server_url' => 'https://kubernetes.docker.internal:6443',
            'insecure_skip_tls_verify' => true,
        ]);
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);
    }

    public function test_can_generate_pod_terminal_token(): void
    {
        // Mock HTTP response for pod existence check
        Http::fake([
            '*/api/v1/namespaces/*/pods/test-pod' => Http::response([
                'metadata' => ['name' => 'test-pod'],
                'spec' => [
                    'containers' => [
                        ['name' => 'test-container'],
                    ],
                ],
            ], 200),
        ]);

        // 设置用户的当前工作空间
        $this->user->update(['current_workspace_id' => $this->workspace->id]);
        setPermissionsTeamId($this->workspace->id);

        $response = $this->actingAs($this->user)
            ->postJson('/api/pods/test-pod/terminal/token', [
                'pod_name' => 'test-pod',
                'container_name' => 'test-container',
                'mode' => 'shell',
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'token',
                'expires_in',
                'websocket_url',
                'pod_name',
                'container_name',
                'mode',
            ]);
    }

    public function test_validates_pod_terminal_token(): void
    {
        $jwtService = new JwtService;

        $token = $jwtService->generatePodTerminalToken(
            $this->user->id,
            $this->workspace->id,
            'test-pod',
            'test-container',
            'shell'
        );

        $tokenData = $jwtService->verifyToken($token);

        $this->assertEquals('pod_terminal', $tokenData['type']);
        $this->assertEquals($this->user->id, $tokenData['user_id']);
        $this->assertEquals($this->workspace->id, $tokenData['workspace_id']);
        $this->assertEquals('test-pod', $tokenData['pod_name']);
        $this->assertEquals('test-container', $tokenData['container_name']);
        $this->assertEquals('shell', $tokenData['mode']);
    }

    public function test_builds_correct_websocket_url_for_shell_mode(): void
    {
        $terminalService = new PodTerminalService(new JwtService);

        // 使用反射来测试受保护的方法
        $reflection = new \ReflectionClass($terminalService);
        $method = $reflection->getMethod('buildWebSocketUrl');
        $method->setAccessible(true);

        $url = $method->invokeArgs($terminalService, [
            $this->cluster,
            $this->workspace->namespace,
            'test-pod',
            'test-container',
            'shell',
        ]);

        $this->assertStringContainsString('wss://kubernetes.docker.internal:6443', $url);
        $this->assertStringContainsString('/api/v1/namespaces/', $url);
        $this->assertStringContainsString('/pods/test-pod/exec', $url);
        $this->assertStringContainsString('container=test-container', $url);
        $this->assertStringContainsString('command=%2Fbin%2Fbash', $url);
        $this->assertStringContainsString('stdin=true', $url);
        $this->assertStringContainsString('stdout=true', $url);
        $this->assertStringContainsString('stderr=true', $url);
        $this->assertStringContainsString('tty=true', $url);
    }

    public function test_builds_correct_websocket_url_for_attach_mode(): void
    {
        $terminalService = new PodTerminalService(new JwtService);

        $reflection = new \ReflectionClass($terminalService);
        $method = $reflection->getMethod('buildWebSocketUrl');
        $method->setAccessible(true);

        $url = $method->invokeArgs($terminalService, [
            $this->cluster,
            $this->workspace->namespace,
            'test-pod',
            'test-container',
            'attach',
        ]);

        $this->assertStringContainsString('wss://kubernetes.docker.internal:6443', $url);
        $this->assertStringContainsString('/api/v1/namespaces/', $url);
        $this->assertStringContainsString('/pods/test-pod/attach', $url);
        $this->assertStringContainsString('container=test-container', $url);
        $this->assertStringContainsString('stdin=true', $url);
        $this->assertStringContainsString('stdout=true', $url);
        $this->assertStringContainsString('stderr=true', $url);
        $this->assertStringContainsString('tty=true', $url);
    }

    public function test_validates_pod_exists_before_connecting(): void
    {
        // Mock HTTP response for pod existence check
        Http::fake([
            '*/api/v1/namespaces/*/pods/existing-pod' => Http::response([
                'metadata' => ['name' => 'existing-pod'],
                'spec' => ['containers' => [['name' => 'test-container']]],
            ], 200),
            '*/api/v1/namespaces/*/pods/non-existing-pod' => Http::response('Not Found', 404),
        ]);

        $terminalService = new PodTerminalService(new JwtService);

        $reflection = new \ReflectionClass($terminalService);
        $method = $reflection->getMethod('validatePodExists');
        $method->setAccessible(true);

        // Test existing pod
        $exists = $method->invokeArgs($terminalService, [$this->workspace, 'existing-pod']);
        $this->assertTrue($exists);

        // Test non-existing pod
        $exists = $method->invokeArgs($terminalService, [$this->workspace, 'non-existing-pod']);
        $this->assertFalse($exists);
    }

    public function test_gets_default_container_from_pod(): void
    {
        // Mock HTTP response for pod details
        Http::fake([
            '*/api/v1/namespaces/*/pods/test-pod' => Http::response([
                'metadata' => ['name' => 'test-pod'],
                'spec' => [
                    'containers' => [
                        ['name' => 'first-container'],
                        ['name' => 'second-container'],
                    ],
                ],
            ], 200),
        ]);

        $terminalService = new PodTerminalService(new JwtService);

        $reflection = new \ReflectionClass($terminalService);
        $method = $reflection->getMethod('getDefaultContainer');
        $method->setAccessible(true);

        $container = $method->invokeArgs($terminalService, [$this->workspace, 'test-pod']);
        $this->assertEquals('first-container', $container);
    }

    public function test_handles_different_cluster_auth_types(): void
    {
        // Test certificate auth
        $certCluster = Cluster::factory()->create([
            'auth_type' => 'certificate',
            'client_certificate_data' => base64_encode('cert-data'),
            'client_key_data' => base64_encode('key-data'),
            'server_url' => 'https://test.k8s.cluster',
        ]);

        // Test username/password auth
        $basicCluster = Cluster::factory()->create([
            'auth_type' => 'username_password',
            'username' => 'testuser',
            'password' => 'testpass',
            'server_url' => 'https://test.k8s.cluster',
        ]);

        $this->assertEquals('certificate', $certCluster->auth_type);
        $this->assertEquals('username_password', $basicCluster->auth_type);
    }

    protected function tearDown(): void
    {
        // 清理生成的 JWT 密钥文件
        @unlink(config('websocket.jwt.private_key_path'));
        @unlink(config('websocket.jwt.public_key_path'));

        parent::tearDown();
    }
}
