<?php

namespace Tests\Feature;

use App\ClusterLabel;
use App\Jobs\ProcessTaskJob;
use App\Models\Cluster;
use App\Models\Task;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WorkspaceNamespaceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->token()->create();
    }

    public function test_workspace_creation_dispatches_namespace_creation_task(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_PENDING,
        ]);

        // 验证任务已创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_CREATE,
            'status' => Task::STATUS_PENDING,
        ]);

        // 验证 ProcessTaskJob 已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_CREATE;
        });
    }

    public function test_workspace_deletion_dispatches_namespace_deletion_task(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 清空之前的队列记录
        Queue::fake();

        // 使用新的删除流程：手动启动删除任务
        $workspace->update(['status' => Workspace::STATUS_DELETING]);
        $workspace->startDeletionTask();

        // 验证删除任务已创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_DELETE,
            'status' => Task::STATUS_PENDING,
        ]);

        // 验证 ProcessTaskJob 已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_DELETE;
        });
    }

    public function test_workspace_retry_namespace_creation(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_FAILED,
            'suspension_reason' => 'Namespace 创建失败: Test error',
        ]);

        // 清空之前的队列记录
        Queue::fake();

        $workspace->retryCreateNamespace();

        // 验证状态已更新
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_PENDING, $workspace->status);
        $this->assertNull($workspace->suspension_reason);

        // 验证任务已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_CREATE;
        });
    }

    public function test_namespace_service_build_correct_resource(): void
    {
        // 禁用模型事件，避免触发队列任务
        Workspace::withoutEvents(function () {
            $workspace = Workspace::create([
                'user_id' => $this->user->id,
                'cluster_id' => $this->cluster->id,
                'name' => 'test-workspace',
                'namespace' => 'ns-test123',
                'status' => Workspace::STATUS_PENDING,
            ]);

            // 我们只测试 buildDefaultLabels 方法，不测试实际的 K8s API 调用
            $labels = $workspace->buildDefaultLabels($workspace);

            $this->assertArrayHasKey(ClusterLabel::WORKSPACE->value, $labels);
            $this->assertArrayHasKey(ClusterLabel::USER->value, $labels);
            $this->assertArrayHasKey(ClusterLabel::MANAGED_BY->value, $labels);
            $this->assertArrayHasKey(ClusterLabel::PLATFORM->value, $labels);
            $this->assertArrayHasKey(ClusterLabel::RESOURCE_ID->value, $labels);

            $this->assertEquals('test-workspace', $labels[ClusterLabel::WORKSPACE->value]);
            // User name is sanitized for Kubernetes labels (spaces become hyphens)
            $expectedUserName = str_replace(' ', '-', $this->user->name);
            $this->assertEquals($expectedUserName, $labels[ClusterLabel::USER->value]);
            $this->assertEquals($workspace->id, $labels[ClusterLabel::RESOURCE_ID->value]);
        });
    }

    public function test_workspace_service_creates_workspace_with_transaction(): void
    {
        Queue::fake();

        $workspaceService = app(\App\Service\WorkspaceService::class);

        $workspace = $workspaceService->createWorkspace([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
        ]);

        // 验证工作空间已创建
        $this->assertDatabaseHas('workspaces', [
            'id' => $workspace->id,
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'status' => Workspace::STATUS_PENDING,
        ]);

        // 验证 namespace 格式正确
        $this->assertMatchesRegularExpression('/^ns-[a-z0-9]{8}$/', $workspace->namespace);

        // 验证队列任务已派发
        Queue::assertPushed(ProcessTaskJob::class);
    }

    public function test_workspace_service_starts_workspace_deletion_with_transaction(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 清空之前的队列记录
        Queue::fake();

        $workspaceService = app(\App\Service\WorkspaceService::class);
        $workspaceService->startWorkspaceDeletion($workspace);

        // 验证工作空间状态变为删除中
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_DELETING, $workspace->status);

        // 验证工作空间记录还存在（未被立即删除）
        $this->assertDatabaseHas('workspaces', [
            'id' => $workspace->id,
            'status' => Workspace::STATUS_DELETING,
        ]);

        // 验证删除任务已创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_DELETE,
            'status' => Task::STATUS_PENDING,
        ]);

        // 验证队列任务已派发
        Queue::assertPushed(ProcessTaskJob::class);
    }
}
