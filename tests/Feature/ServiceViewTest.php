<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceViewTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户和工作空间
        $this->user = User::factory()->create();
        $cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $cluster->id,
        ]);

        $this->user->update(['current_workspace_id' => $this->workspace->id]);
    }

    public function test_can_access_services_index_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('services.index'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('Services/Index')
                ->has('workspace')
            );
    }

    public function test_can_access_services_create_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('services.create'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('Services/Create')
                ->has('workspace')
            );
    }

    public function test_services_index_handles_api_errors_gracefully()
    {
        // 模拟 API 错误情况
        $response = $this->actingAs($this->user)
            ->get(route('services.index'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => $page
                ->component('Services/Index')
                ->has('workspace')
            );
    }

    public function test_requires_authentication_for_services_pages()
    {
        $response = $this->get(route('services.index'));
        $response->assertRedirect(route('login'));

        $response = $this->get(route('services.create'));
        $response->assertRedirect(route('login'));
    }

    public function test_requires_workspace_for_services_pages()
    {
        $userWithoutWorkspace = User::factory()->create();

        $response = $this->actingAs($userWithoutWorkspace)
            ->get(route('services.index'));

        $response->assertRedirect(route('workspaces.index'));
    }
}
