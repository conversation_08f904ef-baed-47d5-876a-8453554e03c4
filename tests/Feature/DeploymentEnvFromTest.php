<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\DeploymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class DeploymentEnvFromTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);
    }

    /** @test */
    public function it_handles_env_from_configmap_with_empty_key_correctly()
    {
        // Mock Kubernetes API responses
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'test-configmap',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildContainers');
        $method->setAccessible(true);

        $containers = $method->invoke($deploymentService, $data['containers']);

        // Check that envFrom is used instead of env for whole ConfigMap reference
        $this->assertArrayHasKey('envFrom', $containers[0]);
        $this->assertCount(1, $containers[0]['envFrom']);
        $this->assertEquals([
            'configMapRef' => [
                'name' => 'test-configmap',
            ],
        ], $containers[0]['envFrom'][0]);

        // env should not contain the configmap reference
        if (isset($containers[0]['env'])) {
            foreach ($containers[0]['env'] as $envVar) {
                $this->assertArrayNotHasKey('configMapRef', $envVar['valueFrom'] ?? []);
            }
        }
    }

    /** @test */
    public function it_handles_env_from_configmap_with_specific_key_correctly()
    {
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'test-configmap',
                            'key' => 'DATABASE_URL',
                            'env_name' => 'DB_URL',
                        ],
                    ],
                ],
            ],
        ];

        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildContainers');
        $method->setAccessible(true);

        $containers = $method->invoke($deploymentService, $data['containers']);

        // Check that env is used for specific key reference
        $this->assertArrayHasKey('env', $containers[0]);
        $this->assertCount(1, $containers[0]['env']);
        $this->assertEquals([
            'name' => 'DB_URL',
            'valueFrom' => [
                'configMapKeyRef' => [
                    'name' => 'test-configmap',
                    'key' => 'DATABASE_URL',
                ],
            ],
        ], $containers[0]['env'][0]);
    }

    /** @test */
    public function it_skips_invalid_env_from_configmap_entries()
    {
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => '', // Empty configmap name should be skipped
                            'key' => '',
                            'env_name' => '',
                        ],
                        [
                            'configmap_name' => 'valid-configmap',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildContainers');
        $method->setAccessible(true);

        $containers = $method->invoke($deploymentService, $data['containers']);

        // Should only have one envFrom entry (the valid one)
        $this->assertArrayHasKey('envFrom', $containers[0]);
        $this->assertCount(1, $containers[0]['envFrom']);
        $this->assertEquals([
            'configMapRef' => [
                'name' => 'valid-configmap',
            ],
        ], $containers[0]['envFrom'][0]);
    }

    /** @test */
    public function it_handles_env_from_secret_correctly()
    {
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'env_from_secret' => [
                        [
                            'secret_name' => 'test-secret',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildContainers');
        $method->setAccessible(true);

        $containers = $method->invoke($deploymentService, $data['containers']);

        // Check that envFrom is used for whole Secret reference
        $this->assertArrayHasKey('envFrom', $containers[0]);
        $this->assertCount(1, $containers[0]['envFrom']);
        $this->assertEquals([
            'secretRef' => [
                'name' => 'test-secret',
            ],
        ], $containers[0]['envFrom'][0]);
    }

    /** @test */
    public function it_combines_env_and_env_from_correctly()
    {
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'env' => [
                        [
                            'name' => 'DIRECT_ENV',
                            'value' => 'direct_value',
                        ],
                    ],
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'whole-configmap',
                            'key' => '',
                            'env_name' => '',
                        ],
                        [
                            'configmap_name' => 'specific-configmap',
                            'key' => 'SPECIFIC_KEY',
                            'env_name' => 'SPECIFIC_ENV',
                        ],
                    ],
                    'env_from_secret' => [
                        [
                            'secret_name' => 'whole-secret',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildContainers');
        $method->setAccessible(true);

        $containers = $method->invoke($deploymentService, $data['containers']);

        // Check env array
        $this->assertArrayHasKey('env', $containers[0]);
        $this->assertCount(2, $containers[0]['env']); // Direct env + specific configmap key

        // Check envFrom array
        $this->assertArrayHasKey('envFrom', $containers[0]);
        $this->assertCount(2, $containers[0]['envFrom']); // Whole configmap + whole secret

        // Verify specific entries
        $envNames = array_column($containers[0]['env'], 'name');
        $this->assertContains('DIRECT_ENV', $envNames);
        $this->assertContains('SPECIFIC_ENV', $envNames);
    }

    public function test_env_from_configmap_with_empty_key_should_use_envfrom()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-env-from',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'wordpress',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);
        $payload = $method->invokeArgs($deploymentService, [$data]);

        // 检查容器配置
        $container = $payload['spec']['template']['spec']['containers'][0];

        // 当 key 为空时，应该使用 envFrom 而不是 env
        $this->assertArrayHasKey('envFrom', $container);
        $this->assertCount(1, $container['envFrom']);
        $this->assertEquals('wordpress', $container['envFrom'][0]['configMapRef']['name']);

        // 不应该有通过 env 的 configMapKeyRef
        if (isset($container['env'])) {
            foreach ($container['env'] as $envVar) {
                $this->assertArrayNotHasKey('configMapKeyRef', $envVar['valueFrom'] ?? []);
            }
        }
    }

    public function test_env_from_configmap_with_specific_key_should_use_env()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-env-from-key',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'my-config',
                            'key' => 'database_url',
                            'env_name' => 'DATABASE_URL',
                        ],
                    ],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);
        $payload = $method->invokeArgs($deploymentService, [$data]);

        // 检查容器配置
        $container = $payload['spec']['template']['spec']['containers'][0];

        // 当 key 不为空时，应该使用 env 而不是 envFrom
        $this->assertArrayHasKey('env', $container);
        $this->assertCount(1, $container['env']);

        $envVar = $container['env'][0];
        $this->assertEquals('DATABASE_URL', $envVar['name']);
        $this->assertEquals('my-config', $envVar['valueFrom']['configMapKeyRef']['name']);
        $this->assertEquals('database_url', $envVar['valueFrom']['configMapKeyRef']['key']);
    }

    public function test_env_from_configmap_mixed_configurations()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-env-from-mixed',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        // 整个 ConfigMap
                        [
                            'configmap_name' => 'app-config',
                            'key' => '',
                            'env_name' => '',
                        ],
                        // 特定键
                        [
                            'configmap_name' => 'db-config',
                            'key' => 'host',
                            'env_name' => 'DB_HOST',
                        ],
                    ],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);
        $payload = $method->invokeArgs($deploymentService, [$data]);

        // 检查容器配置
        $container = $payload['spec']['template']['spec']['containers'][0];

        // 应该同时有 envFrom 和 env
        $this->assertArrayHasKey('envFrom', $container);
        $this->assertArrayHasKey('env', $container);

        // envFrom 应该包含整个 ConfigMap
        $this->assertCount(1, $container['envFrom']);
        $this->assertEquals('app-config', $container['envFrom'][0]['configMapRef']['name']);

        // env 应该包含特定键
        $this->assertCount(1, $container['env']);
        $envVar = $container['env'][0];
        $this->assertEquals('DB_HOST', $envVar['name']);
        $this->assertEquals('db-config', $envVar['valueFrom']['configMapKeyRef']['name']);
        $this->assertEquals('host', $envVar['valueFrom']['configMapKeyRef']['key']);
    }

    public function test_env_from_configmap_should_skip_empty_configmap_name()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-env-from-skip',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'nginx:latest',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => '',
                            'key' => 'some-key',
                            'env_name' => 'SOME_VAR',
                        ],
                        [
                            'configmap_name' => 'valid-config',
                            'key' => 'valid-key',
                            'env_name' => 'VALID_VAR',
                        ],
                    ],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);
        $payload = $method->invokeArgs($deploymentService, [$data]);

        // 检查容器配置
        $container = $payload['spec']['template']['spec']['containers'][0];

        // 应该只有一个有效的环境变量
        $this->assertArrayHasKey('env', $container);
        $this->assertCount(1, $container['env']);

        $envVar = $container['env'][0];
        $this->assertEquals('VALID_VAR', $envVar['name']);
        $this->assertEquals('valid-config', $envVar['valueFrom']['configMapKeyRef']['name']);
    }

    public function test_env_from_secret_with_empty_key_should_use_envfrom()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $data = [
            'name' => 'test-secret-env-from',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'nginx:latest',
                    'env_from_secret' => [
                        [
                            'secret_name' => 'app-secrets',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);
        $payload = $method->invokeArgs($deploymentService, [$data]);

        // 检查容器配置
        $container = $payload['spec']['template']['spec']['containers'][0];

        // 当 key 为空时，应该使用 envFrom 而不是 env
        $this->assertArrayHasKey('envFrom', $container);
        $this->assertCount(1, $container['envFrom']);
        $this->assertEquals('app-secrets', $container['envFrom'][0]['secretRef']['name']);
    }
}
