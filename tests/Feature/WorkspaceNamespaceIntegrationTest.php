<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceNamespaceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed();
    }

    public function test_workspace_creation_automatically_creates_kubernetes_namespace()
    {
        $user = User::factory()->create();

        // 使用真实的集群配置
        $cluster = Cluster::first();
        if (! $cluster) {
            $this->markTestSkipped('No real cluster available for testing');
        }

        // 创建工作空间
        $workspace = Workspace::create([
            'user_id' => $user->id,
            'cluster_id' => $cluster->id,
            'name' => 'test-integration-workspace',
            'namespace' => 'ns-integration-test',
            'status' => 'pending',
        ]);

        // 刷新模型以获取最新状态
        $workspace->refresh();

        // 验证工作空间状态
        $this->assertEquals('active', $workspace->status);
        $this->assertNull($workspace->suspension_reason);

        // 验证 namespace 在 Kubernetes 中存在
        try {
            $namespaceService = app(\App\Service\NamespaceService::class);
            $exists = $namespaceService->namespaceExists($workspace);
            $this->assertTrue($exists, 'Namespace should exist in Kubernetes');
        } catch (\Exception $e) {
            // 如果 Kubernetes 不可用，跳过这个断言
            $this->markTestSkipped('Kubernetes cluster not available: '.$e->getMessage());
        }
    }

    public function test_can_create_secret_in_workspace_namespace()
    {
        $user = User::factory()->create();

        // 使用真实的集群配置
        $cluster = Cluster::first();
        if (! $cluster) {
            $this->markTestSkipped('No real cluster available for testing');
        }

        // 创建工作空间
        $workspace = Workspace::create([
            'user_id' => $user->id,
            'cluster_id' => $cluster->id,
            'name' => 'test-secret-workspace',
            'namespace' => 'ns-secret-test',
            'status' => 'pending',
        ]);

        // 设置为当前工作空间
        $user->setCurrentWorkspace($workspace);

        // 刷新模型
        $workspace->refresh();

        // 验证工作空间是 active 状态
        $this->assertEquals('active', $workspace->status);

        // 尝试创建 Secret
        $response = $this->actingAs($user)
            ->postJson("/api/workspaces/{$workspace->id}/secrets/generic", [
                'name' => 'test-integration-secret',
                'data' => [
                    'username' => 'testuser',
                    'password' => 'testpass123',
                ],
            ]);

        if ($workspace->status === 'active') {
            // 如果工作空间是 active，Secret 创建应该成功
            $response->assertStatus(201);
            $response->assertJsonStructure([
                'name',
                'type',
                'data_keys',
                'data_count',
                'created_at',
            ]);
        } else {
            // 如果工作空间创建失败，应该返回相应错误
            $response->assertStatus(422);
        }
    }

    public function test_workspace_deletion_removes_kubernetes_namespace()
    {
        $user = User::factory()->create();

        // 使用真实的集群配置
        $cluster = Cluster::first();
        if (! $cluster) {
            $this->markTestSkipped('No real cluster available for testing');
        }

        // 创建工作空间
        $workspace = Workspace::create([
            'user_id' => $user->id,
            'cluster_id' => $cluster->id,
            'name' => 'test-deletion-workspace',
            'namespace' => 'ns-deletion-test',
            'status' => 'pending',
        ]);

        $namespace = $workspace->namespace;

        // 刷新模型
        $workspace->refresh();

        // 删除工作空间
        $workspace->delete();

        // 验证 namespace 在 Kubernetes 中被删除
        try {
            $namespaceService = app(\App\Service\NamespaceService::class);

            // 等待一下删除操作完成
            sleep(2);

            // 检查 namespace 是否仍然存在
            $exists = $namespaceService->namespaceExists($workspace);
            $this->assertFalse($exists, 'Namespace should be deleted from Kubernetes');
        } catch (\Exception $e) {
            // 如果 Kubernetes 不可用，跳过这个断言
            $this->markTestSkipped('Kubernetes cluster not available: '.$e->getMessage());
        }
    }
}
