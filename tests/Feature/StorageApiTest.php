<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StorageApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed();
    }

    public function test_can_create_storage_via_api()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-storage',
                'size' => 1024,
            ]);

        if ($workspace->status === 'active') {
            $response->assertStatus(201);
            $response->assertJsonStructure([
                'name',
                'size',
                'storage_class',
                'access_modes',
                'status',
                'formatted_size',
                'created_at',
            ]);
        } else {
            $response->assertStatus(422);
        }
    }

    public function test_validates_storage_size_minimum()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-storage-small',
                'size' => 256, // 小于最小值 512
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);
    }

    public function test_validates_storage_size_multiple_of_512()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-storage-invalid',
                'size' => 700, // 不是 512 的倍数
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);
    }

    public function test_validates_storage_name_format()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'Invalid_Name!', // 包含无效字符
                'size' => 512,
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_create_storage_without_workspace()
    {
        $user = User::first();
        // 清除当前工作空间设置
        $user->current_workspace_id = null;
        $user->save();

        $response = $this->actingAs($user)
            ->postJson('/api/storages', [
                'name' => 'test-storage',
                'size' => 512,
            ]);

        $response->assertStatus(422); // 中间件返回 422 状态码
    }

    public function test_can_get_storages_list()
    {
        $user = User::first();
        $workspace = Workspace::first();

        if (! $workspace) {
            $this->markTestSkipped('No workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        $response = $this->actingAs($user)
            ->getJson('/api/storages');

        if ($workspace->status === 'active') {
            $response->assertStatus(200);
            $response->assertJsonStructure([
                '*' => [
                    'name',
                    'size',
                    'storage_class',
                    'access_modes',
                    'status',
                    'formatted_size',
                    'created_at',
                ],
            ]);
        } else {
            $response->assertStatus(500);
        }
    }
}
