<?php

namespace Tests\Feature;

use App\Models\Task;
use App\Models\User;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceAsyncDeletionTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected WorkspaceService $workspaceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);
        $this->workspaceService = app(WorkspaceService::class);
    }

    public function test_workspace_deletion_task_retry_mechanism()
    {
        // 启动删除任务
        $this->workspaceService->startWorkspaceDeletion($this->workspace);

        // 验证工作空间状态变为删除中
        $this->workspace->refresh();
        $this->assertEquals(Workspace::STATUS_DELETING, $this->workspace->status);

        // 获取创建的删除任务
        $task = $this->workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_DELETE)->first();
        $this->assertNotNull($task);
        $this->assertEquals(Task::STATUS_PENDING, $task->status);
        $this->assertEquals(0, $task->max_attempts); // 无限重试

        // 模拟任务处理（第一次会因为 namespace 仍然存在而需要重试）
        $this->workspace->handleTask($task);

        // 刷新任务状态
        $task->refresh();

        // 任务应该被标记为失败，但由于支持重试，应该会重新调度
        $this->assertEquals(Task::STATUS_FAILED, $task->status);
        $this->assertStringContains('需要重试检查状态', $task->error_message);
        $this->assertEquals(1, $task->attempts);

        // 验证任务可以重试
        $this->assertTrue($task->canRetry());
    }

    public function test_workspace_deletion_completes_when_namespace_not_found()
    {
        // 启动删除任务
        $this->workspaceService->startWorkspaceDeletion($this->workspace);

        // 获取创建的删除任务
        $task = $this->workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_DELETE)->first();

        // 模拟 namespace 已经不存在的情况
        // 这需要 mock WorkspaceService 的 checkKubernetesNamespaceStatus 方法
        $mockWorkspaceService = $this->createMock(WorkspaceService::class);
        $mockWorkspaceService->method('checkKubernetesNamespaceStatus')
            ->willReturn('not_found');

        app()->instance(WorkspaceService::class, $mockWorkspaceService);

        // 处理任务
        $this->workspace->handleTask($task);

        // 刷新任务状态
        $task->refresh();

        // 任务应该完成
        $this->assertEquals(Task::STATUS_COMPLETED, $task->status);
        $this->assertArrayHasKey('namespace_status', $task->result);
        $this->assertEquals('not_found', $task->result['namespace_status']);

        // 工作空间应该被删除
        $this->assertDatabaseMissing('workspaces', ['id' => $this->workspace->id]);
    }

    public function test_workspace_deletion_retries_when_namespace_terminating()
    {
        // 启动删除任务
        $this->workspaceService->startWorkspaceDeletion($this->workspace);

        // 获取创建的删除任务
        $task = $this->workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_DELETE)->first();

        // 模拟 namespace 正在删除中的情况
        $mockWorkspaceService = $this->createMock(WorkspaceService::class);
        $mockWorkspaceService->method('checkKubernetesNamespaceStatus')
            ->willReturn('terminating');

        app()->instance(WorkspaceService::class, $mockWorkspaceService);

        // 处理任务
        $this->workspace->handleTask($task);

        // 刷新任务状态
        $task->refresh();

        // 任务应该失败但可以重试
        $this->assertEquals(Task::STATUS_FAILED, $task->status);
        $this->assertStringContains('需要重试检查状态', $task->error_message);
        $this->assertTrue($task->canRetry());

        // 工作空间应该仍然存在
        $this->assertDatabaseHas('workspaces', ['id' => $this->workspace->id]);
    }
}
