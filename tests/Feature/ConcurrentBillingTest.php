<?php

namespace Tests\Feature;

use App\Models\BillingRecord;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\User;
use App\Models\Workspace;
use App\Service\BalanceService;
use App\Service\BillingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ConcurrentBillingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    private ClusterPricing $pricing;

    private Workspace $workspace;

    private BillingService $billingService;

    private BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['current_balance' => 100.0]);
        $this->cluster = Cluster::factory()->create(['name' => 'test-cluster']);

        $this->pricing = ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => 50.0000,
            'cpu_price_per_core' => 30.0000,
            'storage_price_per_gb' => 10.0000,
            'loadbalancer_price_per_service' => 100.0000,
            'billing_enabled' => true,
            'description' => '并发测试定价',
        ]);

        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => 'active',
            'name' => 'concurrent-test-workspace',
            'namespace' => 'concurrent-test-ws',
        ]);

        $this->billingService = new BillingService(
            app(\App\Service\ResourceCollectionService::class),
            app(BalanceService::class)
        );

        $this->balanceService = app(BalanceService::class);

        // Mock HTTP responses for K8s API calls
        Http::fake([
            '*/api/v1/namespaces/*/pods' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/persistentvolumeclaims' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/services' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/deployments' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/statefulsets' => Http::response(['items' => []]),
        ]);
    }

    /**
     * @test
     * 测试并发余额扣除的原子性
     */
    public function concurrent_balance_deduction_is_atomic()
    {
        Event::fake();

        // 清空用户余额，设置较少的余额用于测试并发
        $this->user->update(['current_balance' => 0]);
        $this->balanceService->addBalance($this->user, 10.0, 'manual', null, ['remark' => '并发测试']);

        $initialBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(10.0, $initialBalance);

        // 创建多个计费记录，总额超过余额
        $records = [];
        for ($i = 0; $i < 5; $i++) {
            $records[] = BillingRecord::create([
                'workspace_id' => $this->workspace->id,
                'cluster_id' => $this->cluster->id,
                'user_id' => $this->user->id,
                'billing_start_at' => now()->subMinutes($i + 1),
                'billing_end_at' => now()->subMinutes($i),
                'resource_usage' => ['memory_mi' => 512],
                'memory_cost' => '2.50000000',
                'cpu_cost' => '0.00000000',
                'storage_cost' => '0.00000000',
                'loadbalancer_cost' => '0.00000000',
                'total_cost' => '2.50000000',
                'status' => BillingRecord::STATUS_PENDING,
            ]);
        }

        // 模拟并发扣费（总共12.5，但用户只有10.0余额）
        $results = [];
        $exceptions = [];

        // 并发执行扣费操作
        DB::transaction(function () use ($records, &$results, &$exceptions) {
            foreach ($records as $record) {
                try {
                    $result = $this->balanceService->deductBalance($this->user, 2.5, "并发测试#{$record->id}");
                    $results[] = $result;
                    $record->markAsCharged();
                } catch (\Exception $e) {
                    $exceptions[] = $e->getMessage();
                    $record->markAsFailed();
                }
            }
        });

        // 验证结果
        $successfulCharges = count($results);
        $failedCharges = count($exceptions);

        // 应该有4次成功（10.0 / 2.5 = 4），1次失败
        $this->assertEquals(4, $successfulCharges);
        $this->assertEquals(1, $failedCharges);

        // 验证最终余额为0
        $finalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(0.0, $finalBalance);

        // 验证至少有一个失败的异常包含"余额不足"
        $hasInsufficientBalanceError = false;
        foreach ($exceptions as $exception) {
            if (str_contains($exception, '余额不足')) {
                $hasInsufficientBalanceError = true;
                break;
            }
        }
        $this->assertTrue($hasInsufficientBalanceError);
    }

    /**
     * @test
     * 测试极小数值的精度处理
     */
    public function extreme_precision_billing_works()
    {
        Event::fake();

        // 清空用户余额并重新设置
        $this->user->update(['current_balance' => 0]);
        $this->balanceService->addBalance($this->user, 1.0, 'manual', null, ['remark' => '极小数值测试']);

        // 设置极小的定价
        $this->pricing->update([
            'memory_price_per_gb' => 0.0001,
            'cpu_price_per_core' => 0.0001,
        ]);

        // 创建极小的计费（使用更合理的精度）
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1, 'cpu_m' => 1],
            'memory_cost' => '0.00001000', // 极小但合理的数值
            'cpu_cost' => '0.00000500',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '0.00001500',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $initialBalance = $this->user->fresh()->current_balance;

        // 使用BCMath精确计算小数值
        $smallAmount = bcadd('0.00001000', '0.00000500', 8); // 精确计算0.000015

        // 扣费应该成功
        $this->balanceService->deductBalance($this->user, (float) $smallAmount, '极小数值计费');
        $record->markAsCharged();

        $finalBalance = $this->user->fresh()->current_balance;

        // 使用BCMath验证精确计算
        $expectedBalance = bcsub((string) $initialBalance, $smallAmount, 8);
        $this->assertEquals($expectedBalance, (string) $finalBalance);
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
    }

    /**
     * @test
     * 测试大数值的处理
     */
    public function large_amount_billing_works()
    {
        Event::fake();

        // 给用户适中的大额余额（避免超出数据库精度）
        $this->user->update(['current_balance' => 0]);
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '大额测试']);

        // 创建较大的计费（在数据库精度范围内）
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 2048, 'cpu_m' => 2000], // 2GB内存，2核CPU
            'memory_cost' => '25.00000000',
            'cpu_cost' => '15.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '40.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $initialBalance = $this->user->fresh()->current_balance;

        // 扣费应该成功
        $this->balanceService->deductBalance($this->user, 40.0, '大额计费');
        $record->markAsCharged();

        $finalBalance = $this->user->fresh()->current_balance;

        // 验证大数值计算正确
        $expectedBalance = bcsub((string) $initialBalance, '40.00000000', 8);
        $this->assertEquals($expectedBalance, (string) $finalBalance);
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
    }

    /**
     * @test
     * 测试余额刚好等于计费金额的边界情况
     */
    public function exact_balance_match_billing()
    {
        Event::fake();

        // 设置用户余额刚好等于计费金额
        $this->user->update(['current_balance' => 0]);
        $this->balanceService->addBalance($this->user, 25.55, 'manual', null, ['remark' => '精确匹配测试']);

        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1024],
            'memory_cost' => '25.55000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '25.55000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 扣费应该成功
        $this->balanceService->deductBalance($this->user, 25.55, '精确匹配计费');
        $record->markAsCharged();

        // 验证余额刚好为0
        $finalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals('0.00000000', (string) $finalBalance);
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);

        // 再尝试一次计费应该失败
        $record2 = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '0.01000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '0.01000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        try {
            $this->balanceService->deductBalance($this->user, 0.01, '零余额后计费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        $record2->markAsFailed();
        $this->assertEquals(BillingRecord::STATUS_FAILED, $record2->fresh()->status);
    }

    /**
     * @test
     * 测试多用户并发计费
     */
    public function multiple_users_concurrent_billing()
    {
        Event::fake();

        // 创建多个用户
        $users = [];
        $workspaces = [];
        for ($i = 0; $i < 3; $i++) {
            $user = User::factory()->create(['current_balance' => 0]);
            $this->balanceService->addBalance($user, 50.0, 'manual', null, ['remark' => "用户{$i}初始余额"]);

            $workspace = Workspace::factory()->create([
                'user_id' => $user->id,
                'cluster_id' => $this->cluster->id,
                'status' => 'active',
                'name' => "user-{$i}-workspace",
                'namespace' => "user-{$i}-ws",
            ]);

            $users[] = $user;
            $workspaces[] = $workspace;
        }

        // 为每个用户创建计费记录
        $records = [];
        foreach ($users as $index => $user) {
            $record = BillingRecord::create([
                'workspace_id' => $workspaces[$index]->id,
                'cluster_id' => $this->cluster->id,
                'user_id' => $user->id,
                'billing_start_at' => now()->subMinute(),
                'billing_end_at' => now(),
                'resource_usage' => ['memory_mi' => 1024],
                'memory_cost' => '20.00000000',
                'cpu_cost' => '0.00000000',
                'storage_cost' => '0.00000000',
                'loadbalancer_cost' => '0.00000000',
                'total_cost' => '20.00000000',
                'status' => BillingRecord::STATUS_PENDING,
            ]);
            $records[] = $record;
        }

        // 并发处理所有用户的计费
        DB::transaction(function () use ($users, $records) {
            foreach ($users as $index => $user) {
                $this->balanceService->deductBalance($user, 20.0, "多用户并发计费#{$index}");
                $records[$index]->markAsCharged();
            }
        });

        // 验证所有用户的余额都正确扣除
        foreach ($users as $user) {
            $finalBalance = $user->fresh()->current_balance;
            $this->assertEquals(30.0, $finalBalance); // 50 - 20 = 30
        }

        // 验证所有计费记录都成功
        foreach ($records as $record) {
            $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
        }
    }

    /**
     * @test
     * 测试数据库连接中断时的处理
     */
    public function database_connection_failure_handling()
    {
        Event::fake();

        // 创建计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '10.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '10.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $initialBalance = $this->user->fresh()->current_balance;

        // 模拟事务中的失败
        $transactionFailed = false;
        try {
            DB::transaction(function () {
                $this->balanceService->deductBalance($this->user, 10.0, '数据库故障测试');
                // 模拟数据库操作失败
                throw new \Exception('模拟数据库连接中断');
            });
        } catch (\Exception $e) {
            $transactionFailed = true;
            // 这里可能捕获到的是余额扣除失败的异常，因为事务会回滚
            $this->assertTrue(
                str_contains($e->getMessage(), '模拟数据库连接中断') ||
                str_contains($e->getMessage(), '余额扣除失败')
            );
        }

        $this->assertTrue($transactionFailed, '事务应该失败');

        // 验证事务回滚，余额没有变化
        $finalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals($initialBalance, $finalBalance);

        // 验证计费记录状态没有改变
        $this->assertEquals(BillingRecord::STATUS_PENDING, $record->fresh()->status);
    }

    /**
     * @test
     * 测试TopUpRecord余额分片扣除
     */
    public function topup_record_fragmented_deduction()
    {
        Event::fake();

        // 清空用户余额
        $this->user->update(['current_balance' => 0]);

        // 创建多个小额充值记录
        $this->balanceService->addBalance($this->user, 5.0, 'manual', null, ['remark' => '第一次充值']);
        $this->balanceService->addBalance($this->user, 3.0, 'manual', null, ['remark' => '第二次充值']);
        $this->balanceService->addBalance($this->user, 7.0, 'manual', null, ['remark' => '第三次充值']);

        $totalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(15.0, $totalBalance);

        // 创建一个需要跨多个TopUpRecord扣除的计费
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1024],
            'memory_cost' => '12.50000000', // 需要从多个TopUpRecord中扣除
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '12.50000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 扣费应该成功
        $this->balanceService->deductBalance($this->user, 12.5, '分片扣除测试');
        $record->markAsCharged();

        $finalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(2.5, $finalBalance); // 15 - 12.5 = 2.5

        // 验证TopUpRecord的剩余金额分布
        $topupRecords = $this->user->availableTopUpRecords()->get();

        // 应该有一个记录还有剩余
        $totalRemaining = $topupRecords->sum('remaining_amount');
        $this->assertEquals(2.5, $totalRemaining);

        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
    }
}
