<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspacePricingDisplayTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
        ]);
    }

    /** @test */
    public function it_displays_cluster_pricing_in_workspace_create_page()
    {
        // 创建集群定价配置
        $pricing = ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => '10.0000',
            'cpu_price_per_core' => '20.0000',
            'storage_price_per_gb' => '5.0000',
            'loadbalancer_price_per_service' => '50.0000',
            'billing_enabled' => true,
            'description' => '测试定价策略',
        ]);

        // 访问创建工作空间页面
        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证页面数据包含集群和定价信息
        $response->assertInertia(fn ($page) => $page->component('Workspaces/Create')
            ->has('clusters.0', fn ($cluster) => $cluster->where('id', $this->cluster->id)
                ->where('name', 'test-cluster')
                ->has('pricing', fn ($pricing) => $pricing->where('billing_enabled', true)
                    ->where('memory_price_per_gb', '10.0000')
                    ->where('cpu_price_per_core', '20.0000')
                    ->where('storage_price_per_gb', '5.0000')
                    ->where('loadbalancer_price_per_service', '50.0000')
                    ->where('description', '测试定价策略')
                    ->etc()
                )
            )
        );
    }

    /** @test */
    public function it_displays_no_pricing_message_when_billing_disabled()
    {
        // 创建禁用计费的集群定价配置
        ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => '10.0000',
            'cpu_price_per_core' => '20.0000',
            'storage_price_per_gb' => '5.0000',
            'loadbalancer_price_per_service' => '50.0000',
            'billing_enabled' => false,
        ]);

        // 访问创建工作空间页面
        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证页面数据包含集群和定价信息，但计费被禁用
        $clusters = $response->getOriginalContent()['page']['props']['clusters'];
        $this->assertCount(1, $clusters);
        $this->assertEquals($this->cluster->id, $clusters[0]['id']);
        $this->assertNotNull($clusters[0]['pricing']);
        $this->assertFalse($clusters[0]['pricing']['billing_enabled']);
    }

    /** @test */
    public function it_displays_no_pricing_message_when_no_pricing_configured()
    {
        // 不创建定价配置

        // 访问创建工作空间页面
        $response = $this->actingAs($this->user)
            ->get(route('workspaces.create'));

        $response->assertOk();

        // 验证页面数据包含集群但没有定价配置
        $clusters = $response->getOriginalContent()['page']['props']['clusters'];
        $this->assertCount(1, $clusters);
        $this->assertEquals($this->cluster->id, $clusters[0]['id']);
        $this->assertNull($clusters[0]['pricing']);
    }

    /** @test */
    public function pricing_calculation_is_correct()
    {
        $pricing = ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => '10.0000',
            'cpu_price_per_core' => '20.0000',
            'storage_price_per_gb' => '5.0000',
            'loadbalancer_price_per_service' => '50.0000',
            'billing_enabled' => true,
        ]);

        // 测试价格计算方法
        $memoryPricePerMinute = $pricing->calculateMemoryPricePerMinute(1024); // 1GB
        $cpuPricePerMinute = $pricing->calculateCpuPricePerMinute(1000); // 1 core
        $storagePricePerMinute = $pricing->calculateStoragePricePerMinute(1); // 1GB
        $lbPricePerMinute = $pricing->calculateLoadBalancerPricePerMinute(1); // 1 service

        // 验证每分钟价格计算正确
        // 月价格 / 43200分钟 = 每分钟价格
        $this->assertEquals('0.00023148', $memoryPricePerMinute); // 10/43200
        $this->assertEquals('0.00046296', $cpuPricePerMinute); // 20/43200
        $this->assertEquals('0.00011574', $storagePricePerMinute); // 5/43200
        $this->assertEquals('0.00115740', $lbPricePerMinute); // 50/43200

        // 验证每小时价格（每分钟价格 * 60）
        $memoryHourly = bcmul($memoryPricePerMinute, '60', 8);
        $cpuHourly = bcmul($cpuPricePerMinute, '60', 8);
        $storageHourly = bcmul($storagePricePerMinute, '60', 8);
        $lbHourly = bcmul($lbPricePerMinute, '60', 8);

        $this->assertEquals('0.01388880', $memoryHourly); // 约等于 10/720
        $this->assertEquals('0.02777760', $cpuHourly); // 约等于 20/720
        $this->assertEquals('0.00694440', $storageHourly); // 约等于 5/720
        $this->assertEquals('0.06944400', $lbHourly); // 约等于 50/720
    }
}
