<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClusterExternalIpTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户和工作空间
        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
        ]);
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);
        $this->user->setCurrentWorkspace($this->workspace);
    }

    public function test_set_cluster_ingress_external_ips_command()
    {
        // 测试设置 Ingress 外部 IP
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'test-cluster',
            'external_ips' => ['*************', '*************'],
        ])
            ->expectsOutput("已设置集群 'test-cluster' 的 Ingress 外部 IP:")
            ->expectsOutput('  - *************')
            ->expectsOutput('  - *************')
            ->assertExitCode(0);

        // 验证设置已保存
        $this->cluster->refresh();
        $ingressIps = $this->cluster->getSetting('ingress_external_ips');
        $this->assertEquals(['*************', '*************'], $ingressIps);
    }

    public function test_clear_cluster_ingress_external_ips_command()
    {
        // 先设置一些 IP
        $this->cluster->setSetting('ingress_external_ips', ['*************']);

        // 测试清空
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'test-cluster',
            '--clear' => true,
        ])
            ->expectsOutput("已清空集群 'test-cluster' 的 Ingress 外部 IP 配置")
            ->assertExitCode(0);

        // 验证已清空
        $this->cluster->refresh();
        $ingressIps = $this->cluster->getSetting('ingress_external_ips', []);
        $this->assertEquals([], $ingressIps);
    }

    public function test_invalid_ip_address()
    {
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'test-cluster',
            'external_ips' => ['invalid-ip'],
        ])
            ->expectsOutput('无效的 IP 地址: invalid-ip')
            ->assertExitCode(1);
    }

    public function test_nonexistent_cluster()
    {
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'nonexistent-cluster',
            'external_ips' => ['*************'],
        ])
            ->expectsOutput("集群 'nonexistent-cluster' 不存在")
            ->assertExitCode(1);
    }

    public function test_get_cluster_nodes_api()
    {
        // 这里需要模拟 K8s API 响应，因为我们没有真实的集群
        // 在实际环境中，这应该返回真实的节点信息
        $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/nodes")
            ->assertStatus(500); // 期望失败，因为没有真实的 K8s 集群
    }

    public function test_get_cluster_ingress_external_ips_api()
    {
        // 设置一些 Ingress 外部 IP
        $this->cluster->setSetting('ingress_external_ips', ['*************', '*************']);

        $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/ingress-external-ips")
            ->assertOk()
            ->assertJson(['*************', '*************']);
    }

    public function test_get_cluster_ingress_external_ips_api_not_configured()
    {
        $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/ingress-external-ips")
            ->assertStatus(404)
            ->assertJson(['message' => '未配置 Ingress 外部 IP']);
    }
}
