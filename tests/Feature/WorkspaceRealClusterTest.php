<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\NamespaceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceRealClusterTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    private User $user;

    private NamespaceService $namespaceService;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建真实的 K8s 集群配置
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
            'kubeconfig' => file_get_contents('/Users/<USER>/Herd/paas/testdata/k8s.yaml'),
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();
        $this->namespaceService = new NamespaceService;
    }

    /**
     * 测试在真实 K8s 集群中创建 Workspace
     *
     * @group real-cluster
     */
    public function test_create_workspace_with_real_cluster()
    {
        // 跳过测试如果没有真实的 K8s 集群
        if (! $this->isRealClusterAvailable()) {
            $this->markTestSkipped('真实 K8s 集群不可用');
        }

        $namespaceName = 'test-workspace-'.time();

        // 创建工作空间
        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-real-workspace',
            'namespace' => $namespaceName,
            'status' => 'pending',
        ]);

        // 等待 namespace 创建完成
        sleep(2);

        // 刷新工作空间状态
        $workspace->refresh();

        // 验证工作空间状态
        if ($workspace->status === 'active') {
            // 验证 namespace 是否真的被创建
            $this->assertTrue($this->namespaceService->namespaceExists($workspace));

            // 验证资源配额是否被创建
            $quota = $this->namespaceService->getResourceQuota($workspace);
            $this->assertNotNull($quota);
            $this->assertArrayHasKey('limits', $quota);
            $this->assertArrayHasKey('used', $quota);

            // 验证网络策略是否被创建
            if (config('k8s.workspace.networkPolicy.enabled')) {
                $policies = $this->namespaceService->getNetworkPolicies($workspace);
                $this->assertNotEmpty($policies);

                // 应该至少有一个网络策略
                $policyNames = array_column($policies, 'name');
                $this->assertContains('allow-same-namespace', $policyNames);
            }

            // 清理：删除工作空间
            $workspace->delete();

            // 验证 namespace 被删除
            sleep(2);
            $this->assertFalse($this->namespaceService->namespaceExists($workspace));
        } else {
            // 如果创建失败，输出错误信息
            $this->fail("工作空间创建失败: {$workspace->suspension_reason}");
        }
    }

    /**
     * 测试资源配额限制
     *
     * @group real-cluster
     */
    public function test_resource_quota_limits()
    {
        if (! $this->isRealClusterAvailable()) {
            $this->markTestSkipped('真实 K8s 集群不可用');
        }

        $namespaceName = 'test-quota-'.time();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-quota-workspace',
            'namespace' => $namespaceName,
            'status' => 'pending',
        ]);

        sleep(2);
        $workspace->refresh();

        if ($workspace->status === 'active') {
            $quota = $this->namespaceService->getResourceQuota($workspace);

            // 验证资源配额设置
            $this->assertNotNull($quota);
            $this->assertArrayHasKey('limits', $quota['limits']);

            // 验证内存限制
            $memoryLimit = $quota['limits']['limits.memory'] ?? null;
            $this->assertNotNull($memoryLimit);
            $this->assertStringContains('Mi', $memoryLimit);

            // 验证 CPU 限制
            $cpuLimit = $quota['limits']['limits.cpu'] ?? null;
            $this->assertNotNull($cpuLimit);
            $this->assertStringContains('m', $cpuLimit);

            // 清理
            $workspace->delete();
        } else {
            $this->fail("工作空间创建失败: {$workspace->suspension_reason}");
        }
    }

    /**
     * 检查真实 K8s 集群是否可用
     */
    private function isRealClusterAvailable(): bool
    {
        try {
            // 尝试连接到集群
            $response = $this->cluster->http()->get('/api/v1/namespaces');

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }
}
