<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use Tests\TestCase;

class StorageIntegrationTest extends TestCase
{
    public function test_storage_api_integration()
    {
        $user = User::first();
        $workspace = Workspace::where('status', 'active')->first();

        if (! $workspace) {
            $this->markTestSkipped('No active workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        // 测试创建 Storage
        $response = $this->actingAs($user)
            ->postJson("/api/workspaces/{$workspace->id}/storages", [
                'name' => 'test-storage-integration',
                'size' => 1024,
            ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'name',
            'size',
            'storage_class',
            'access_modes',
            'status',
            'formatted_size',
            'created_at',
        ]);

        $storageName = $response->json('name');

        // 测试获取 Storage 列表
        $response = $this->actingAs($user)
            ->getJson("/api/workspaces/{$workspace->id}/storages");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'name',
                'size',
                'storage_class',
                'access_modes',
                'status',
                'formatted_size',
                'created_at',
            ],
        ]);

        // 验证我们创建的 Storage 在列表中
        $storages = $response->json();
        $found = false;
        foreach ($storages as $storage) {
            if ($storage['name'] === $storageName) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, 'Created storage not found in list');

        // 测试获取单个 Storage
        $response = $this->actingAs($user)
            ->getJson("/api/workspaces/{$workspace->id}/storages/{$storageName}");

        $response->assertStatus(200);
        $response->assertJson(['name' => $storageName]);

        // 测试扩容 Storage
        $response = $this->actingAs($user)
            ->patchJson("/api/workspaces/{$workspace->id}/storages/{$storageName}/expand", [
                'size' => 1536, // 扩容到 1.5GB
            ]);

        $response->assertStatus(200);

        // 测试删除 Storage
        $response = $this->actingAs($user)
            ->deleteJson("/api/workspaces/{$workspace->id}/storages/{$storageName}");

        $response->assertStatus(204);

        // 验证 Storage 已被删除
        $response = $this->actingAs($user)
            ->getJson("/api/workspaces/{$workspace->id}/storages/{$storageName}");

        $response->assertStatus(404);
    }

    public function test_storage_validation()
    {
        $user = User::first();
        $workspace = Workspace::where('status', 'active')->first();

        if (! $workspace) {
            $this->markTestSkipped('No active workspace available for testing');
        }

        $user->setCurrentWorkspace($workspace);

        // 测试容量小于最小值
        $response = $this->actingAs($user)
            ->postJson("/api/workspaces/{$workspace->id}/storages", [
                'name' => 'test-storage-small',
                'size' => 256,
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);

        // 测试容量不是 512 的倍数
        $response = $this->actingAs($user)
            ->postJson("/api/workspaces/{$workspace->id}/storages", [
                'name' => 'test-storage-invalid',
                'size' => 700,
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size']);

        // 测试无效的名称格式
        $response = $this->actingAs($user)
            ->postJson("/api/workspaces/{$workspace->id}/storages", [
                'name' => 'Invalid_Name!',
                'size' => 512,
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }
}
