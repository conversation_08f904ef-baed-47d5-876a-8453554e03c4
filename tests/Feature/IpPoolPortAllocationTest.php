<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IpPoolService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class IpPoolPortAllocationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected Workspace $workspace;

    protected IpPool $ipPool;

    protected PoolIp $poolIp;

    protected IpPoolService $ipPoolService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);

        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
        ]);

        $this->poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30010, // 小范围便于测试
        ]);

        $this->ipPoolService = new IpPoolService;
    }

    /** @test */
    public function it_can_allocate_first_port_efficiently()
    {
        // 第一次分配应该直接从起始端口开始
        $port = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-1', 'test-namespace');

        $this->assertEquals(30000, $port);

        // 验证数据库记录
        $allocation = PortAllocation::where('pool_ip_id', $this->poolIp->id)
            ->where('port', $port)
            ->first();

        $this->assertNotNull($allocation);
        $this->assertEquals('test-service-1', $allocation->service_name);
        $this->assertEquals('test-namespace', $allocation->namespace);
        $this->assertEquals('allocated', $allocation->status);
    }

    /** @test */
    public function it_can_allocate_sequential_ports()
    {
        // 分配多个端口应该是连续的
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-1', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-2', 'test-namespace');
        $port3 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-3', 'test-namespace');

        $this->assertEquals(30000, $port1);
        $this->assertEquals(30001, $port2);
        $this->assertEquals(30002, $port3);
    }

    /** @test */
    public function it_can_reuse_released_ports()
    {
        // 先分配几个端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-1', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-2', 'test-namespace');
        $port3 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-3', 'test-namespace');

        $this->assertEquals([30000, 30001, 30002], [$port1, $port2, $port3]);

        // 释放中间的端口
        $this->ipPoolService->releasePort('test-service-2', 'test-namespace');

        // 分配新端口应该复用释放的端口
        $port4 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-4', 'test-namespace');
        $this->assertEquals(30001, $port4); // 复用了 service-2 的端口

        // 下一个分配应该是连续的新端口
        $port5 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-5', 'test-namespace');
        $this->assertEquals(30003, $port5);
    }

    /** @test */
    public function it_handles_concurrent_allocation_correctly()
    {
        $allocatedPorts = [];

        // 模拟并发分配
        DB::transaction(function () use (&$allocatedPorts) {
            for ($i = 1; $i <= 5; $i++) {
                $port = $this->ipPoolService->allocatePort(
                    $this->poolIp,
                    "test-service-{$i}",
                    'test-namespace'
                );
                $allocatedPorts[] = $port;
            }
        });

        // 验证分配的端口都是唯一的
        $this->assertEquals(5, count(array_unique($allocatedPorts)));

        // 验证端口都在有效范围内
        foreach ($allocatedPorts as $port) {
            $this->assertGreaterThanOrEqual(30000, $port);
            $this->assertLessThanOrEqual(30010, $port);
        }
    }

    /** @test */
    public function it_throws_exception_when_ports_exhausted()
    {
        // 分配所有可用端口 (30000-30010 = 11个端口)
        for ($i = 1; $i <= 11; $i++) {
            $this->ipPoolService->allocatePort($this->poolIp, "test-service-{$i}", 'test-namespace');
        }

        // 尝试分配第12个端口应该失败
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('的端口已耗尽');

        $this->ipPoolService->allocatePort($this->poolIp, 'test-service-12', 'test-namespace');
    }

    /** @test */
    public function it_returns_existing_port_for_same_service()
    {
        // 为同一个服务分配端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');

        // 应该返回相同的端口
        $this->assertEquals($port1, $port2);

        // 数据库中应该只有一条记录
        $count = PortAllocation::where('service_name', 'test-service')
            ->where('namespace', 'test-namespace')
            ->where('status', 'allocated')
            ->count();

        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_handles_disabled_ports_correctly()
    {
        // 禁用一些端口
        PortAllocation::create([
            'pool_ip_id' => $this->poolIp->id,
            'port' => 30001,
            'service_name' => null,
            'namespace' => null,
            'status' => 'released',
            'is_disabled' => true,
            'reason' => 'Test disabled port',
        ]);

        PortAllocation::create([
            'pool_ip_id' => $this->poolIp->id,
            'port' => 30003,
            'service_name' => null,
            'namespace' => null,
            'status' => 'released',
            'is_disabled' => true,
            'reason' => 'Test disabled port',
        ]);

        // 分配端口应该跳过禁用的端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-1', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-2', 'test-namespace');
        $port3 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service-3', 'test-namespace');

        $this->assertEquals(30000, $port1); // 正常分配
        $this->assertEquals(30002, $port2); // 跳过了 30001
        $this->assertEquals(30004, $port3); // 跳过了 30003
    }

    /** @test */
    public function it_performs_efficiently_with_many_allocations()
    {
        // 创建一个更大的端口范围
        $largePoolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 31000, // 1000个端口
        ]);

        $startTime = microtime(true);

        // 分配100个端口
        for ($i = 1; $i <= 100; $i++) {
            $this->ipPoolService->allocatePort($largePoolIp, "test-service-{$i}", 'test-namespace');
        }

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // 100个端口分配应该在1秒内完成
        $this->assertLessThan(1.0, $duration, "端口分配耗时过长: {$duration}秒");

        // 验证分配的端口数量
        $allocatedCount = PortAllocation::where('pool_ip_id', $largePoolIp->id)
            ->where('status', 'allocated')
            ->count();

        $this->assertEquals(100, $allocatedCount);
    }

    /** @test */
    public function it_efficiently_reuses_released_ports_in_bulk()
    {
        // 创建一个更大的端口范围用于批量测试
        $bulkPoolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 30100, // 100个端口
        ]);

        // 先分配50个端口
        for ($i = 1; $i <= 50; $i++) {
            $this->ipPoolService->allocatePort($bulkPoolIp, "initial-service-{$i}", 'test-namespace');
        }

        // 释放前25个端口
        for ($i = 1; $i <= 25; $i++) {
            $this->ipPoolService->releasePort("initial-service-{$i}", 'test-namespace');
        }

        $startTime = microtime(true);

        // 重新分配25个端口，应该复用释放的端口
        for ($i = 1; $i <= 25; $i++) {
            $this->ipPoolService->allocatePort($bulkPoolIp, "new-service-{$i}", 'test-namespace');
        }

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // 复用端口应该很快
        $this->assertLessThan(0.5, $duration, "端口复用耗时过长: {$duration}秒");

        // 验证总的已分配端口数量
        $allocatedCount = PortAllocation::where('pool_ip_id', $bulkPoolIp->id)
            ->where('status', 'allocated')
            ->count();

        $this->assertEquals(50, $allocatedCount); // 25个保留 + 25个新分配
    }

    /** @test */
    public function it_handles_concurrent_port_allocation_safely()
    {
        // 创建一个小的端口范围便于测试
        $this->poolIp->update([
            'port_range_start' => 30000,
            'port_range_end' => 30005, // 只有6个端口
        ]);

        $allocatedPorts = [];
        $exceptions = [];

        // 模拟并发请求 - 尝试分配10个端口（超过可用数量）
        for ($i = 1; $i <= 10; $i++) {
            try {
                $port = $this->ipPoolService->allocatePort(
                    $this->poolIp,
                    "concurrent-service-{$i}",
                    'test-namespace'
                );
                $allocatedPorts[] = $port;
            } catch (\Exception $e) {
                $exceptions[] = $e->getMessage();
            }
        }

        // 验证结果
        $this->assertCount(6, $allocatedPorts, '应该只分配6个端口');
        $this->assertCount(4, $exceptions, '应该有4个异常（端口耗尽）');

        // 验证分配的端口都是唯一的
        $uniquePorts = array_unique($allocatedPorts);
        $this->assertCount(6, $uniquePorts, '分配的端口应该都是唯一的');

        // 验证端口都在正确的范围内
        foreach ($allocatedPorts as $port) {
            $this->assertGreaterThanOrEqual(30000, $port);
            $this->assertLessThanOrEqual(30005, $port);
        }

        // 验证数据库中的记录
        $dbAllocations = PortAllocation::where('pool_ip_id', $this->poolIp->id)
            ->where('status', 'allocated')
            ->pluck('port')
            ->sort()
            ->values()
            ->toArray();

        sort($allocatedPorts);
        $this->assertCount(6, $dbAllocations);
        $this->assertEquals($allocatedPorts, $dbAllocations);
    }

    /** @test */
    public function it_prevents_duplicate_port_allocation_for_same_service()
    {
        // 为同一个服务多次分配端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');
        $port3 = $this->ipPoolService->allocatePort($this->poolIp, 'test-service', 'test-namespace');

        // 应该返回相同的端口
        $this->assertEquals($port1, $port2);
        $this->assertEquals($port1, $port3);

        // 数据库中应该只有一条记录
        $count = PortAllocation::where('service_name', 'test-service')
            ->where('namespace', 'test-namespace')
            ->where('status', 'allocated')
            ->count();

        $this->assertEquals(1, $count);
    }

    /** @test */
    public function it_reuses_released_ports_correctly()
    {
        // 分配一些端口
        $port1 = $this->ipPoolService->allocatePort($this->poolIp, 'service-1', 'test-namespace');
        $port2 = $this->ipPoolService->allocatePort($this->poolIp, 'service-2', 'test-namespace');
        $port3 = $this->ipPoolService->allocatePort($this->poolIp, 'service-3', 'test-namespace');

        // 释放中间的端口
        $this->ipPoolService->releasePort('service-2', 'test-namespace');

        // 分配新端口应该重用已释放的端口
        $newPort = $this->ipPoolService->allocatePort($this->poolIp, 'service-4', 'test-namespace');

        $this->assertEquals($port2, $newPort, '应该重用已释放的端口');

        // 验证数据库状态
        $allocation = PortAllocation::where('pool_ip_id', $this->poolIp->id)
            ->where('port', $port2)
            ->where('status', 'allocated')
            ->first();

        $this->assertNotNull($allocation);
        $this->assertEquals('service-4', $allocation->service_name);
    }
}
