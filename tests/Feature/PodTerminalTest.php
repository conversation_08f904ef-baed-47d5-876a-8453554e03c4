<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use App\Service\JwtService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PodTerminalTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected JwtService $jwtService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->create(['user_id' => $this->user->id]);
        $this->jwtService = new JwtService;

        // 设置当前工作空间
        $this->user->update(['current_workspace_id' => $this->workspace->id]);
    }

    public function test_can_generate_jwt_keys(): void
    {
        $this->artisan('websocket:generate-jwt-keys --force')
            ->expectsOutput('JWT keys generated successfully!')
            ->assertExitCode(0);

        $this->assertFileExists(config('websocket.jwt.private_key_path'));
        $this->assertFileExists(config('websocket.jwt.public_key_path'));
    }

    public function test_can_generate_pod_terminal_token(): void
    {
        // 先生成 JWT 密钥
        $this->artisan('websocket:generate-jwt-keys --force');

        $token = $this->jwtService->generatePodTerminalToken(
            $this->user->id,
            $this->workspace->id,
            'test-pod',
            'test-container',
            'shell'
        );

        $this->assertNotEmpty($token);

        // 验证 token
        $decoded = $this->jwtService->verifyToken($token);
        $this->assertEquals($this->user->id, $decoded['user_id']);
        $this->assertEquals($this->workspace->id, $decoded['workspace_id']);
        $this->assertEquals('test-pod', $decoded['pod_name']);
        $this->assertEquals('test-container', $decoded['container_name']);
        $this->assertEquals('shell', $decoded['mode']);
        $this->assertEquals('pod_terminal', $decoded['type']);
    }

    public function test_can_get_supported_modes(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/terminal/modes');

        $response->assertOk()
            ->assertJsonStructure([
                'modes' => [
                    '*' => [
                        'name',
                        'display_name',
                        'description',
                        'icon',
                    ],
                ],
            ]);

        $modes = $response->json('modes');
        $modeNames = collect($modes)->pluck('name')->toArray();

        $this->assertContains('shell', $modeNames);
        $this->assertContains('attach', $modeNames);
    }

    public function test_can_get_websocket_server_status(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/terminal/status');

        $response->assertOk()
            ->assertJsonStructure([
                'status',
                'websocket_url',
            ]);
    }

    public function test_generate_token_requires_authentication(): void
    {
        $response = $this->postJson('/api/pods/test-pod/terminal/token', [
            'pod_name' => 'test-pod',
            'mode' => 'shell',
        ]);

        $response->assertUnauthorized();
    }

    public function test_generate_token_validates_input(): void
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/pods/test-pod/terminal/token', [
                // 缺少 pod_name
                'mode' => 'invalid_mode',
            ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['pod_name']);
    }

    public function test_jwt_token_expiration(): void
    {
        // 先生成 JWT 密钥
        $this->artisan('websocket:generate-jwt-keys --force');

        // 生成过期时间很短的 token
        config(['websocket.jwt.ttl' => -1]); // 设置为过去时间

        $token = $this->jwtService->generatePodTerminalToken(
            $this->user->id,
            $this->workspace->id,
            'test-pod',
            'test-container',
            'shell'
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid JWT token');

        $this->jwtService->verifyToken($token);
    }

    public function test_jwt_service_handles_missing_keys(): void
    {
        // 删除密钥文件
        @unlink(config('websocket.jwt.private_key_path'));
        @unlink(config('websocket.jwt.public_key_path'));

        $jwtService = new JwtService;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Private key not found');

        $jwtService->generatePodTerminalToken(
            $this->user->id,
            $this->workspace->id,
            'test-pod',
            'test-container',
            'shell'
        );
    }
}
