<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->workspaceService = new WorkspaceService;
});

test('generateUniqueNamespace generates unique namespace with correct format', function () {
    $namespace = $this->workspaceService->generateUniqueNamespace();

    // 检查格式是否正确
    expect($namespace)->toMatch('/^ns-[a-z0-9]{8}$/');

    // 检查是否唯一
    expect(Workspace::where('namespace', $namespace)->exists())->toBeFalse();
});

test('generateUniqueNamespace generates different namespaces', function () {
    $namespace1 = $this->workspaceService->generateUniqueNamespace();
    $namespace2 = $this->workspaceService->generateUniqueNamespace();

    expect($namespace1)->not()->toBe($namespace2);
});

test('createWorkspace creates workspace with correct data', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $data = [
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'test-workspace',
    ];

    $workspace = $this->workspaceService->createWorkspace($data);

    expect($workspace)->toBeInstanceOf(Workspace::class);
    expect($workspace->user_id)->toBe($user->id);
    expect($workspace->cluster_id)->toBe($cluster->id);
    expect($workspace->name)->toBe('test-workspace');
    expect($workspace->status)->toBe('pending');
    expect($workspace->namespace)->toMatch('/^ns-[a-z0-9]{8}$/');
});

test('isWorkspaceNameUnique returns true for unique name', function () {
    $user = User::factory()->create();

    $isUnique = $this->workspaceService->isWorkspaceNameUnique('unique-name', $user->id);

    expect($isUnique)->toBeTrue();
});

test('isWorkspaceNameUnique returns false for existing name', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    // 创建现有工作空间
    Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'existing-workspace',
    ]);

    $isUnique = $this->workspaceService->isWorkspaceNameUnique('existing-workspace', $user->id);

    expect($isUnique)->toBeFalse();
});
