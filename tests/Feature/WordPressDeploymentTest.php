<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\DeploymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class WordPressDeploymentTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);
    }

    /** @test */
    public function it_can_handle_wordpress_deployment_with_empty_env_from_configmap()
    {
        // Mock Kubernetes API responses
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        // 这是用户提供的实际数据，之前会导致 422 错误
        $wordpressData = [
            'name' => 'wordpress',
            'replicas' => 1,
            'restart_policy' => 'Always',
            'image_pull_secrets' => [],
            'containers' => [
                [
                    'name' => 'wordpress',
                    'image' => 'wordpress:6.8.1-php8.1-apache',
                    'working_dir' => '',
                    'command' => [],
                    'args' => [],
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'env' => [],
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'wordpress',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                    'env_from_secret' => [],
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 1000,
                    ],
                    'volume_mounts' => [
                        [
                            'storage_name' => 'wordpress',
                            'mount_path' => '/var/www/html',
                            'sub_path' => '',
                            'read_only' => false,
                        ],
                    ],
                    'configmap_mounts' => [],
                    'secret_mounts' => [],
                ],
            ],
        ];

        // 使用反射来访问受保护的方法
        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);

        // 这应该不会抛出异常
        $payload = $method->invoke($deploymentService, $wordpressData);

        // 验证生成的 payload 结构正确
        $this->assertArrayHasKey('spec', $payload);
        $this->assertArrayHasKey('template', $payload['spec']);
        $this->assertArrayHasKey('spec', $payload['spec']['template']);
        $this->assertArrayHasKey('containers', $payload['spec']['template']['spec']);

        $container = $payload['spec']['template']['spec']['containers'][0];

        // 验证容器基本信息
        $this->assertEquals('wordpress', $container['name']);
        $this->assertEquals('wordpress:6.8.1-php8.1-apache', $container['image']);

        // 验证端口配置
        $this->assertArrayHasKey('ports', $container);
        $this->assertCount(1, $container['ports']);
        $this->assertEquals(80, $container['ports'][0]['containerPort']);

        // 验证环境变量配置 - 应该使用 envFrom 而不是 env
        $this->assertArrayHasKey('envFrom', $container);
        $this->assertCount(1, $container['envFrom']);
        $this->assertEquals([
            'configMapRef' => [
                'name' => 'wordpress',
            ],
        ], $container['envFrom'][0]);

        // 验证资源限制
        $this->assertArrayHasKey('resources', $container);
        $this->assertEquals('1024Mi', $container['resources']['limits']['memory']);
        $this->assertEquals('1000m', $container['resources']['limits']['cpu']);
        $this->assertEquals('512Mi', $container['resources']['requests']['memory']);
        $this->assertEquals('500m', $container['resources']['requests']['cpu']);

        // 验证卷挂载
        $this->assertArrayHasKey('volumeMounts', $container);
        $this->assertCount(1, $container['volumeMounts']);
        $this->assertEquals('wordpress', $container['volumeMounts'][0]['name']);
        $this->assertEquals('/var/www/html', $container['volumeMounts'][0]['mountPath']);

        // 验证卷配置
        $this->assertArrayHasKey('volumes', $payload['spec']['template']['spec']);
        $this->assertCount(1, $payload['spec']['template']['spec']['volumes']);
        $this->assertEquals([
            'name' => 'wordpress',
            'persistentVolumeClaim' => [
                'claimName' => 'wordpress',
            ],
        ], $payload['spec']['template']['spec']['volumes'][0]);
    }

    /** @test */
    public function it_generates_valid_kubernetes_deployment_yaml()
    {
        Http::fake([
            '*' => Http::response(['items' => []], 200),
        ]);

        $deploymentService = new DeploymentService($this->workspace);

        $wordpressData = [
            'name' => 'wordpress',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'wordpress',
                    'image' => 'wordpress:6.8.1-php8.1-apache',
                    'env_from_configmap' => [
                        [
                            'configmap_name' => 'wordpress',
                            'key' => '',
                            'env_name' => '',
                        ],
                    ],
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 1000,
                    ],
                ],
            ],
        ];

        $reflection = new \ReflectionClass($deploymentService);
        $method = $reflection->getMethod('buildDeploymentPayload');
        $method->setAccessible(true);

        $payload = $method->invoke($deploymentService, $wordpressData);

        // 验证生成的 YAML 结构符合 Kubernetes 规范
        $this->assertEquals('apps/v1', $payload['apiVersion']);
        $this->assertEquals('Deployment', $payload['kind']);

        // 验证 metadata
        $this->assertArrayHasKey('metadata', $payload);
        $this->assertEquals('wordpress', $payload['metadata']['name']);
        $this->assertEquals($this->workspace->namespace, $payload['metadata']['namespace']);

        // 验证 selector
        $this->assertArrayHasKey('selector', $payload['spec']);
        $this->assertEquals(['app' => 'wordpress'], $payload['spec']['selector']['matchLabels']);

        // 验证 template labels
        $templateLabels = $payload['spec']['template']['metadata']['labels'];
        $this->assertEquals('wordpress', $templateLabels['app']);
        $this->assertEquals($this->workspace->name, $templateLabels['workspace']);

        // 最重要的：验证不会产生无效的 Kubernetes 配置
        // envFrom 应该在容器级别，而不是 env 中的 configMapRef
        $container = $payload['spec']['template']['spec']['containers'][0];

        if (isset($container['env'])) {
            foreach ($container['env'] as $envVar) {
                // env 数组中不应该有 configMapRef
                $this->assertArrayNotHasKey('configMapRef', $envVar['valueFrom'] ?? []);
            }
        }

        // envFrom 应该存在并且格式正确
        $this->assertArrayHasKey('envFrom', $container);
        $this->assertIsArray($container['envFrom']);
        $this->assertArrayHasKey('configMapRef', $container['envFrom'][0]);
        $this->assertEquals('wordpress', $container['envFrom'][0]['configMapRef']['name']);
    }
}
