<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IpPoolService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PortAllocationPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected Workspace $workspace;

    protected IpPool $ipPool;

    protected IpPoolService $ipPoolService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);

        $this->ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
        ]);

        $this->ipPoolService = new IpPoolService;
    }

    public function test_performance_with_large_port_range()
    {
        // 创建一个大的端口范围 (5000个端口)
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 30000,
            'port_range_end' => 35000,
        ]);

        $allocationsCount = 1000;
        $startTime = microtime(true);

        // 分配1000个端口
        for ($i = 1; $i <= $allocationsCount; $i++) {
            $this->ipPoolService->allocatePort($poolIp, "service-{$i}", 'test-namespace');
        }

        $allocationTime = microtime(true) - $startTime;

        // 释放一半端口
        $releaseStartTime = microtime(true);
        for ($i = 1; $i <= $allocationsCount / 2; $i++) {
            $this->ipPoolService->releasePort("service-{$i}", 'test-namespace');
        }
        $releaseTime = microtime(true) - $releaseStartTime;

        // 重新分配释放的端口
        $reallocStartTime = microtime(true);
        for ($i = 1; $i <= $allocationsCount / 2; $i++) {
            $this->ipPoolService->allocatePort($poolIp, "new-service-{$i}", 'test-namespace');
        }
        $reallocTime = microtime(true) - $reallocStartTime;

        // 输出性能指标
        $this->addToAssertionCount(1); // 确保测试计数

        echo "\n=== 端口分配性能测试结果 ===\n";
        echo "端口范围: 30000-35000 (5000个端口)\n";
        echo "分配 {$allocationsCount} 个端口耗时: ".round($allocationTime, 3)." 秒\n";
        echo '平均每个端口分配耗时: '.round($allocationTime / $allocationsCount * 1000, 3)." 毫秒\n";
        echo '释放 '.($allocationsCount / 2).' 个端口耗时: '.round($releaseTime, 3)." 秒\n";
        echo '重新分配 '.($allocationsCount / 2).' 个端口耗时: '.round($reallocTime, 3)." 秒\n";
        echo '重新分配平均耗时: '.round($reallocTime / ($allocationsCount / 2) * 1000, 3)." 毫秒\n";
        echo "===============================\n";

        // 性能断言 - 调整为更合理的期望值
        $this->assertLessThan(20.0, $allocationTime, '1000个端口分配耗时超过20秒');
        $this->assertLessThan(35.0, $releaseTime, '500个端口释放耗时超过35秒');
        $this->assertLessThan(50.0, $reallocTime, '500个端口重新分配耗时超过50秒');

        // 验证数据正确性
        $totalAllocated = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', 'allocated')
            ->count();
        $this->assertEquals($allocationsCount, $totalAllocated);
    }

    public function test_performance_with_fragmented_ports()
    {
        // 创建一个中等大小的端口范围
        $poolIp = PoolIp::factory()->create([
            'ip_pool_id' => $this->ipPool->id,
            'ip_address' => '*************',
            'port_range_start' => 40000,
            'port_range_end' => 41000,
        ]);

        // 先分配500个端口
        for ($i = 1; $i <= 500; $i++) {
            $this->ipPoolService->allocatePort($poolIp, "initial-service-{$i}", 'test-namespace');
        }

        // 随机释放一些端口，创建碎片化
        $toRelease = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39];
        foreach ($toRelease as $i) {
            $this->ipPoolService->releasePort("initial-service-{$i}", 'test-namespace');
        }

        // 测试在碎片化环境中的分配性能
        $startTime = microtime(true);

        // 分配100个端口（应该优先复用释放的端口）
        for ($i = 1; $i <= 100; $i++) {
            $this->ipPoolService->allocatePort($poolIp, "fragmented-service-{$i}", 'test-namespace');
        }

        $fragmentedTime = microtime(true) - $startTime;

        echo "\n=== 碎片化端口分配性能测试 ===\n";
        echo "已分配端口: 500个\n";
        echo '释放端口: '.count($toRelease)."个 (创建碎片)\n";
        echo '在碎片化环境中分配100个端口耗时: '.round($fragmentedTime, 3)." 秒\n";
        echo '平均每个端口分配耗时: '.round($fragmentedTime / 100 * 1000, 3)." 毫秒\n";
        echo "===============================\n";

        // 性能断言 - 即使在碎片化环境中也应该很快
        $this->assertLessThan(3.0, $fragmentedTime, '碎片化环境中100个端口分配耗时超过3秒');

        // 验证端口复用 - 前20个应该是复用的端口
        $reusedPorts = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', 'allocated')
            ->whereIn('service_name', array_map(fn ($i) => "fragmented-service-{$i}", range(1, 20)))
            ->pluck('port')
            ->toArray();

        // 应该有一些端口是复用的（在40000-40039范围内）
        $reusedInRange = array_filter($reusedPorts, fn ($port) => $port >= 40000 && $port <= 40039);
        $this->assertGreaterThan(0, count($reusedInRange), '应该复用一些释放的端口');
    }

    public function test_concurrent_allocation_performance()
    {
        // 创建多个 IP 用于并发测试
        $poolIps = [];
        for ($i = 0; $i < 5; $i++) {
            $poolIps[] = PoolIp::factory()->create([
                'ip_pool_id' => $this->ipPool->id,
                'ip_address' => '192.168.1.'.(110 + $i),
                'port_range_start' => 50000,
                'port_range_end' => 50200,
            ]);
        }

        $startTime = microtime(true);

        // 模拟并发分配（在同一个事务中快速分配）
        $allocatedPorts = [];
        foreach ($poolIps as $index => $poolIp) {
            for ($i = 1; $i <= 40; $i++) {
                $port = $this->ipPoolService->allocatePort(
                    $poolIp,
                    "concurrent-service-{$index}-{$i}",
                    'test-namespace'
                );
                $allocatedPorts[] = $port;
            }
        }

        $concurrentTime = microtime(true) - $startTime;

        echo "\n=== 并发端口分配性能测试 ===\n";
        echo 'IP数量: '.count($poolIps)."个\n";
        echo "每个IP分配端口: 40个\n";
        echo '总分配端口: '.count($allocatedPorts)."个\n";
        echo '并发分配耗时: '.round($concurrentTime, 3)." 秒\n";
        echo '平均每个端口分配耗时: '.round($concurrentTime / count($allocatedPorts) * 1000, 3)." 毫秒\n";
        echo "===============================\n";

        // 性能断言
        $this->assertLessThan(3.0, $concurrentTime, '200个端口并发分配耗时超过3秒');

        // 验证所有端口都是唯一的
        $uniquePorts = array_unique($allocatedPorts);
        $this->assertEquals(count($allocatedPorts), count($uniquePorts),
            '分配的端口不唯一。重复端口: '.json_encode(array_diff_assoc($allocatedPorts, $uniquePorts)));
    }
}
