<?php

namespace Tests\Feature;

use App\Models\User;
use App\Service\PaymentManagerService;
use App\Service\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CompletePaymentFlowTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private PaymentManagerService $paymentManager;

    private PaymentService $paymentService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->paymentManager = app(PaymentManagerService::class);
        $this->paymentService = app(PaymentService::class);
    }

    public function test_complete_test_payment_flow()
    {
        // 1. 记录初始余额
        $initialBalance = $this->user->current_balance;
        $amount = 100.00;

        // 2. 创建支付订单
        $paymentData = $this->paymentService->createPayment($this->user, $amount, 'test_payment', [
            'subject' => '测试充值',
            'body' => '完整流程测试',
        ]);

        $this->assertArrayHasKey('url', $paymentData);
        $this->assertArrayHasKey('order_id', $paymentData);

        // 3. 创建待支付记录
        $record = $this->paymentService->createPendingTopUpRecord(
            $this->user,
            $amount,
            'test_payment',
            [
                'remark' => '测试充值',
                'order_id' => $paymentData['order_id'],
            ]
        );

        $this->assertEquals(\App\Models\TopUpRecord::STATUS_PENDING, $record->status);
        $this->assertEquals(0, $record->remaining_amount);

        // 4. 解析支付URL获取回调参数
        $parsedUrl = parse_url($paymentData['url']);
        parse_str($parsedUrl['query'], $callbackData);

        // 5. 模拟支付回调
        $response = $this->post('/payment/callback/test_payment', $callbackData);

        $response->assertStatus(302);
        $response->assertRedirect(route('balance.index'));
        $response->assertSessionHas('success', '充值成功！');

        // 6. 验证充值记录状态已更新
        $record->refresh();
        $this->assertEquals(\App\Models\TopUpRecord::STATUS_COMPLETED, $record->status);
        $this->assertEquals($amount, $record->remaining_amount);

        // 7. 验证用户余额已更新
        $this->user->refresh();
        $this->assertEquals($initialBalance + $amount, $this->user->current_balance);
    }

    public function test_web_interface_payment_flow()
    {
        $this->actingAs($this->user);

        $initialBalance = $this->user->current_balance;
        $amount = 50.00;

        // 1. 访问余额页面，确保支付方式列表可用
        $response = $this->get('/balance');
        $response->assertStatus(200);
        $response->assertInertia(fn ($assert) => $assert->has('paymentMethods')
            ->has('paymentMethods.0.identifier')
        );

        // 2. 提交充值请求
        $response = $this->post('/balance/top-up', [
            'amount' => $amount,
            'payment_method' => 'test_payment',
        ]);

        // 3. 应该重定向到支付URL
        $response->assertRedirect();
        $redirectUrl = $response->headers->get('Location');
        $this->assertStringContainsString('/payment/callback/test_payment', $redirectUrl);

        // 4. 验证数据库中创建了待支付记录
        $this->assertDatabaseHas('top_up_records', [
            'user_id' => $this->user->id,
            'amount' => $amount,
            'status' => \App\Models\TopUpRecord::STATUS_PENDING,
            'payment_method' => 'test_payment',
        ]);

        // 5. 解析重定向URL并模拟回调
        $parsedUrl = parse_url($redirectUrl);
        parse_str($parsedUrl['query'], $callbackData);

        $callbackResponse = $this->post('/payment/callback/test_payment', $callbackData);
        $callbackResponse->assertStatus(302);
        $callbackResponse->assertRedirect(route('balance.index'));
        $callbackResponse->assertSessionHas('success', '充值成功！');

        // 6. 验证用户余额已更新
        $this->user->refresh();
        $this->assertEquals($initialBalance + $amount, $this->user->current_balance);
    }

    public function test_payment_method_api_endpoint()
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/payment-methods');
        $response->assertStatus(200);

        $paymentMethods = $response->json();
        $this->assertIsArray($paymentMethods);

        // 验证包含测试支付方式
        $testPaymentMethod = collect($paymentMethods)->firstWhere('identifier', 'test_payment');
        $this->assertNotNull($testPaymentMethod);
        $this->assertEquals('测试支付', $testPaymentMethod['name']);
        $this->assertTrue($testPaymentMethod['enabled']);
        $this->assertTrue($testPaymentMethod['supports_refund']);
        $this->assertTrue($testPaymentMethod['requires_callback']);
    }

    public function test_invalid_callback_signature()
    {
        // 创建一个待支付记录
        $record = \App\Models\TopUpRecord::factory()->create([
            'user_id' => $this->user->id,
            'amount' => 100.00,
            'status' => \App\Models\TopUpRecord::STATUS_PENDING,
            'payment_method' => 'test_payment',
        ]);

        // 使用无效签名的回调数据
        $callbackData = [
            'order_id' => 'TEST_ORDER_123',
            'amount' => 100.00,
            'user_id' => $this->user->id,
            'signature' => 'invalid_signature',
            'status' => 'success',
            'transaction_id' => 'TEST_123',
        ];

        $response = $this->post('/payment/callback/test_payment', $callbackData);
        $response->assertStatus(302);
        $response->assertRedirect(route('balance.index'));
        $response->assertSessionHas('error');

        // 验证记录状态未改变
        $record->refresh();
        $this->assertEquals(\App\Models\TopUpRecord::STATUS_PENDING, $record->status);
    }

    public function test_command_line_payment_with_test_gateway()
    {
        $initialBalance = $this->user->current_balance;
        $amount = 75.00;

        // 使用命令行工具添加余额
        $this->artisan('user:add-balance', [
            'user' => $this->user->email,
            'amount' => $amount,
            '--method' => 'test_payment',
            '--remark' => '命令行测试充值',
        ])
            ->expectsQuestion('确认执行充值操作？', 'yes')
            ->expectsOutput('充值成功！')
            ->assertExitCode(0);

        // 验证用户余额已更新
        $this->user->refresh();
        $this->assertEquals($initialBalance + $amount, $this->user->current_balance);

        // 验证充值记录已创建
        $this->assertDatabaseHas('top_up_records', [
            'user_id' => $this->user->id,
            'amount' => $amount,
            'status' => \App\Models\TopUpRecord::STATUS_COMPLETED,
            'payment_method' => 'test_payment',
            'remark' => '命令行测试充值',
        ]);
    }
}
