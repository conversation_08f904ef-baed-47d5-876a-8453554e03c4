<?php

namespace Tests\Feature;

use App\Events\WorkspaceAutoDeleted;
use App\Events\WorkspacePendingDeletion;
use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class WorkspaceOverdueCleanupTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'current_balance' => 10.0,
        ]);

        $this->cluster = Cluster::factory()->create();
    }

    /** @test */
    public function test_finds_workspaces_for_deletion()
    {
        // 创建不同状态的工作空间
        $activeWorkspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        $recentSuspendedWorkspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(3), // 只暂停3天
            'overdue_amount' => 50.0,
        ]);

        $oldSuspendedWorkspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8), // 暂停8天，超过7天限制
            'overdue_amount' => 100.0,
        ]);

        $nonOverdueWorkspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'manual',
            'suspended_at' => now()->subDays(10),
        ]);

        // 模拟命令执行（dry-run模式）
        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('发现 1 个工作空间需要清理')
            ->expectsOutputToContain($oldSuspendedWorkspace->name)
            ->assertExitCode(0);
    }

    /** @test */
    public function test_respects_deletion_days_configuration()
    {
        // 临时修改配置
        config(['billing.overdue.deletion_days' => 5]);

        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(6), // 暂停6天，超过5天限制
            'overdue_amount' => 100.0,
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('删除天数：5 天')
            ->expectsOutputToContain('发现 1 个工作空间需要清理')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_respects_command_line_days_option()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(4), // 暂停4天
            'overdue_amount' => 100.0,
        ]);

        // 使用命令行参数指定3天
        $this->artisan('billing:cleanup-overdue-workspaces', [
            '--dry-run' => true,
            '--days' => 3,
        ])
            ->expectsOutputToContain('删除天数：3 天')
            ->expectsOutputToContain('发现 1 个工作空间需要清理')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_dry_run_mode_does_not_delete_workspaces()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => 100.0,
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('预览模式')
            ->expectsOutputToContain('[预览] 将删除工作空间')
            ->assertExitCode(0);

        // 验证工作空间没有被删除
        $this->assertDatabaseHas('workspaces', ['id' => $workspace->id]);
    }

    /** @test */
    public function test_cleanup_disabled_by_configuration()
    {
        config(['billing.cleanup.enabled' => false]);

        $this->artisan('billing:cleanup-overdue-workspaces')
            ->expectsOutputToContain('工作空间清理功能已禁用')
            ->assertExitCode(1);
    }

    /** @test */
    public function test_force_option_bypasses_disabled_configuration()
    {
        config(['billing.cleanup.enabled' => false]);

        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => 100.0,
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', [
            '--dry-run' => true,
            '--force' => true,
        ])
            ->expectsOutputToContain('发现 1 个工作空间需要清理')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_sends_first_warning_notification()
    {
        Event::fake();

        // 创建刚好3天前暂停的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(3)->startOfDay(),
            'overdue_amount' => 50.0,
        ]);

        $this->artisan('billing:send-overdue-warnings')
            ->expectsOutputToContain('发现 1 个工作空间需要发送首次警告')
            ->expectsOutputToContain($workspace->name)
            ->assertExitCode(0);

        Event::assertDispatched(WorkspacePendingDeletion::class, function ($event) use ($workspace) {
            return $event->workspace->id === $workspace->id
                && $event->daysUntilDeletion === 4; // 7 - 3 = 4
        });
    }

    /** @test */
    public function test_sends_final_warning_notification()
    {
        Event::fake();

        // 创建刚好6天前暂停的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(6)->startOfDay(),
            'overdue_amount' => 50.0,
        ]);

        $this->artisan('billing:send-overdue-warnings')
            ->expectsOutputToContain('发现 1 个工作空间需要发送最终警告')
            ->expectsOutputToContain($workspace->name)
            ->assertExitCode(0);

        Event::assertDispatched(WorkspacePendingDeletion::class, function ($event) use ($workspace) {
            return $event->workspace->id === $workspace->id
                && $event->daysUntilDeletion === 1; // 7 - 6 = 1
        });
    }

    /** @test */
    public function test_warning_notifications_dry_run_mode()
    {
        Event::fake();

        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(3)->startOfDay(),
            'overdue_amount' => 50.0,
        ]);

        $this->artisan('billing:send-overdue-warnings', ['--dry-run' => true])
            ->expectsOutputToContain('预览模式')
            ->expectsOutputToContain('[预览] 将发送首次警告')
            ->assertExitCode(0);

        Event::assertNotDispatched(WorkspacePendingDeletion::class);
    }

    /** @test */
    public function test_workspace_auto_deleted_event_dispatched()
    {
        Event::fake();

        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => 100.0,
        ]);

        // 注意：实际测试中，由于没有真实的K8s集群，删除操作可能会失败
        // 但我们可以测试事件分发的逻辑

        $this->artisan('billing:cleanup-overdue-workspaces', [
            '--dry-run' => true,
            '--force' => true,
        ])->assertExitCode(0);

        // 在dry-run模式下不会触发删除事件
        Event::assertNotDispatched(WorkspaceAutoDeleted::class);
    }

    /** @test */
    public function test_batch_size_limits_processing()
    {
        // 创建多个过期工作空间
        for ($i = 0; $i < 5; $i++) {
            Workspace::factory()->create([
                'user_id' => $this->user->id,
                'cluster_id' => $this->cluster->id,
                'status' => Workspace::STATUS_SUSPENDED,
                'suspension_reason' => 'overdue',
                'suspended_at' => now()->subDays(8),
                'overdue_amount' => 100.0,
            ]);
        }

        // 设置批处理大小为3
        $this->artisan('billing:cleanup-overdue-workspaces', [
            '--dry-run' => true,
            '--batch-size' => 3,
        ])
            ->expectsOutputToContain('批处理大小：3')
            ->expectsOutputToContain('发现 3 个工作空间需要清理')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_ignores_workspaces_without_overdue_amount()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => null, // 没有欠费金额
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('没有找到需要清理的工作空间')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_ignores_workspaces_with_zero_overdue_amount()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => 0.0, // 欠费金额为0
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('没有找到需要清理的工作空间')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_displays_workspace_information_correctly()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-cleanup-workspace',
            'status' => Workspace::STATUS_SUSPENDED,
            'suspension_reason' => 'overdue',
            'suspended_at' => now()->subDays(8),
            'overdue_amount' => 123.45,
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('test-cleanup-workspace')
            ->expectsOutputToContain($this->user->name)
            ->expectsOutputToContain($this->cluster->name)
            ->expectsOutputToContain('¥123.45')
            ->expectsOutputToContain('8 天')
            ->assertExitCode(0);
    }

    /** @test */
    public function test_configuration_values_are_respected()
    {
        // 修改配置值
        config([
            'billing.overdue.deletion_days' => 10,
            'billing.overdue.notification.first_warning_days' => 5,
            'billing.overdue.notification.final_warning_days' => 8,
            'billing.cleanup.batch_size' => 25,
        ]);

        $this->artisan('billing:cleanup-overdue-workspaces', ['--dry-run' => true])
            ->expectsOutputToContain('删除天数：10 天')
            ->expectsOutputToContain('批处理大小：25')
            ->assertExitCode(0);

        $this->artisan('billing:send-overdue-warnings', ['--dry-run' => true])
            ->expectsOutputToContain('删除天数：10 天')
            ->expectsOutputToContain('首次警告：欠费 5 天后')
            ->expectsOutputToContain('最终警告：欠费 8 天后')
            ->assertExitCode(0);
    }
}
