<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use App\Service\ImageValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ImageValidationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->active()->create(['user_id' => $this->user->id]);
        $this->user->update(['current_workspace_id' => $this->workspace->id]);
    }

    public function test_image_validation_service_initialization()
    {
        $service = new ImageValidationService($this->workspace);
        $this->assertInstanceOf(ImageValidationService::class, $service);
    }

    public function test_deployment_request_validates_image_names()
    {
        $this->actingAs($this->user);

        // 测试无效的镜像名称格式
        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => '', // 空镜像名
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 应该因为镜像名为空而验证失败（或者工作空间问题返回400）
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(422), // 验证失败
                $this->equalTo(400)  // 工作空间问题
            )
        );

        if ($response->status() === 422) {
            $this->assertStringContainsString('image', $response->getContent());
        }
    }

    public function test_statefulset_request_validates_image_names()
    {
        $this->actingAs($this->user);

        $statefulSetData = [
            'name' => 'test-statefulset',
            'replicas' => 1,
            'service_name' => 'test-service',
            'containers' => [
                [
                    'name' => 'database',
                    'image' => '', // 空镜像名
                    'ports' => [
                        [
                            'container_port' => 5432,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 1000,
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/statefulsets', $statefulSetData);

        // 应该因为镜像名为空而验证失败（或者工作空间问题返回400）
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(422), // 验证失败
                $this->equalTo(400)  // 工作空间问题
            )
        );

        if ($response->status() === 422) {
            $this->assertStringContainsString('image', $response->getContent());
        }
    }

    public function test_image_validation_with_non_strict_mode()
    {
        // 设置非严格模式
        config(['k8s.imageValidation.strict_mode' => false]);

        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 在非严格模式下，即使镜像验证失败也不应该阻止部署
        // 注意：实际结果依赖于其他验证逻辑和 K8s 连接
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201), // 成功创建
                $this->equalTo(422), // 其他验证失败
                $this->equalTo(500), // 服务器错误（如 K8s 连接问题）
                $this->equalTo(400)  // 工作空间问题
            )
        );
    }

    public function test_image_validation_preserves_other_validation_errors()
    {
        $this->actingAs($this->user);

        $deploymentData = [
            'name' => '', // 无效的名称
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 应该因为名称验证失败（或者工作空间问题返回400）
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(422), // 验证失败
                $this->equalTo(400)  // 工作空间问题
            )
        );

        if ($response->status() === 422) {
            $this->assertStringContainsString('name', $response->getContent());
        }
    }

    public function test_image_validation_handles_image_pull_secrets()
    {
        config(['k8s.imageValidation.strict_mode' => false]);

        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'private-app',
                    'image' => 'private-registry.com/myapp:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
            'image_pull_secrets' => ['my-registry-secret'], // 添加镜像拉取密钥
        ];

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 测试镜像拉取密钥是否被正确处理
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201),
                $this->equalTo(422),
                $this->equalTo(500),
                $this->equalTo(400)  // 工作空间问题
            )
        );
    }

    public function test_image_size_configuration_is_respected()
    {
        // 测试新的 K8s 镜像验证配置是否正确加载
        $this->assertIsBool(config('k8s.imageValidation.enabled'));
        $this->assertIsBool(config('k8s.imageValidation.strict_mode'));
        $this->assertIsInt(config('k8s.imageValidation.max_size_mb'));
        $this->assertIsInt(config('k8s.imageValidation.timeout_seconds'));
        $this->assertIsBool(config('k8s.imageValidation.skip_private_images'));
        $this->assertIsArray(config('k8s.imageValidation.allowed_registries'));
        $this->assertIsArray(config('k8s.imageValidation.blocked_registries'));

        // 测试代理配置仍然可用
        $this->assertIsBool(config('app.proxy.enabled'));
        $this->assertIsString(config('app.proxy.http'));
        $this->assertIsString(config('app.proxy.https'));
    }
}
