<?php

namespace Tests\Feature;

use App\Models\TopUpRecord;
use App\Models\User;
use App\Service\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BalanceSystemTest extends TestCase
{
    use RefreshDatabase;

    protected BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->balanceService = app(BalanceService::class);
    }

    /** @test */
    public function user_can_have_balance_added()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        $record = $this->balanceService->addBalance(
            $user,
            100.00,
            TopUpRecord::PAYMENT_METHOD_MANUAL,
            'TEST123',
            ['remark' => '测试充值']
        );

        $this->assertInstanceOf(TopUpRecord::class, $record);
        $this->assertEquals(100.00, $record->amount);
        $this->assertEquals(100.00, $record->remaining_amount);
        $this->assertEquals(TopUpRecord::STATUS_COMPLETED, $record->status);
        $this->assertEquals('TEST123', $record->transaction_number);

        // 检查用户余额是否更新
        $user->refresh();
        $this->assertBalanceEquals(100.00, $user->current_balance);
    }

    /** @test */
    public function user_balance_can_be_deducted()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        // 先充值
        $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 再扣除
        $result = $this->balanceService->deductBalance($user, '30.00000000', '测试扣除');

        $this->assertTrue($result);

        // 检查用户余额
        $user->refresh();
        $this->assertBalanceEquals(70.00, $user->current_balance);

        // 检查充值记录的剩余金额
        $record = $user->topUpRecords()->first();
        $this->assertBalanceEquals(70.00, $record->remaining_amount);
    }

    /** @test */
    public function multiple_top_up_records_are_used_in_fifo_order()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        // 创建多个充值记录
        $record1 = $this->balanceService->addBalance($user, 50.00, TopUpRecord::PAYMENT_METHOD_MANUAL);
        sleep(1); // 确保时间不同
        $record2 = $this->balanceService->addBalance($user, 80.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        $user->refresh();
        $this->assertBalanceEquals(130.00, $user->current_balance);

        // 扣除100元，应该先用完第一个充值记录，再用第二个的50元
        $this->balanceService->deductBalance($user, '100.00000000', '测试FIFO');

        $record1->refresh();
        $record2->refresh();

        $this->assertBalanceEquals(0.00, $record1->remaining_amount); // 第一个记录用完
        $this->assertBalanceEquals(30.00, $record2->remaining_amount); // 第二个记录剩余30

        $user->refresh();
        $this->assertBalanceEquals(30.00, $user->current_balance);
    }

    /** @test */
    public function cannot_deduct_more_than_available_balance()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        $this->balanceService->addBalance($user, 50.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('余额不足');

        $this->balanceService->deductBalance($user, '100.00000000', '超额扣除');
    }

    /** @test */
    public function can_calculate_actual_balance_from_records()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        // 添加多个充值记录
        $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);
        $this->balanceService->addBalance($user, 50.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 扣除一些金额
        $this->balanceService->deductBalance($user, '80.00000000', '测试');

        $actualBalance = $this->balanceService->calculateActualBalance($user);
        $this->assertBalanceEquals(70.00, $actualBalance);

        $user->refresh();
        $this->assertBalanceEquals($actualBalance, $user->current_balance);
    }

    /** @test */
    public function can_sync_user_balance_when_inconsistent()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        // 添加充值记录
        $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 手动修改用户余额造成不一致
        $user->update(['current_balance' => 50.00]);

        // 同步余额
        $updated = $this->balanceService->syncUserBalance($user);

        $this->assertTrue($updated);
        $user->refresh();
        $this->assertBalanceEquals(100.00, $user->current_balance);
    }

    /** @test */
    public function can_refund_completed_record()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        $record = $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 部分退款
        $result = $this->balanceService->refund($record, 30.00, '测试退款');

        $this->assertTrue($result);

        $record->refresh();
        $this->assertBalanceEquals(70.00, $record->remaining_amount);
        $this->assertBalanceEquals(30.00, $record->refund_amount);
        $this->assertEquals(TopUpRecord::STATUS_PARTIAL_REFUNDED, $record->status);

        $user->refresh();
        $this->assertBalanceEquals(70.00, $user->current_balance);
    }

    /** @test */
    public function can_full_refund_record()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        $record = $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 全额退款
        $result = $this->balanceService->refund($record, null, '全额退款');

        $this->assertTrue($result);

        $record->refresh();
        $this->assertBalanceEquals(0.00, $record->remaining_amount);
        $this->assertBalanceEquals(100.00, $record->refund_amount);
        $this->assertEquals(TopUpRecord::STATUS_REFUNDED, $record->status);
        $this->assertNotNull($record->refunded_at);

        $user->refresh();
        $this->assertBalanceEquals(0.00, $user->current_balance);
    }

    /** @test */
    public function cannot_refund_more_than_remaining_amount()
    {
        $user = User::factory()->create(['current_balance' => 0]);

        $record = $this->balanceService->addBalance($user, 100.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        // 先扣除一些余额
        $this->balanceService->deductBalance($user, '30.00000000', '使用余额');

        $record->refresh();
        $this->assertBalanceEquals(70.00, $record->remaining_amount);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('退款金额不能超过剩余金额');

        $this->balanceService->refund($record, 80.00, '超额退款');
    }

    /** @test */
    public function transaction_number_is_unique()
    {
        $user = User::factory()->create();

        $record1 = $this->balanceService->addBalance($user, 50.00, TopUpRecord::PAYMENT_METHOD_MANUAL);
        $record2 = $this->balanceService->addBalance($user, 60.00, TopUpRecord::PAYMENT_METHOD_MANUAL);

        $this->assertNotEquals($record1->transaction_number, $record2->transaction_number);
    }

    private function assertBalanceEquals($expected, $actual, string $message = ''): void
    {
        $this->assertEquals(
            number_format($expected, 8, '.', ''),
            number_format($actual, 8, '.', ''),
            $message
        );
    }
}
