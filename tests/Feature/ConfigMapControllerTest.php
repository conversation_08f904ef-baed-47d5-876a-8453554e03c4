<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ConfigMapControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建测试集群
        $this->cluster = Cluster::factory()->create();

        // 创建测试工作空间
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => 'active',
        ]);

        // 设置当前工作空间
        $this->user->update(['current_workspace_id' => $this->workspace->id]);
    }

    public function test_can_access_configmaps_index_with_workspace()
    {
        $response = $this->actingAs($this->user)
            ->get(route('configmaps.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('ConfigMaps/Index'));
    }

    public function test_cannot_access_configmaps_without_current_workspace()
    {
        // 移除当前工作空间
        $this->user->update(['current_workspace_id' => null]);

        $response = $this->actingAs($this->user)
            ->get(route('configmaps.index'));

        $response->assertRedirect(route('workspaces.index'));
        $response->assertSessionHas('message', '请先选择一个工作空间。');
    }

    public function test_can_access_configmaps_create_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('configmaps.create'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('ConfigMaps/Create'));
    }

    public function test_unauthenticated_user_cannot_access_configmaps()
    {
        $response = $this->get(route('configmaps.index'));

        $response->assertRedirect(route('login'));
    }
}
