<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 创建测试用的 kubeconfig 文件
    $this->kubeconfigContent = <<<'YAML'
apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t
    server: https://test-cluster.example.com:6443
  name: test-cluster
contexts:
- context:
    cluster: test-cluster
    user: test-user
  name: test-context
current-context: test-context
kind: Config
preferences: {}
users:
- name: test-user
  user:
    token: test-token-12345
YAML;

    $this->kubeconfigFile = storage_path('test-kubeconfig.yaml');
    file_put_contents($this->kubeconfigFile, $this->kubeconfigContent);
});

afterEach(function () {
    // 清理测试文件
    if (file_exists($this->kubeconfigFile)) {
        unlink($this->kubeconfigFile);
    }
});

test('cluster:add command creates new cluster successfully', function () {
    $this->artisan('cluster:add', [
        'name' => 'test-cluster',
        'kubeconfig-file' => $this->kubeconfigFile,
    ])
        ->expectsOutput('集群 test-cluster 添加成功！')
        ->assertExitCode(0);

    $this->assertDatabaseHas('clusters', [
        'name' => 'test-cluster',
        'server_url' => 'https://test-cluster.example.com:6443',
        'auth_type' => 'token',
    ]);
});

test('cluster:add command fails when cluster name already exists', function () {
    Cluster::factory()->create(['name' => 'existing-cluster']);

    $this->artisan('cluster:add', [
        'name' => 'existing-cluster',
        'kubeconfig-file' => $this->kubeconfigFile,
    ])
        ->expectsOutput('集群名称 existing-cluster 已存在')
        ->assertExitCode(1);
});

test('cluster:add command fails when kubeconfig file does not exist', function () {
    $this->artisan('cluster:add', [
        'name' => 'test-cluster',
        'kubeconfig-file' => '/nonexistent/file.yaml',
    ])
        ->expectsOutput('kubeconfig 文件不存在: /nonexistent/file.yaml')
        ->assertExitCode(1);
});

test('cluster:list command shows all clusters', function () {
    $cluster1 = Cluster::factory()->create([
        'name' => 'cluster-1',
        'auth_type' => 'token',
    ]);
    $cluster2 = Cluster::factory()->create([
        'name' => 'cluster-2',
        'auth_type' => 'certificate',
    ]);

    $this->artisan('cluster:list')
        ->expectsOutput('找到 2 个集群：')
        ->expectsTable(
            ['ID', '名称', '服务器地址', '认证类型', '工作空间数量', '创建时间', '更新时间'],
            [
                [
                    $cluster1->id,
                    $cluster1->name,
                    $cluster1->server_url,
                    $cluster1->auth_type,
                    0,
                    $cluster1->created_at->format('Y-m-d H:i:s'),
                    $cluster1->updated_at->format('Y-m-d H:i:s'),
                ],
                [
                    $cluster2->id,
                    $cluster2->name,
                    $cluster2->server_url,
                    $cluster2->auth_type,
                    0,
                    $cluster2->created_at->format('Y-m-d H:i:s'),
                    $cluster2->updated_at->format('Y-m-d H:i:s'),
                ],
            ]
        )
        ->assertExitCode(0);
});

test('cluster:list command filters by name', function () {
    Cluster::factory()->create(['name' => 'production-cluster']);
    Cluster::factory()->create(['name' => 'staging-cluster']);

    $this->artisan('cluster:list', ['--name' => 'production'])
        ->expectsOutput('找到 1 个集群：')
        ->assertExitCode(0);
});

test('cluster:list command filters by auth type', function () {
    Cluster::factory()->create(['auth_type' => 'token']);
    Cluster::factory()->create(['auth_type' => 'certificate']);

    $this->artisan('cluster:list', ['--auth-type' => 'token'])
        ->expectsOutput('找到 1 个集群：')
        ->assertExitCode(0);
});

test('cluster:list command outputs json format', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:list', ['--format' => 'json'])
        ->expectsOutputToContain('"name": "test-cluster"')
        ->assertExitCode(0);
});

test('cluster:show command displays cluster details', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:show', ['name' => 'test-cluster'])
        ->expectsOutput('集群详细信息：')
        ->expectsTable(
            ['字段', '值'],
            [
                ['ID', $cluster->id],
                ['名称', $cluster->name],
                ['服务器地址', $cluster->server_url],
                ['认证类型', $cluster->auth_type],
                ['跳过 TLS 验证', '否'],
                ['创建时间', $cluster->created_at->format('Y-m-d H:i:s')],
                ['更新时间', $cluster->updated_at->format('Y-m-d H:i:s')],
            ]
        )
        ->assertExitCode(0);
});

test('cluster:show command fails when cluster does not exist', function () {
    $this->artisan('cluster:show', ['name' => 'nonexistent'])
        ->expectsOutput('集群 nonexistent 不存在')
        ->assertExitCode(1);
});

test('cluster:show command displays associated workspaces', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);
    Workspace::factory()->create([
        'cluster_id' => $cluster->id,
        'user_id' => $user->id,
        'name' => 'test-workspace',
    ]);

    $this->artisan('cluster:show', ['name' => 'test-cluster'])
        ->expectsOutputToContain('关联的工作空间 (1 个)：')
        ->assertExitCode(0);
});

test('cluster:update command updates cluster name', function () {
    $cluster = Cluster::factory()->create(['name' => 'old-name']);

    $this->artisan('cluster:update', [
        'name' => 'old-name',
        '--new-name' => 'new-name',
    ])
        ->expectsConfirmation('确定要更新集群 old-name 吗？', 'yes')
        ->expectsOutput('集群 old-name 更新成功！')
        ->assertExitCode(0);

    $this->assertDatabaseHas('clusters', [
        'id' => $cluster->id,
        'name' => 'new-name',
    ]);
});

test('cluster:update command updates cluster with new kubeconfig', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:update', [
        'name' => 'test-cluster',
        '--kubeconfig-file' => $this->kubeconfigFile,
    ])
        ->expectsConfirmation('确定要更新集群 test-cluster 吗？', 'yes')
        ->expectsOutput('集群 test-cluster 更新成功！')
        ->assertExitCode(0);

    $this->assertDatabaseHas('clusters', [
        'id' => $cluster->id,
        'server_url' => 'https://test-cluster.example.com:6443',
    ]);
});

test('cluster:update command fails when no options provided', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:update', ['name' => 'test-cluster'])
        ->expectsOutput('请提供至少一个更新选项')
        ->assertExitCode(1);
});

test('cluster:update command fails when cluster does not exist', function () {
    $this->artisan('cluster:update', [
        'name' => 'nonexistent',
        '--new-name' => 'new-name',
    ])
        ->expectsOutput('集群 nonexistent 不存在')
        ->assertExitCode(1);
});

test('cluster:delete command deletes cluster successfully', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:delete', ['name' => 'test-cluster'])
        ->expectsConfirmation('确定要删除集群 test-cluster 吗？', 'yes')
        ->expectsOutput('集群 test-cluster 删除成功！')
        ->assertExitCode(0);

    $this->assertDatabaseMissing('clusters', [
        'id' => $cluster->id,
    ]);
});

test('cluster:delete command with force option skips confirmation', function () {
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);

    $this->artisan('cluster:delete', [
        'name' => 'test-cluster',
        '--force' => true,
    ])
        ->expectsOutput('集群 test-cluster 删除成功！')
        ->assertExitCode(0);

    $this->assertDatabaseMissing('clusters', [
        'id' => $cluster->id,
    ]);
});

test('cluster:delete command warns about associated workspaces', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create(['name' => 'test-cluster']);
    Workspace::factory()->create([
        'cluster_id' => $cluster->id,
        'user_id' => $user->id,
    ]);

    $this->artisan('cluster:delete', ['name' => 'test-cluster'])
        ->expectsOutput('警告：该集群有 1 个关联的工作空间')
        ->expectsConfirmation('确定要删除集群 test-cluster 吗？', 'no')
        ->expectsOutput('操作已取消')
        ->assertExitCode(0);

    $this->assertDatabaseHas('clusters', [
        'id' => $cluster->id,
    ]);
});

test('cluster:delete command fails when cluster does not exist', function () {
    $this->artisan('cluster:delete', ['name' => 'nonexistent'])
        ->expectsOutput('集群 nonexistent 不存在')
        ->assertExitCode(1);
});
