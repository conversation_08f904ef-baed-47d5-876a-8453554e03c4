<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->cluster = Cluster::factory()->create();
    $this->workspace = Workspace::factory()->create([
        'user_id' => $this->user->id,
        'cluster_id' => $this->cluster->id,
        'status' => 'active',
    ]);
    $this->user->update(['current_workspace_id' => $this->workspace->id]);
});

test('can create generic secret via api', function () {
    $response = $this->actingAs($this->user)
        ->postJson('/api/secrets/generic', [
            'name' => 'test-secret',
            'data' => [
                'username' => 'testuser',
                'password' => 'testpass',
            ],
        ]);

    // 如果没有真实的 K8s 集群，这会失败，但我们可以检查是否通过了验证
    // 和服务调用
    expect($response->status())->toBeIn([200, 201, 500]); // 500 是因为没有真实的 K8s 集群
});

test('cannot create secret without workspace', function () {
    $user = User::factory()->create(); // 没有当前工作空间的用户

    $response = $this->actingAs($user)
        ->postJson('/api/secrets/generic', [
            'name' => 'test-secret',
            'data' => [
                'username' => 'testuser',
                'password' => 'testpass',
            ],
        ]);

    $response->assertStatus(422); // 中间件会返回 422 状态码和 "请先选择工作空间" 消息
});

test('validates secret data', function () {
    $response = $this->actingAs($this->user)
        ->postJson('/api/secrets/generic', [
            'name' => '', // 空名称应该失败
            'data' => [],
        ]);

    $response->assertStatus(422); // 验证失败
});
