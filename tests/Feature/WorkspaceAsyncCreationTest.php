<?php

namespace Tests\Feature;

use App\Jobs\ProcessTaskJob;
use App\Models\Cluster;
use App\Models\Task;
use App\Models\User;
use App\Models\Workspace;
use App\Service\WorkspaceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WorkspaceAsyncCreationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected WorkspaceService $workspaceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->token()->create();
        $this->workspaceService = app(WorkspaceService::class);
    }

    public function test_workspace_creation_starts_async_task(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_PENDING,
        ]);

        // 验证任务已创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'workspace_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_CREATE,
            'status' => Task::STATUS_PENDING,
        ]);

        // 验证 ProcessTaskJob 已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_CREATE;
        });
    }

    public function test_workspace_creation_task_execution(): void
    {
        // 禁用队列以便同步测试
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_PENDING,
        ]);

        $task = $workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_CREATE)->first();
        $this->assertNotNull($task);

        // 模拟任务执行（注意：这将失败因为没有真实的 K8s 集群）
        try {
            $workspace->handleTask($task);
        } catch (\Exception $e) {
            // 预期会失败，因为没有真实的 K8s 集群
            $this->assertStringContainsString('创建 Namespace 失败', $e->getMessage());
        }

        // 验证任务状态
        $task->refresh();
        $this->assertEquals(Task::STATUS_FAILED, $task->status);

        // 验证工作空间状态
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_FAILED, $workspace->status);
    }

    public function test_workspace_deletion_starts_async_task(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 清空之前的队列记录
        Queue::fake();

        // 使用新的删除流程：手动启动删除任务
        $workspace->update(['status' => Workspace::STATUS_DELETING]);
        $workspace->startDeletionTask();

        // 验证删除任务已创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_DELETE,
            'status' => Task::STATUS_PENDING,
            'max_attempts' => 0, // 现在是无限重试
        ]);

        // 验证 ProcessTaskJob 已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_DELETE;
        });
    }

    public function test_workspace_retry_creation(): void
    {
        Queue::fake();

        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_FAILED,
            'suspension_reason' => 'Namespace 创建失败: Test error',
        ]);

        // 清空之前的队列记录
        Queue::fake();

        $workspace->retryCreateNamespace();

        // 验证状态已更新
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_PENDING, $workspace->status);
        $this->assertNull($workspace->suspension_reason);

        // 验证新的创建任务已派发
        Queue::assertPushed(ProcessTaskJob::class, function ($job) use ($workspace) {
            return $job->task->taskable_id === $workspace->id &&
                   $job->task->type === Workspace::TASK_WORKSPACE_CREATE;
        });
    }

    public function test_workspace_task_retry_mechanism(): void
    {
        $workspace = Workspace::create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'ns-test123',
            'status' => Workspace::STATUS_PENDING,
        ]);

        $task = $workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_CREATE)->first();
        $this->assertNotNull($task);

        // 模拟任务失败但可以重试
        $task->update([
            'status' => Task::STATUS_FAILED,
            'attempts' => 1,
            'max_attempts' => 3,
            'error_message' => 'Test error',
        ]);

        $this->assertTrue($task->canRetry());

        // 重置任务
        $task->reset();
        $this->assertEquals(Task::STATUS_PENDING, $task->status);
        $this->assertNull($task->error_message);
        $this->assertNull($task->started_at);
        $this->assertNull($task->completed_at);
        $this->assertEquals(1, $task->attempts); // attempts 不应该被重置
    }

    public function test_workspace_service_creates_workspace_with_async_task(): void
    {
        Queue::fake();

        $workspaceService = app(WorkspaceService::class);

        $workspace = $workspaceService->createWorkspace([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
        ]);

        // 验证工作空间已创建
        $this->assertDatabaseHas('workspaces', [
            'id' => $workspace->id,
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'status' => Workspace::STATUS_PENDING,
        ]);

        // 验证 namespace 格式正确
        $this->assertMatchesRegularExpression('/^ns-[a-z0-9]{8}$/', $workspace->namespace);

        // 验证任务已创建并派发
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_CREATE,
            'status' => Task::STATUS_PENDING,
        ]);

        Queue::assertPushed(ProcessTaskJob::class);
    }

    public function test_status_constants_are_defined(): void
    {
        $this->assertEquals('pending', Workspace::STATUS_PENDING);
        $this->assertEquals('creating', Workspace::STATUS_CREATING);
        $this->assertEquals('deleting', Workspace::STATUS_DELETING);
        $this->assertEquals('active', Workspace::STATUS_ACTIVE);
        $this->assertEquals('suspended', Workspace::STATUS_SUSPENDED);
        $this->assertEquals('failed', Workspace::STATUS_FAILED);
    }

    public function test_task_type_constants_are_defined(): void
    {
        $this->assertEquals('workspace:create', Workspace::TASK_WORKSPACE_CREATE);
        $this->assertEquals('workspace:delete', Workspace::TASK_WORKSPACE_DELETE);
    }

    public function test_workspace_api_deletion_starts_async_task(): void
    {
        Queue::fake();

        // 创建一个活跃的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 通过 API 删除工作空间
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/workspaces/{$workspace->id}");

        $response->assertOk();

        // 验证工作空间状态变为删除中
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_DELETING, $workspace->status);

        // 验证删除任务被创建
        $this->assertTrue($workspace->tasks()->where('type', Workspace::TASK_WORKSPACE_DELETE)->exists());

        // 验证队列任务被分派
        Queue::assertPushed(ProcessTaskJob::class);

        // 验证工作空间记录还存在（未被立即删除）
        $this->assertDatabaseHas('workspaces', ['id' => $workspace->id]);
    }

    public function test_workspace_deletion_task_supports_infinite_retry(): void
    {
        // 创建一个删除中的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_DELETING,
        ]);

        // 创建删除任务
        $task = $workspace->startDeletionTask();

        // 验证任务设置为无限重试
        $this->assertEquals(0, $task->max_attempts);

        // 新创建的任务状态应该是 pending，不能重试
        $this->assertEquals(Task::STATUS_PENDING, $task->status);
        $this->assertFalse($task->canRetry()); // pending 状态不能重试

        // 模拟任务失败
        $task->markAsFailed('测试失败');
        $this->assertEquals(Task::STATUS_FAILED, $task->status);
        $this->assertTrue($task->canRetry()); // 失败状态且无限重试

        // 即使尝试次数很多，仍然可以重试
        $task->update(['attempts' => 100]);
        $this->assertTrue($task->canRetry());
    }
}
