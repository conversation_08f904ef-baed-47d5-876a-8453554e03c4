<?php

namespace Tests\Feature;

use App\ClusterLabel;
use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\NamespaceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceSecurityTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    private User $user;

    private NamespaceService $namespaceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cluster = Cluster::factory()->create();
        $this->user = User::factory()->create();
        $this->namespaceService = new NamespaceService;
    }

    public function test_workspace_creation_with_security_settings()
    {
        // 创建工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-security-workspace',
            'namespace' => 'test-security-workspace-'.time(),
        ]);

        // 验证工作空间创建成功
        $this->assertDatabaseHas('workspaces', [
            'id' => $workspace->id,
            'name' => 'test-security-workspace',
        ]);

        // 验证 buildDefaultLabels 方法包含安全标签
        $labels = $workspace->buildDefaultLabels($workspace);

        $this->assertArrayHasKey(ClusterLabel::WORKSPACE->value, $labels);
        $this->assertArrayHasKey(ClusterLabel::USER->value, $labels);
        $this->assertArrayHasKey(ClusterLabel::MANAGED_BY->value, $labels);
        $this->assertArrayHasKey(ClusterLabel::PLATFORM->value, $labels);
    }

    public function test_resource_quota_configuration()
    {
        // 验证配置文件中的资源配额设置
        $quotaConfig = config('k8s.workspace.resourceQuota');

        $this->assertIsArray($quotaConfig);
        $this->assertArrayHasKey('memory', $quotaConfig);
        $this->assertArrayHasKey('cpu', $quotaConfig);
        $this->assertArrayHasKey('storage', $quotaConfig);
        $this->assertArrayHasKey('pods', $quotaConfig);

        // 验证默认值
        $this->assertEquals(131072, $quotaConfig['memory']); // 128GB in Mi
        $this->assertEquals(64000, $quotaConfig['cpu']); // 64000m
        $this->assertEquals(1024, $quotaConfig['storage']); // 1TB in Gi
        $this->assertEquals(100, $quotaConfig['pods']);
    }

    public function test_network_policy_configuration()
    {
        // 验证网络策略配置
        $networkConfig = config('k8s.workspace.networkPolicy');

        $this->assertIsArray($networkConfig);
        $this->assertArrayHasKey('enabled', $networkConfig);
        $this->assertArrayHasKey('defaultDenyIngress', $networkConfig);
        $this->assertArrayHasKey('defaultDenyEgress', $networkConfig);

        // 验证默认值
        $this->assertTrue($networkConfig['enabled']);
        $this->assertTrue($networkConfig['defaultDenyIngress']);
        $this->assertFalse($networkConfig['defaultDenyEgress']);
    }

    public function test_pod_security_standards_configuration()
    {
        // 验证 Pod Security Standards 配置
        $podSecurityConfig = config('k8s.podSecurity');

        $this->assertIsArray($podSecurityConfig);
        $this->assertArrayHasKey('enforce', $podSecurityConfig);
        $this->assertArrayHasKey('audit', $podSecurityConfig);
        $this->assertArrayHasKey('warn', $podSecurityConfig);

        // 验证默认值
        $this->assertEquals('privileged', $podSecurityConfig['enforce']);
        $this->assertEquals('baseline', $podSecurityConfig['audit']);
        $this->assertEquals('baseline', $podSecurityConfig['warn']);
    }

    public function test_security_context_defaults()
    {
        // 验证安全上下文默认配置
        $securityContext = config('k8s.defaults.securityContext');

        $this->assertIsArray($securityContext);
        $this->assertArrayHasKey('allowPrivilegeEscalation', $securityContext);
        $this->assertArrayHasKey('runAsNonRoot', $securityContext);
        $this->assertArrayHasKey('capabilities', $securityContext);
        $this->assertArrayHasKey('readOnlyRootFilesystem', $securityContext);

        // 验证安全设置
        $this->assertFalse($securityContext['allowPrivilegeEscalation']);
        $this->assertFalse($securityContext['runAsNonRoot']); // 允许以 root 运行提高兼容性
        $this->assertFalse($securityContext['readOnlyRootFilesystem']); // 允许写入根文件系统

        // 验证能力设置
        $capabilities = $securityContext['capabilities'];
        $this->assertArrayHasKey('drop', $capabilities);
        $this->assertArrayHasKey('add', $capabilities);
        $this->assertContains('ALL', $capabilities['drop']);
        $this->assertContains('NET_BIND_SERVICE', $capabilities['add']);
        $this->assertContains('CHOWN', $capabilities['add']);
    }

    public function test_namespace_service_resource_quota_parsing()
    {
        // 测试资源值解析方法
        $namespaceService = new NamespaceService;

        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($namespaceService);
        $parseMethod = $reflection->getMethod('parseResourceValue');
        $parseMethod->setAccessible(true);

        // 测试不同单位的解析
        $this->assertEquals(1024.0, $parseMethod->invoke($namespaceService, '1024Mi'));
        $this->assertEquals(2048.0, $parseMethod->invoke($namespaceService, '2Gi'));
        $this->assertEquals(500.0, $parseMethod->invoke($namespaceService, '500m'));
        $this->assertEquals(100.0, $parseMethod->invoke($namespaceService, '100'));

        // 测试百分比计算
        $percentageMethod = $reflection->getMethod('calculateUsagePercentage');
        $percentageMethod->setAccessible(true);

        $this->assertEquals(50.0, $percentageMethod->invoke($namespaceService, '512Mi', '1024Mi'));
        $this->assertEquals(25.0, $percentageMethod->invoke($namespaceService, '250m', '1000m'));
        $this->assertEquals(0.0, $percentageMethod->invoke($namespaceService, '0', '1000'));
    }

    public function test_workspace_label_sanitization()
    {
        // 创建包含特殊字符的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test workspace with spaces & symbols!',
            'namespace' => 'test-workspace-'.time(),
        ]);

        $labels = $workspace->buildDefaultLabels($workspace);

        // 验证标签值被正确清理
        $workspaceLabel = $labels[ClusterLabel::WORKSPACE->value];
        $this->assertStringNotContainsString(' ', $workspaceLabel);
        $this->assertStringNotContainsString('&', $workspaceLabel);
        $this->assertStringNotContainsString('!', $workspaceLabel);
        $this->assertMatchesRegularExpression('/^[A-Za-z0-9\-_.]*$/', $workspaceLabel);
        $this->assertLessThanOrEqual(63, strlen($workspaceLabel));
    }

    public function test_workspace_deletion_cleanup()
    {
        // 创建工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-deletion-workspace',
            'namespace' => 'test-deletion-workspace-'.time(),
        ]);

        $workspaceId = $workspace->id;
        $namespace = $workspace->namespace;

        // 删除工作空间
        $workspace->delete();

        // 验证工作空间已从数据库中删除
        $this->assertDatabaseMissing('workspaces', [
            'id' => $workspaceId,
        ]);

        // 注意：在实际测试中，由于我们没有真实的 K8s 集群，
        // namespace 的删除操作会失败，但这是预期的行为
    }

    public function test_workspace_retry_create_namespace()
    {
        // 模拟队列，避免实际执行 Job
        \Illuminate\Support\Facades\Queue::fake();

        // 创建失败状态的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-retry-workspace',
            'namespace' => 'test-retry-workspace-'.time(),
            'status' => Workspace::STATUS_FAILED,
            'suspension_reason' => 'Namespace 创建失败',
        ]);

        // 验证初始状态
        $this->assertEquals(Workspace::STATUS_FAILED, $workspace->status);
        $this->assertEquals('Namespace 创建失败', $workspace->suspension_reason);

        // 重试创建 namespace（这只会重置状态并派发 Task）
        $workspace->retryCreateNamespace();

        // 验证状态被重置
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_PENDING, $workspace->status);
        $this->assertNull($workspace->suspension_reason);

        // 验证 ProcessTaskJob 被派发
        \Illuminate\Support\Facades\Queue::assertPushed(\App\Jobs\ProcessTaskJob::class);
    }
}
