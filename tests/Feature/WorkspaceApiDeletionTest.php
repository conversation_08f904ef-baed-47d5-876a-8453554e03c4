<?php

namespace Tests\Feature;

use App\Jobs\ProcessTaskJob;
use App\Models\Cluster;
use App\Models\Task;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class WorkspaceApiDeletionTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->token()->create();
    }

    public function test_api_deletion_sets_workspace_to_deleting_status(): void
    {
        Queue::fake();

        // 创建一个活跃的工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 通过 API 删除工作空间
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/workspaces/{$workspace->id}");

        $response->assertOk()
            ->assertJson([
                'message' => '工作空间删除已启动，正在清理 Kubernetes 资源',
            ]);

        // 验证工作空间状态变为删除中
        $workspace->refresh();
        $this->assertEquals(Workspace::STATUS_DELETING, $workspace->status);

        // 验证工作空间记录还存在（未被立即删除）
        $this->assertDatabaseHas('workspaces', [
            'id' => $workspace->id,
            'status' => Workspace::STATUS_DELETING,
        ]);

        // 验证删除任务被创建
        $this->assertDatabaseHas('tasks', [
            'taskable_type' => Workspace::class,
            'taskable_id' => $workspace->id,
            'type' => Workspace::TASK_WORKSPACE_DELETE,
            'status' => Task::STATUS_PENDING,
            'max_attempts' => 0, // 无限重试
        ]);

        // 验证队列任务被分派
        Queue::assertPushed(ProcessTaskJob::class);
    }

    public function test_api_deletion_clears_current_workspace_if_needed(): void
    {
        Queue::fake();

        // 创建一个活跃的工作空间并设为当前工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        $this->user->update(['current_workspace_id' => $workspace->id]);

        // 通过 API 删除工作空间
        $response = $this->actingAs($this->user)
            ->deleteJson("/api/workspaces/{$workspace->id}");

        $response->assertOk();

        // 验证用户的当前工作空间被清除
        $this->user->refresh();
        $this->assertNull($this->user->current_workspace_id);
    }

    public function test_workspace_deletion_task_has_infinite_retry(): void
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_DELETING,
        ]);

        // 启动删除任务
        $task = $workspace->startDeletionTask();

        // 验证任务设置为无限重试
        $this->assertEquals(0, $task->max_attempts);
        $this->assertEquals(Task::STATUS_PENDING, $task->status);

        // 模拟任务失败
        $task->markAsFailed('测试失败');
        $this->assertTrue($task->canRetry());

        // 即使尝试次数很多，仍然可以重试
        $task->update(['attempts' => 999]);
        $this->assertTrue($task->canRetry());
    }
}
