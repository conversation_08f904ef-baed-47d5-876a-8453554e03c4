<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TaskStoreIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_access_tasks_api_with_workspace()
    {
        // 创建用户和工作空间
        $user = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $user->id]);

        // 设置当前工作空间
        $user->update(['current_workspace_id' => $workspace->id]);

        // 测试获取任务列表
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/tasks');

        $response->assertStatus(200);
    }

    public function test_user_can_access_task_stats_with_workspace()
    {
        // 创建用户和工作空间
        $user = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $user->id]);

        // 设置当前工作空间
        $user->update(['current_workspace_id' => $workspace->id]);

        // 测试获取任务统计
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/tasks-stats');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total',
            'pending',
            'running',
            'completed',
            'failed',
            'cancelled',
        ]);
    }

    public function test_user_cannot_access_tasks_without_workspace()
    {
        // 创建用户但不设置工作空间
        $user = User::factory()->create();

        // 测试访问任务列表应该被拒绝
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/tasks');

        $response->assertStatus(422);
    }

    public function test_guest_cannot_access_tasks()
    {
        // 测试未认证用户无法访问任务列表
        $response = $this->getJson('/api/tasks');

        $response->assertStatus(401);
    }

    public function test_task_api_endpoints_exist()
    {
        $user = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $user->id]);
        $user->update(['current_workspace_id' => $workspace->id]);

        // 测试所有任务相关的 API 端点
        $endpoints = [
            '/api/tasks',
            '/api/tasks-stats',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->actingAs($user, 'sanctum')
                ->getJson($endpoint);

            $this->assertNotEquals(404, $response->getStatusCode(), "Endpoint {$endpoint} should exist");
        }
    }
}
