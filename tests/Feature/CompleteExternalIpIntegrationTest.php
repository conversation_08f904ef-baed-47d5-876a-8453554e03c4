<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CompleteExternalIpIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户和工作空间
        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create([
            'name' => 'integration-test-cluster',
        ]);
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);
        $this->user->setCurrentWorkspace($this->workspace);
    }

    public function test_complete_external_ip_management_workflow()
    {
        // 1. 设置 Ingress 外部 IP
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'integration-test-cluster',
            'external_ips' => ['**************', '**************'],
        ])->assertExitCode(0);

        // 2. 验证 Ingress 外部 IP 设置
        $this->cluster->refresh();
        $ingressIps = $this->cluster->getSetting('ingress_external_ips');
        $this->assertEquals(['**************', '**************'], $ingressIps);

        // 3. 测试获取 Ingress 外部 IP 的 API
        $response = $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/ingress-external-ips");

        $response->assertOk()
            ->assertJson(['**************', '**************']);

        // 4. 测试获取节点信息的 API（期望失败，因为没有真实的 K8s 集群）
        $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/nodes")
            ->assertStatus(500);

        // 5. 测试 Ingress 页面数据传递
        $response = $this->actingAs($this->user)
            ->get(route('ingresses.index'));

        $response->assertOk();

        // 验证 props 中包含集群信息
        $props = $response->getOriginalContent()->getData()['page']['props'];
        $this->assertArrayHasKey('cluster', $props);
        $this->assertEquals($this->cluster->id, $props['cluster']['id']);
        $this->assertEquals(['**************', '**************'], $props['cluster']['ingress_external_ips']);

        // 6. 测试清空 Ingress 外部 IP
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'integration-test-cluster',
            '--clear' => true,
        ])->assertExitCode(0);

        // 7. 验证已清空
        $this->cluster->refresh();
        $ingressIps = $this->cluster->getSetting('ingress_external_ips', []);
        $this->assertEquals([], $ingressIps);

        // 8. 测试清空后的 API 响应
        $this->actingAs($this->user)
            ->getJson("/api/clusters/{$this->cluster->id}/ingress-external-ips")
            ->assertStatus(404);
    }

    public function test_workspace_resource_includes_cluster_id()
    {
        // 测试 WorkspaceResource 是否包含 cluster_id
        $response = $this->actingAs($this->user)
            ->getJson('/api/workspaces');

        $response->assertOk();

        $responseData = $response->json();

        // API 返回的是 WorkspaceResource collection，有 data 包装
        $workspaces = $responseData['data'];

        $this->assertNotEmpty($workspaces, 'No workspaces returned');

        $workspace = $workspaces[0];
        $this->assertArrayHasKey('cluster_id', $workspace);
        $this->assertEquals($this->cluster->id, $workspace['cluster_id']);
    }

    public function test_services_page_loads_with_workspace_data()
    {
        // 测试服务页面能正确加载工作空间数据
        $response = $this->actingAs($this->user)
            ->get(route('services.index'));

        $response->assertOk();

        // 验证 props 中包含工作空间信息
        $props = $response->getOriginalContent()->getData()['page']['props'];
        $this->assertArrayHasKey('workspace', $props);

        // WorkspaceResource 被包装在 data 键中
        $workspace = $props['workspace']['data'];

        $this->assertArrayHasKey('cluster_id', $workspace);
        $this->assertEquals($this->cluster->id, $workspace['cluster_id']);
    }

    public function test_invalid_operations()
    {
        // 测试无效的 IP 地址
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'integration-test-cluster',
            'external_ips' => ['invalid-ip-address'],
        ])->assertExitCode(1);

        // 测试不存在的集群
        $this->artisan('cluster:set-ingress-external-ips', [
            'cluster_name' => 'nonexistent-cluster',
            'external_ips' => ['***********'],
        ])->assertExitCode(1);

        // 测试未认证的 API 访问
        $this->getJson("/api/clusters/{$this->cluster->id}/ingress-external-ips")
            ->assertStatus(401);
    }
}
