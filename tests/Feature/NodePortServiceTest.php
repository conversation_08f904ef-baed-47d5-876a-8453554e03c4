<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NodePortServiceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cluster = Cluster::factory()->create();
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
        ]);

        $this->user->setCurrentWorkspace($this->workspace);
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_create_nodeport_service()
    {
        $serviceData = [
            'name' => 'test-nodeport-service',
            'type' => 'NodePort',
            'target_workload_type' => 'Deployment',
            'target_workload_name' => 'test-app',
            'ports' => [
                [
                    'name' => 'http',
                    'port' => 80,
                    'target_port' => 8080,
                    'protocol' => 'TCP',
                ],
            ],
            'session_affinity' => 'None',
            'external_traffic_policy' => 'Cluster',
        ];

        // Mock Kubernetes API calls
        $this->mockKubernetesApi();

        $response = $this->postJson('/api/services', $serviceData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'name',
            'type',
            'ports',
            'selector',
        ]);

        $responseData = $response->json();
        $this->assertEquals('NodePort', $responseData['type']);
        $this->assertEquals('test-nodeport-service', $responseData['name']);
    }

    /** @test */
    public function it_validates_nodeport_service_type()
    {
        $serviceData = [
            'name' => 'test-service',
            'type' => 'InvalidType',
            'target_workload_type' => 'Deployment',
            'target_workload_name' => 'test-app',
            'ports' => [
                [
                    'port' => 80,
                    'target_port' => 8080,
                ],
            ],
        ];

        $response = $this->postJson('/api/services', $serviceData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['type']);
        $response->assertJsonFragment([
            'type' => ['Service 类型只能是 ClusterIP、NodePort 或 LoadBalancer'],
        ]);
    }

    /** @test */
    public function it_supports_external_traffic_policy_for_nodeport()
    {
        $serviceData = [
            'name' => 'test-nodeport-local',
            'type' => 'NodePort',
            'target_workload_type' => 'Deployment',
            'target_workload_name' => 'test-app',
            'ports' => [
                [
                    'port' => 80,
                    'target_port' => 8080,
                ],
            ],
            'external_traffic_policy' => 'Local',
        ];

        // Mock Kubernetes API calls
        $this->mockKubernetesApi();

        $response = $this->postJson('/api/services', $serviceData);

        $response->assertStatus(201);
    }

    /** @test */
    public function nodeport_service_model_methods_work_correctly()
    {
        $service = \App\Models\Service::create([
            'workspace_id' => $this->workspace->id,
            'name' => 'test-nodeport',
            'type' => 'NodePort',
            'ports' => [['port' => 80, 'target_port' => 8080]],
            'selector' => ['app' => 'test'],
            'status' => 'active',
        ]);

        $this->assertTrue($service->isNodePort());
        $this->assertFalse($service->isClusterIp());
        $this->assertFalse($service->isLoadBalancer());
        $this->assertTrue($service->hasExternalAccess());
        $this->assertFalse($service->needsExternalIp());
        $this->assertTrue($service->supportsLocalTrafficPolicy());
    }

    protected function mockKubernetesApi(): void
    {
        // Mock deployment existence check
        \Http::fake([
            "*/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/test-app" => \Http::response([
                'metadata' => ['name' => 'test-app'],
                'spec' => ['replicas' => 1],
            ]),
            "*/api/v1/namespaces/{$this->workspace->namespace}/services" => \Http::response([
                'metadata' => [
                    'name' => 'test-nodeport-service',
                    'namespace' => $this->workspace->namespace,
                ],
                'spec' => [
                    'type' => 'NodePort',
                    'ports' => [
                        [
                            'name' => 'http',
                            'port' => 80,
                            'targetPort' => 8080,
                            'protocol' => 'TCP',
                            'nodePort' => 30080,
                        ],
                    ],
                    'selector' => ['app' => 'test-app'],
                ],
            ]),
        ]);
    }
}
