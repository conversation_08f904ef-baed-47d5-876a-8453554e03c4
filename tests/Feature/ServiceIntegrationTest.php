<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use App\Models\Service;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IpPoolService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Cluster $cluster;

    protected Workspace $workspace;

    protected IpPool $rangePool;

    protected IpPool $singlePool;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建测试集群
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
        ]);

        // 创建测试工作空间
        $this->workspace = Workspace::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-workspace',
            'namespace' => 'test-workspace-'.str()->random(8),
        ]);

        // 设置当前工作空间
        $this->user->update(['current_workspace_id' => $this->workspace->id]);

        // 创建 IP 池范围 (**************-************** 用于测试)
        $this->rangePool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-range-pool',
            'allocation_strategy' => 'least_used',
            'description' => '测试范围 IP 池',
        ]);

        // 添加范围内的 IP
        for ($i = 150; $i <= 152; $i++) {
            PoolIp::factory()->create([
                'ip_pool_id' => $this->rangePool->id,
                'ip_address' => "192.168.81.{$i}",
                'port_range_start' => 20000,
                'port_range_end' => 20002, // 只允许 3 个端口用于测试
                'usage_count' => 0,
            ]);
        }

        // 创建单 IP 池 (用于共享测试)
        $this->singlePool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-single-pool',
            'allocation_strategy' => 'least_used',
            'description' => '测试单个 IP 池',
        ]);

        // 添加单个 IP
        PoolIp::factory()->create([
            'ip_pool_id' => $this->singlePool->id,
            'ip_address' => '**************',
            'port_range_start' => 20000,
            'port_range_end' => 20002, // 只允许 3 个端口用于测试
            'usage_count' => 0,
        ]);
    }

    /** @test */
    public function it_can_allocate_ips_using_least_used_strategy(): void
    {
        $ipPoolService = app(IpPoolService::class);

        // 第一次分配应该返回第一个 IP
        $firstIp = $ipPoolService->allocateIp($this->rangePool);
        $this->assertNotNull($firstIp);
        $this->assertEquals('**************', $firstIp->ip_address);

        // 增加第一个 IP 的使用次数
        $firstIp->increment('usage_count', 2);

        // 第二次分配应该返回使用次数较少的 IP
        $secondIp = $ipPoolService->allocateIp($this->rangePool);
        $this->assertNotNull($secondIp);
        $this->assertEquals('**************', $secondIp->ip_address);

        // 第三次分配应该仍然返回使用次数最少的 IP
        $thirdIp = $ipPoolService->allocateIp($this->rangePool);
        $this->assertNotNull($thirdIp);
        $this->assertEquals('**************', $thirdIp->ip_address); // 因为 151 和 152 都是 0 次使用
    }

    /** @test */
    public function it_can_allocate_ports_correctly(): void
    {
        $ipPoolService = app(IpPoolService::class);
        $poolIp = $this->rangePool->poolIps->first();

        // 分配第一个端口
        $port1 = $ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);
        $this->assertEquals(20000, $port1);

        // 分配第二个端口
        $port2 = $ipPoolService->allocatePort($poolIp, 'service-2', $this->workspace->namespace);
        $this->assertEquals(20001, $port2);

        // 分配第三个端口
        $port3 = $ipPoolService->allocatePort($poolIp, 'service-3', $this->workspace->namespace);
        $this->assertEquals(20002, $port3);

        // 验证端口分配记录
        $allocations = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('status', 'allocated')
            ->orderBy('port')
            ->get();

        $this->assertCount(3, $allocations);
        $this->assertEquals(['service-1', 'service-2', 'service-3'], $allocations->pluck('service_name')->toArray());
        $this->assertEquals([20000, 20001, 20002], $allocations->pluck('port')->toArray());

        // 验证 IP 使用次数增加
        $poolIp->refresh();
        $this->assertEquals(3, $poolIp->usage_count);
    }

    /** @test */
    public function it_prevents_port_exhaustion(): void
    {
        $ipPoolService = app(IpPoolService::class);
        $poolIp = $this->rangePool->poolIps->first();

        // 分配所有可用端口
        $ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);
        $ipPoolService->allocatePort($poolIp, 'service-2', $this->workspace->namespace);
        $ipPoolService->allocatePort($poolIp, 'service-3', $this->workspace->namespace);

        // 尝试分配第四个端口应该失败
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('没有可用端口');
        $ipPoolService->allocatePort($poolIp, 'service-4', $this->workspace->namespace);
    }

    /** @test */
    public function it_can_release_ports_correctly(): void
    {
        $ipPoolService = app(IpPoolService::class);
        $poolIp = $this->rangePool->poolIps->first();

        // 分配端口
        $port = $ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);
        $this->assertEquals(20000, $port);

        // 验证分配记录存在
        $allocation = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('service_name', 'service-1')
            ->where('status', 'allocated')
            ->first();
        $this->assertNotNull($allocation);

        // 释放端口
        $ipPoolService->releasePort('service-1', $this->workspace->namespace);

        // 验证端口已释放
        $allocation->refresh();
        $this->assertEquals('released', $allocation->status);
        $this->assertNotNull($allocation->released_at);
    }

    /** @test */
    public function it_handles_shared_ip_allocation(): void
    {
        $ipPoolService = app(IpPoolService::class);
        $singleIp = $this->singlePool->poolIps->first();

        // 在同一个 IP 上分配多个端口（共享 IP 模式）
        $port1 = $ipPoolService->allocatePort($singleIp, 'service-1', $this->workspace->namespace);
        $port2 = $ipPoolService->allocatePort($singleIp, 'service-2', $this->workspace->namespace);
        $port3 = $ipPoolService->allocatePort($singleIp, 'service-3', $this->workspace->namespace);

        // 验证所有端口都分配在同一个 IP 上
        $allocations = PortAllocation::where('pool_ip_id', $singleIp->id)
            ->where('status', 'allocated')
            ->get();

        $this->assertCount(3, $allocations);
        $this->assertEquals([20000, 20001, 20002], $allocations->pluck('port')->sort()->values()->toArray());

        // 验证所有服务都使用同一个 IP
        foreach ($allocations as $allocation) {
            $this->assertEquals($singleIp->ip_address, $allocation->poolIp->ip_address);
        }
    }

    /** @test */
    public function it_prevents_duplicate_port_allocation(): void
    {
        $ipPoolService = app(IpPoolService::class);
        $poolIp = $this->rangePool->poolIps->first();

        // 为同一个服务分配端口两次
        $port1 = $ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);
        $port2 = $ipPoolService->allocatePort($poolIp, 'service-1', $this->workspace->namespace);

        // 应该返回相同的端口
        $this->assertEquals($port1, $port2);

        // 验证只有一个分配记录
        $allocations = PortAllocation::where('pool_ip_id', $poolIp->id)
            ->where('service_name', 'service-1')
            ->where('status', 'allocated')
            ->get();

        $this->assertCount(1, $allocations);
    }

    /** @test */
    public function it_calculates_pool_stats_correctly(): void
    {
        $ipPoolService = app(IpPoolService::class);

        // 初始统计
        $stats = $ipPoolService->getPoolStats($this->rangePool);
        $this->assertEquals(3, $stats['total_ips']);
        $this->assertEquals(3, $stats['active_ips']);
        $this->assertEquals(0, $stats['inactive_ips']);
        $this->assertEquals(9, $stats['total_ports']); // 3 IPs * 3 ports each
        $this->assertEquals(0, $stats['allocated_ports']);
        $this->assertEquals(9, $stats['available_ports']);
        $this->assertEquals(0, $stats['utilization_rate']);

        // 分配一些端口
        $poolIp1 = $this->rangePool->poolIps->first();
        $poolIp2 = $this->rangePool->poolIps->skip(1)->first();

        $ipPoolService->allocatePort($poolIp1, 'service-1', $this->workspace->namespace);
        $ipPoolService->allocatePort($poolIp1, 'service-2', $this->workspace->namespace);
        $ipPoolService->allocatePort($poolIp2, 'service-3', $this->workspace->namespace);

        // 重新计算统计
        $stats = $ipPoolService->getPoolStats($this->rangePool);
        $this->assertEquals(3, $stats['allocated_ports']);
        $this->assertEquals(6, $stats['available_ports']);
        $this->assertEquals(33.33, $stats['utilization_rate']); // 3/9 * 100
    }

    /** @test */
    public function it_handles_ip_pool_strategies(): void
    {
        $ipPoolService = app(IpPoolService::class);

        // 测试轮询策略
        $this->rangePool->update(['allocation_strategy' => 'round_robin']);
        $ip1 = $ipPoolService->allocateIp($this->rangePool);
        $this->assertNotNull($ip1);

        // 测试随机策略
        $this->rangePool->update(['allocation_strategy' => 'random']);
        $ip2 = $ipPoolService->allocateIp($this->rangePool);
        $this->assertNotNull($ip2);

        // 测试无效策略 - 直接在服务中测试
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('不支持的分配策略: invalid');

        // 使用反射来测试私有方法，而不是通过数据库约束
        $reflection = new \ReflectionClass($ipPoolService);
        $method = $reflection->getMethod('allocateByLeastUsed');
        $method->setAccessible(true);

        // 创建一个虚拟的 IP 池对象来测试
        $invalidPool = new IpPool(['allocation_strategy' => 'invalid']);
        $ipPoolService->allocateIp($invalidPool);
    }

    /** @test */
    public function it_validates_pure_lb_traffic_policy_constraints(): void
    {
        // 创建测试 Service 记录，关联 poolIp
        $poolIp = $this->singlePool->poolIps->first();
        $service = Service::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'test-service',
            'type' => 'LoadBalancer',
            'external_traffic_policy' => 'Local',
            'allow_shared_ip' => true, // 这个组合是不允许的
            'pool_ip_id' => $poolIp->id,
            'allocated_ip' => $poolIp->ip_address,
            'allocated_port' => 20000,
        ]);

        // 验证 getEffectiveExternalTrafficPolicy 方法
        $this->assertEquals('Cluster', $service->getEffectiveExternalTrafficPolicy());

        // 验证注释生成
        $annotations = $service->getLoadBalancerAnnotations();
        $this->assertArrayHasKey('metallb.io/allow-shared-ip', $annotations);

        // 验证注释内容
        $sharingKey = 'share-ip-'.str_replace('.', '-', $poolIp->ip_address);
        $this->assertEquals($sharingKey, $annotations['metallb.io/allow-shared-ip']);
    }
}
