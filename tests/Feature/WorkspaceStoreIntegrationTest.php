<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('workspace store can fetch workspaces via API', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    // 创建几个工作区
    $workspace1 = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'test-workspace-1',
        'status' => 'active',
    ]);

    $workspace2 = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'test-workspace-2',
        'status' => 'pending',
    ]);

    // 设置当前工作区
    $user->setCurrentWorkspace($workspace1);

    // 测试 API 端点
    $response = $this->actingAs($user)->getJson('/api/workspaces');

    $response->assertStatus(200)
        ->assertJsonCount(2, 'data')
        ->assertJsonFragment(['name' => 'test-workspace-1'])
        ->assertJsonFragment(['name' => 'test-workspace-2']);

    // 验证返回的数据结构
    $responseData = $response->json();
    expect($responseData)->toHaveKey('data');
    $workspaces = $responseData['data'];
    expect($workspaces[0])->toHaveKeys(['id', 'name', 'status', 'namespace', 'cluster']);
    expect($workspaces[0]['cluster'])->toHaveKeys(['id', 'name']);
});

test('workspace store can switch current workspace via API', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace1 = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'workspace-1',
        'status' => 'active',
    ]);

    $workspace2 = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'workspace-2',
        'status' => 'active',
    ]);

    // 设置初始工作区
    $user->setCurrentWorkspace($workspace1);
    expect($user->current_workspace_id)->toBe($workspace1->id);

    // 切换到另一个工作区
    $response = $this->actingAs($user)
        ->postJson("/api/workspaces/{$workspace2->id}/set-current");

    $response->assertStatus(200);

    // 验证工作区已切换
    $user->refresh();
    expect($user->current_workspace_id)->toBe($workspace2->id);
});

test('workspace store handles unauthorized access', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user2->id,
        'cluster_id' => $cluster->id,
    ]);

    // 用户1不能访问用户2的工作区
    $response = $this->actingAs($user1)
        ->postJson("/api/workspaces/{$workspace->id}/set-current");

    $response->assertStatus(403);
});
