<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// 暂时跳过需要 Vite 的页面测试
test('index route requires authentication', function () {
    $response = $this->get(route('workspaces.index'));
    $response->assertRedirect(route('login'));
});

test('create route requires authentication', function () {
    $response = $this->get(route('workspaces.create'));
    $response->assertRedirect(route('login'));
});

test('store creates workspace successfully', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $data = [
        'name' => 'test-workspace',
        'cluster_id' => $cluster->id,
    ];

    $response = $this->actingAs($user)->post(route('workspaces.store'), $data);

    $response->assertRedirect(route('workspaces.index'));
    $response->assertSessionHas('success', '工作空间创建成功');

    $this->assertDatabaseHas('workspaces', [
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'test-workspace',
        'status' => 'pending',
    ]);

    $workspace = Workspace::where('name', 'test-workspace')->first();
    expect($workspace->namespace)->toMatch('/^ns-[a-z0-9]{8}$/');
});

test('store validates workspace name', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    // 测试空名称
    $response = $this->actingAs($user)->postJson(route('workspaces.store'), [
        'name' => '',
        'cluster_id' => $cluster->id,
    ]);
    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['name']);

    // 测试名称太短
    $response = $this->actingAs($user)->postJson(route('workspaces.store'), [
        'name' => 'ab',
        'cluster_id' => $cluster->id,
    ]);
    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['name']);

    // 测试无效字符
    $response = $this->actingAs($user)->postJson(route('workspaces.store'), [
        'name' => 'test workspace!',
        'cluster_id' => $cluster->id,
    ]);
    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['name']);
});

test('store validates cluster selection', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->postJson(route('workspaces.store'), [
        'name' => 'test-workspace',
        'cluster_id' => 999, // 不存在的集群
    ]);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['cluster_id']);
});

test('store prevents duplicate workspace names for same user', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    // 创建现有工作空间
    Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'existing-workspace',
    ]);

    $response = $this->actingAs($user)->postJson(route('workspaces.store'), [
        'name' => 'existing-workspace',
        'cluster_id' => $cluster->id,
    ]);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['name']);
});

test('store allows duplicate workspace names for different users', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $cluster = Cluster::factory()->create();

    // 用户1创建工作空间
    Workspace::factory()->create([
        'user_id' => $user1->id,
        'cluster_id' => $cluster->id,
        'name' => 'shared-name',
    ]);

    // 用户2使用相同名称
    $response = $this->actingAs($user2)->post(route('workspaces.store'), [
        'name' => 'shared-name',
        'cluster_id' => $cluster->id,
    ]);

    $response->assertRedirect(route('workspaces.index'));
    $response->assertSessionHas('success', '工作空间创建成功');

    $this->assertDatabaseHas('workspaces', [
        'user_id' => $user2->id,
        'name' => 'shared-name',
    ]);
});

test('api index returns user workspaces', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
        'name' => 'api-test-workspace',
        'status' => 'pending',
    ]);

    $response = $this->actingAs($user)->getJson('/api/workspaces');

    $response->assertStatus(200);
    $response->assertJsonCount(1, 'data');
    $response->assertJsonFragment([
        'name' => 'api-test-workspace',
        'status' => 'pending',
    ]);
});

test('api store creates workspace', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $data = [
        'name' => 'api-workspace',
        'cluster_id' => $cluster->id,
    ];

    $response = $this->actingAs($user)->postJson('/api/workspaces', $data);

    $response->assertStatus(201);
    $response->assertJsonFragment([
        'name' => 'api-workspace',
        'status' => 'pending',
    ]);

    $this->assertDatabaseHas('workspaces', [
        'user_id' => $user->id,
        'name' => 'api-workspace',
    ]);
});

test('api clusters returns available clusters', function () {
    $user = User::factory()->create();
    $cluster1 = Cluster::factory()->create(['name' => 'api-cluster-1']);
    $cluster2 = Cluster::factory()->create(['name' => 'api-cluster-2']);

    $response = $this->actingAs($user)->getJson('/api/clusters');

    $response->assertStatus(200);
    $response->assertJsonCount(2);
    $response->assertJsonFragment(['name' => 'api-cluster-1']);
    $response->assertJsonFragment(['name' => 'api-cluster-2']);
});

test('show workspace requires authentication and ownership', function () {
    $user = User::factory()->create();
    $otherUser = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
    ]);

    // 未认证用户
    $this->get(route('workspaces.show', $workspace))->assertRedirect(route('login'));

    // 其他用户无法访问
    $this->actingAs($otherUser)->get(route('workspaces.show', $workspace))->assertStatus(403);

    // 所有者可以访问
    $this->actingAs($user)->get(route('workspaces.show', $workspace))->assertStatus(200);
});

test('edit workspace requires authentication and ownership', function () {
    $user = User::factory()->create();
    $otherUser = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
    ]);

    // 未认证用户
    $this->get(route('workspaces.edit', $workspace))->assertRedirect(route('login'));

    // 其他用户无法访问
    $this->actingAs($otherUser)->get(route('workspaces.edit', $workspace))->assertStatus(403);

    // 所有者可以访问
    $this->actingAs($user)->get(route('workspaces.edit', $workspace))->assertStatus(200);
});

test('update workspace successfully', function () {
    $user = User::factory()->create();
    $cluster1 = Cluster::factory()->create();
    $cluster2 = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster1->id,
        'name' => 'old-name',
    ]);

    $response = $this->actingAs($user)->put(route('workspaces.update', $workspace), [
        'name' => 'new-name',
        'cluster_id' => $cluster2->id,
    ]);

    $response->assertRedirect(route('workspaces.show', $workspace));
    $response->assertSessionHas('success', '工作空间更新成功');

    $workspace->refresh();
    expect($workspace->name)->toBe('new-name');
    expect($workspace->cluster_id)->toBe($cluster2->id);
});

test('delete workspace successfully', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
    ]);

    $response = $this->actingAs($user)->delete(route('workspaces.destroy', $workspace));

    $response->assertRedirect(route('workspaces.index'));
    $response->assertSessionHas('success', '工作空间删除成功');

    $this->assertDatabaseMissing('workspaces', [
        'id' => $workspace->id,
    ]);
});

test('set current workspace successfully', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
    ]);

    $response = $this->actingAs($user)->post(route('workspaces.set-current', $workspace));

    $response->assertRedirect();
    $response->assertSessionHas('success', '已切换到工作空间：'.$workspace->name);

    $user->refresh();
    expect($user->current_workspace_id)->toBe($workspace->id);
});

test('api update workspace successfully', function () {
    $user = User::factory()->create();
    $cluster1 = Cluster::factory()->create();
    $cluster2 = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster1->id,
        'name' => 'api-old-name',
    ]);

    $response = $this->actingAs($user)->putJson("/api/workspaces/{$workspace->id}", [
        'name' => 'api-new-name',
        'cluster_id' => $cluster2->id,
    ]);

    $response->assertStatus(200);
    $response->assertJsonFragment([
        'name' => 'api-new-name',
    ]);

    $workspace->refresh();
    expect($workspace->name)->toBe('api-new-name');
    expect($workspace->cluster_id)->toBe($cluster2->id);
});

test('api delete workspace successfully', function () {
    $user = User::factory()->create();
    $cluster = Cluster::factory()->create();

    $workspace = Workspace::factory()->create([
        'user_id' => $user->id,
        'cluster_id' => $cluster->id,
    ]);

    $response = $this->actingAs($user)->deleteJson("/api/workspaces/{$workspace->id}");

    $response->assertStatus(204);

    $this->assertDatabaseMissing('workspaces', [
        'id' => $workspace->id,
    ]);
});

test('unauthenticated users cannot access workspace routes', function () {
    $this->get(route('workspaces.index'))->assertRedirect(route('login'));
    $this->get(route('workspaces.create'))->assertRedirect(route('login'));
    $this->post(route('workspaces.store'))->assertRedirect(route('login'));

    $this->getJson('/api/workspaces')->assertStatus(401);
    $this->postJson('/api/workspaces')->assertStatus(401);
    $this->getJson('/api/clusters')->assertStatus(401);
});
