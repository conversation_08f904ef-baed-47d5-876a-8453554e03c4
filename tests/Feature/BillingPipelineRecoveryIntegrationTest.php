<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\BalanceService;
use App\Service\Billing\BillingPipeline;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class BillingPipelineRecoveryIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected BalanceService $balanceService;

    protected BillingPipeline $billingPipeline;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake();

        $this->user = User::factory()->create([
            'current_balance' => 0.0,
        ]);

        $this->cluster = Cluster::factory()->create();

        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        $this->balanceService = app(BalanceService::class);
        $this->billingPipeline = app(BillingPipeline::class);

        // 为用户充值
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '测试充值']);
    }

    /**
     * @test
     * 测试计费流水线包含恢复阶段
     */
    public function billing_pipeline_includes_recovery_phase()
    {
        // 临时禁用自动恢复，避免K8s API调用
        config(['billing.suspension.auto_resume_on_payment' => false]);

        // 设置工作空间为欠费状态
        $this->workspace->update([
            'status' => Workspace::STATUS_SUSPENDED,
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 5.0000,
            'last_overdue_at' => now(),
        ]);

        // 执行计费流水线
        $result = $this->billingPipeline->processClusterBilling($this->cluster);

        // 验证流水线成功执行
        $this->assertTrue($result['success']);

        // 验证包含恢复阶段
        $this->assertArrayHasKey('overdue_recovery', $result['phases']);

        // 验证恢复阶段结构
        $recoveryPhase = $result['phases']['overdue_recovery'];
        $this->assertEquals('overdue_recovery', $recoveryPhase['phase']);
        $this->assertTrue($recoveryPhase['success']);
        $this->assertArrayHasKey('recovery_results', $recoveryPhase);
        $this->assertArrayHasKey('recovery_processed', $recoveryPhase);
        $this->assertArrayHasKey('recovery_successful', $recoveryPhase);
        $this->assertArrayHasKey('recovery_failed', $recoveryPhase);

        // 验证摘要包含恢复信息
        $this->assertArrayHasKey('recovered_workspaces', $result['summary']);
    }

    /**
     * @test
     * 测试恢复阶段在余额扣费之后执行
     */
    public function recovery_phase_executes_after_balance_deduction()
    {
        config(['billing.suspension.auto_resume_on_payment' => false]);

        $result = $this->billingPipeline->processClusterBilling($this->cluster);

        $this->assertTrue($result['success']);

        // 验证阶段顺序：恢复阶段在余额扣费之后，欠费处理之前
        $phases = $result['phases'];
        $this->assertArrayHasKey('balance_deduction', $phases);
        $this->assertArrayHasKey('overdue_recovery', $phases);
        $this->assertArrayHasKey('overdue_handling', $phases);

        // 验证阶段名称
        $this->assertEquals('balance_deduction', $phases['balance_deduction']['phase']);
        $this->assertEquals('overdue_recovery', $phases['overdue_recovery']['phase']);
        $this->assertEquals('overdue_handling', $phases['overdue_handling']['phase']);
    }

    /**
     * @test
     * 测试批处理摘要包含恢复统计
     */
    public function batch_summary_includes_recovery_statistics()
    {
        config(['billing.suspension.auto_resume_on_payment' => false]);

        $result = $this->billingPipeline->processAllClustersBilling();

        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('total_recovered_workspaces', $result['summary']);

        // 验证统计数据类型
        $this->assertIsInt($result['summary']['total_recovered_workspaces']);
    }

    /**
     * @test
     * 测试恢复阶段错误处理
     */
    public function recovery_phase_handles_errors_gracefully()
    {
        // 通过强制错误来测试错误处理（比如模拟服务不可用）
        // 这里我们模拟一个可能的错误场景

        $result = $this->billingPipeline->processClusterBilling($this->cluster);

        // 即使恢复阶段出现错误，流水线仍应该继续执行
        $this->assertTrue($result['success']);

        $recoveryPhase = $result['phases']['overdue_recovery'];
        $this->assertEquals('overdue_recovery', $recoveryPhase['phase']);

        // 验证恢复阶段的错误统计存在
        $this->assertArrayHasKey('recovery_failed', $recoveryPhase);
        $this->assertIsInt($recoveryPhase['recovery_failed']);
    }

    /**
     * @test
     * 测试命令行帮助包含恢复阶段描述
     */
    public function command_help_includes_recovery_phase()
    {
        $this->artisan('billing:pipeline --dry-run')
            ->expectsOutputToContain('5. 欠费恢复阶段 (Overdue Recovery Phase)')
            ->expectsOutputToContain('- 扫描因欠费暂停的工作空间')
            ->expectsOutputToContain('- 检查用户余额是否足够支付欠费')
            ->expectsOutputToContain('- 自动恢复余额充足的工作空间')
            ->expectsOutputToContain('- 重新启动暂停的资源')
            ->expectsOutputToContain('6. 欠费处理阶段 (Overdue Handling Phase)')
            ->assertSuccessful();
    }

    /**
     * @test
     * 测试不再有独立的恢复命令选项
     */
    public function no_separate_recovery_command_option()
    {
        // 验证命令描述包含恢复功能，且不再有 --recovery 选项
        $this->artisan('billing:pipeline --help')
            ->expectsOutputToContain('执行完整的计费流水线：资源收集、成本计算、计费记录、余额扣费、欠费恢复、欠费处理')
            ->doesntExpectOutputToContain('--recovery')
            ->assertSuccessful();
    }
}
