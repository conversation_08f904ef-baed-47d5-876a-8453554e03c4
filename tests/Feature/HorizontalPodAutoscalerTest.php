<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\HorizontalPodAutoscalerService;
use App\Service\KubernetesService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HorizontalPodAutoscalerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    private Cluster $cluster;

    private HorizontalPodAutoscalerService $hpaService;

    private KubernetesService $k8sService;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建测试集群
        $this->cluster = Cluster::factory()->token()->create([
            'name' => 'test-cluster',
        ]);

        // 创建测试工作空间（属于测试用户）
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'test-workspace',
            'cluster_id' => $this->cluster->id,
            'namespace' => 'test-namespace',
        ]);

        // 设置用户当前工作空间
        $this->user->current_workspace_id = $this->workspace->id;
        $this->user->save();

        $this->hpaService = app(HorizontalPodAutoscalerService::class);
        $this->k8sService = app(KubernetesService::class);
    }

    /**
     * @test
     */
    public function can_get_scalable_workloads()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/hpas-scalable-workloads');

        // 如果集群不可用，期望 422 错误，否则期望 200 成功
        $this->assertTrue(in_array($response->getStatusCode(), [200, 422, 500]));

        if ($response->getStatusCode() === 200) {
            $this->assertIsArray($response->json());
        }
    }

    /**
     * @test
     */
    public function can_create_hpa_with_cpu_utilization_metric()
    {
        $this->actingAs($this->user);

        // 首先创建一个测试用的 Deployment
        $deploymentData = [
            'name' => 'test-app',
            'replicas' => 2,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);

        // 如果 Deployment 创建失败，跳过 HPA 测试
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        // 创建 HPA
        $hpaData = [
            'name' => 'test-app-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'test-app',
            'min_replicas' => 2,
            'max_replicas' => 10,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '80',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        if ($response->getStatusCode() === 201) {
            $data = $response->json();

            $this->assertEquals('test-app-hpa', $data['name']);
            $this->assertEquals('Deployment', $data['scale_target_ref']['kind']);
            $this->assertEquals('test-app', $data['scale_target_ref']['name']);
            $this->assertEquals(2, $data['min_replicas']);
            $this->assertEquals(10, $data['max_replicas']);
            $this->assertCount(1, $data['metrics']);
            $this->assertEquals('Resource', $data['metrics'][0]['type']);
            $this->assertEquals('cpu', $data['metrics'][0]['resource']['name']);
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function can_create_hpa_with_memory_average_value_metric()
    {
        $this->actingAs($this->user);

        // 首先创建一个测试用的 Deployment
        $deploymentData = [
            'name' => 'memory-app',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        // 创建基于内存平均值的 HPA
        $hpaData = [
            'name' => 'memory-app-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'memory-app',
            'min_replicas' => 1,
            'max_replicas' => 5,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'memory',
                    'target_type' => 'AverageValue',
                    'target_value' => '512Mi',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        if ($response->getStatusCode() === 201) {
            $response->assertCreated();
            $data = $response->json();
            $this->assertEquals('memory-app-hpa', $data['name']);
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function can_get_hpa_list()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/hpas');

        // 如果集群不可用，期望 422、500 或 400 错误，否则期望 200 成功
        $statusCode = $response->getStatusCode();
        if (! in_array($statusCode, [200, 422, 500, 400])) {
            $this->assertEquals(200, $statusCode, "Unexpected status code: {$statusCode}. Response: ".$response->getContent());
        }
        $this->assertTrue(in_array($statusCode, [200, 422, 500, 400]));

        if ($response->getStatusCode() === 200) {
            $this->assertIsArray($response->json());
        }
    }

    /**
     * @test
     */
    public function can_get_specific_hpa()
    {
        $this->actingAs($this->user);

        // 首先创建一个 HPA
        $deploymentData = [
            'name' => 'get-test-app',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        $hpaData = [
            'name' => 'get-test-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'get-test-app',
            'min_replicas' => 1,
            'max_replicas' => 3,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '70',
                ],
            ],
        ];

        $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        // 获取特定 HPA
        $response = $this->actingAs($this->user)->getJson('/api/hpas/get-test-hpa');

        if ($response->getStatusCode() === 200) {
            $response->assertOk();
            $data = $response->json();
            $this->assertEquals('get-test-hpa', $data['name']);
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 404, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function can_update_hpa()
    {
        $this->actingAs($this->user);

        // 首先创建一个 HPA
        $deploymentData = [
            'name' => 'update-test-app',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        $hpaData = [
            'name' => 'update-test-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'update-test-app',
            'min_replicas' => 1,
            'max_replicas' => 3,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '70',
                ],
            ],
        ];

        $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        // 更新 HPA
        $updateData = [
            'target_type' => 'Deployment',
            'target_name' => 'update-test-app',
            'min_replicas' => 2,
            'max_replicas' => 8,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '85',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)->putJson('/api/hpas/update-test-hpa', $updateData);

        if ($response->getStatusCode() === 200) {
            $response->assertOk();
            $data = $response->json();
            $this->assertEquals(2, $data['min_replicas']);
            $this->assertEquals(8, $data['max_replicas']);
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 404, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function can_delete_hpa()
    {
        $this->actingAs($this->user);

        // 首先创建一个 HPA
        $deploymentData = [
            'name' => 'delete-test-app',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        $hpaData = [
            'name' => 'delete-test-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'delete-test-app',
            'min_replicas' => 1,
            'max_replicas' => 3,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '70',
                ],
            ],
        ];

        $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        // 删除 HPA
        $response = $this->actingAs($this->user)->deleteJson('/api/hpas/delete-test-hpa');

        if ($response->getStatusCode() === 200) {
            $response->assertOk();
            // 验证 HPA 已被删除
            $getResponse = $this->actingAs($this->user)->getJson('/api/hpas/delete-test-hpa');
            $this->assertTrue(in_array($getResponse->getStatusCode(), [404, 400, 422, 500]));
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 404, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function validates_hpa_creation_data()
    {
        // 测试缺少必需字段
        $invalidData = [
            'name' => '',
            'target_type' => '',
            'target_name' => '',
            'min_replicas' => 0,
            'max_replicas' => 0,
            'metrics' => [],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/hpas', $invalidData);

        $response->assertUnprocessable();
        $errors = $response->json('errors');
        if ($errors) {
            $this->assertArrayHasKey('name', $errors);
            $this->assertArrayHasKey('target_type', $errors);
            $this->assertArrayHasKey('target_name', $errors);
            $this->assertArrayHasKey('min_replicas', $errors);
            $this->assertArrayHasKey('max_replicas', $errors);
            $this->assertArrayHasKey('metrics', $errors);
        }
    }

    /**
     * @test
     */
    public function validates_min_max_replicas_relationship()
    {
        $invalidData = [
            'name' => 'test-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'test-app',
            'target_api_version' => 'apps/v1',
            'min_replicas' => 10,  // min > max
            'max_replicas' => 5,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '80',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/hpas', $invalidData);

        $response->assertUnprocessable();
    }

    /**
     * @test
     */
    public function web_routes_work_correctly()
    {
        // 测试 HPA 列表页面
        $response = $this->actingAs($this->user)->get('/hpas');
        $this->assertTrue(in_array($response->getStatusCode(), [200, 302]));

        // 测试 HPA 创建页面
        $response = $this->actingAs($this->user)->get('/hpas/create');
        $this->assertTrue(in_array($response->getStatusCode(), [200, 302]));
    }

    /**
     * @test
     */
    public function hpa_service_can_handle_multiple_metrics()
    {
        $this->actingAs($this->user);

        // 首先创建一个测试用的 Deployment
        $deploymentData = [
            'name' => 'multi-metric-app',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 1000,
                    ],
                ],
            ],
        ];

        $deploymentResponse = $this->actingAs($this->user)->postJson('/api/deployments', $deploymentData);
        if ($deploymentResponse->getStatusCode() !== 201) {
            $this->markTestSkipped('Deployment 创建失败，跳过 HPA 测试');
        }

        // 创建具有多个指标的 HPA
        $hpaData = [
            'name' => 'multi-metric-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'multi-metric-app',
            'target_api_version' => 'apps/v1',
            'min_replicas' => 1,
            'max_replicas' => 10,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '80',
                ],
                [
                    'type' => 'Resource',
                    'resource_name' => 'memory',
                    'target_type' => 'AverageValue',
                    'target_value' => '512Mi',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)->postJson('/api/hpas', $hpaData);

        if ($response->getStatusCode() === 201) {
            $response->assertCreated();
            $data = $response->json();
            $this->assertCount(2, $data['metrics']);
        } else {
            $this->assertTrue(in_array($response->getStatusCode(), [400, 422, 500]));
        }
    }

    /**
     * @test
     */
    public function requires_workspace()
    {
        // 创建没有工作空间的用户
        $userWithoutWorkspace = User::factory()->create();

        $response = $this->actingAs($userWithoutWorkspace)
            ->getJson('/api/hpas');

        $response->assertStatus(422)
            ->assertJson(['message' => '请先选择工作空间']);
    }

    /**
     * @test
     */
    public function hpa_creation_validates_resource_metrics()
    {
        $validData = [
            'name' => 'test-hpa',
            'target_type' => 'Deployment',
            'target_name' => 'test-app',
            'min_replicas' => 1,
            'max_replicas' => 10,
            'metrics' => [
                [
                    'type' => 'Resource',
                    'resource_name' => 'cpu',
                    'target_type' => 'Utilization',
                    'target_value' => '80',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/hpas', $validData);

        // 期望验证通过（即使集群不可用也应该通过验证阶段）
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422, 500, 400]));
    }

    protected function tearDown(): void
    {
        // 清理测试创建的 K8s 资源
        try {
            if ($this->workspace && $this->workspace->cluster) {
                $hpaService = new \App\Service\HorizontalPodAutoscalerService($this->workspace);
                $hpas = $hpaService->getHPAs();
                foreach ($hpas as $hpa) {
                    $hpaService->deleteHPA($hpa->name);
                }

                // 清理测试创建的 Deployments
                $deploymentService = new \App\Service\DeploymentService($this->workspace);
                $deployments = $deploymentService->getDeployments();
                foreach ($deployments as $deployment) {
                    $deploymentService->deleteDeployment($deployment->name);
                }
            }
        } catch (\Exception $e) {
            // 忽略清理错误
        }

        parent::tearDown();
    }
}
