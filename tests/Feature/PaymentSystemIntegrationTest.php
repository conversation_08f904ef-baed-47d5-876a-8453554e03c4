<?php

namespace Tests\Feature;

use App\Models\User;
use App\Service\PaymentManagerService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentSystemIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentManagerService $paymentManager;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->paymentManager = app(PaymentManagerService::class);
        $this->user = User::factory()->create([
            'current_balance' => 0,
        ]);
    }

    /** @test */
    public function payment_system_loads_all_configured_gateways()
    {
        $gateways = $this->paymentManager->getAllGateways();

        // 验证启用的网关被加载
        $this->assertArrayHasKey('manual', $gateways);
        $this->assertArrayHasKey('redeem_code', $gateways);
        $this->assertArrayHasKey('test_payment', $gateways);

        // 禁用的网关不应该被加载
        $this->assertArrayNotHasKey('alipay', $gateways);
        $this->assertArrayNotHasKey('wechat', $gateways);
        $this->assertArrayNotHasKey('bank_card', $gateways);
    }

    /** @test */
    public function payment_methods_api_returns_enabled_gateways()
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/payment-methods');

        $response->assertOk();
        $data = $response->json();

        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // 验证返回的数据结构
        foreach ($data as $method) {
            $this->assertArrayHasKey('identifier', $method);
            $this->assertArrayHasKey('name', $method);
            $this->assertArrayHasKey('description', $method);
            $this->assertArrayHasKey('enabled', $method);
        }
    }

    /** @test */
    public function web_balance_page_includes_payment_methods()
    {
        $this->actingAs($this->user);

        $response = $this->get('/balance');

        $response->assertOk();

        $response->assertInertia(function ($page) {
            $page->has('paymentMethods')
                ->where('paymentMethods', function ($methods) {
                    // 处理Collection或数组
                    $methodsArray = $methods instanceof \Illuminate\Support\Collection
                        ? $methods->toArray()
                        : $methods;

                    return is_array($methodsArray) && count($methodsArray) > 0;
                });
        });
    }

    /** @test */
    public function test_payment_gateway_end_to_end_flow()
    {
        $this->actingAs($this->user);

        // 1. 发起充值请求
        $response = $this->post('/balance/top-up', [
            'amount' => 100,
            'payment_method' => 'test_payment',
        ]);

        // 测试支付会重定向到支付页面
        $response->assertRedirect();

        // 获取重定向URL
        $redirectUrl = $response->headers->get('Location');
        $this->assertNotNull($redirectUrl);
        $this->assertTrue(str_contains($redirectUrl, '/payment/callback/test_payment'));

        // 2. 模拟访问支付回调URL
        $callbackResponse = $this->get($redirectUrl);
        $callbackResponse->assertRedirect(route('balance.index'));

        // 3. 验证用户余额更新
        $this->user->refresh();
        $this->assertEquals(100, $this->user->current_balance);

        // 4. 验证充值记录创建
        $this->assertDatabaseHas('top_up_records', [
            'user_id' => $this->user->id,
            'amount' => 100,
            'status' => 'completed',
            'payment_method' => 'test_payment',
        ]);
    }

    /** @test */
    public function manual_payment_works_correctly()
    {
        $gateway = $this->paymentManager->getGateway('manual');
        $this->assertNotNull($gateway);

        // 测试手动充值
        $result = $gateway->processManualTopUp($this->user, 50, [
            'operator' => 'admin',
            'remark' => '测试手动充值',
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals(50, $result['amount']);

        // 验证用户余额
        $this->user->refresh();
        $this->assertEquals(50, $this->user->current_balance);
    }

    /** @test */
    public function command_line_payment_works_with_all_methods()
    {
        // 测试支持的支付方式
        $supportedMethods = ['manual', 'test_payment'];

        foreach ($supportedMethods as $method) {
            $user = User::factory()->create(['current_balance' => 0]);

            $this->artisan('user:add-balance', [
                'user' => $user->id,
                'amount' => 25,
                '--method' => $method,
                '--remark' => "测试{$method}支付",
            ])
                ->expectsConfirmation('确认执行充值操作？', 'yes')
                ->assertExitCode(0);

            $user->refresh();
            $this->assertEquals(25, $user->current_balance);

            $this->assertDatabaseHas('top_up_records', [
                'user_id' => $user->id,
                'amount' => 25,
                'payment_method' => $method,
                'status' => 'completed',
            ]);
        }
    }

    /** @test */
    public function payment_gateway_configuration_is_flexible()
    {
        // 验证配置的灵活性
        $config = config('payments');

        $this->assertIsArray($config['gateways']);
        $this->assertArrayHasKey('limits', $config);
        $this->assertArrayHasKey('refund', $config);
        $this->assertArrayHasKey('logging', $config);

        // 验证每个网关配置
        foreach ($config['gateways'] as $identifier => $gatewayConfig) {
            $this->assertArrayHasKey('class', $gatewayConfig);
            $this->assertArrayHasKey('enabled', $gatewayConfig);
            $this->assertArrayHasKey('config', $gatewayConfig);

            // 验证class类存在
            $this->assertTrue(class_exists($gatewayConfig['class']));
        }
    }

    /** @test */
    public function payment_limits_are_respected()
    {
        $this->actingAs($this->user);

        $limits = config('payments.limits');

        // 测试最小金额限制
        $response = $this->post('/balance/top-up', [
            'amount' => $limits['min_amount'] - 0.01,
            'payment_method' => 'test_payment',
        ]);
        $response->assertSessionHasErrors('amount');

        // 测试最大金额限制
        $response = $this->post('/balance/top-up', [
            'amount' => $limits['max_amount'] + 1,
            'payment_method' => 'test_payment',
        ]);
        $response->assertSessionHasErrors('amount');

        // 测试正常金额
        $response = $this->post('/balance/top-up', [
            'amount' => $limits['min_amount'],
            'payment_method' => 'test_payment',
        ]);
        $response->assertRedirect(); // 正常情况下会重定向到支付页面
    }

    /** @test */
    public function refund_system_works_correctly()
    {
        // 创建一个已完成的充值记录
        $this->user->topUpRecords()->create([
            'transaction_number' => 'TEST_'.time(),
            'amount' => 100,
            'remaining_amount' => 100,
            'status' => 'completed',
            'payment_method' => 'test_payment',
            'completed_at' => now(),
        ]);

        $record = $this->user->topUpRecords()->first();

        // 使用 BalanceService 执行退款（这会更新数据库记录）
        $balanceService = app(\App\Service\BalanceService::class);
        $result = $balanceService->refund($record, 50, '测试退款');

        $this->assertTrue($result);

        // 验证记录更新
        $record->refresh();
        $this->assertEquals(50, $record->remaining_amount);
        $this->assertEquals(50, $record->refund_amount);
        $this->assertEquals('partial_refunded', $record->status);
    }
}
