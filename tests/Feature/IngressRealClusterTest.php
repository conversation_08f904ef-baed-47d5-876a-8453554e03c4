<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\Ingress;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class IngressRealClusterTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected ?Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 获取真实集群
        $this->cluster = Cluster::first();
        if (! $this->cluster) {
            $this->markTestSkipped('No cluster available for testing');
        }

        // 创建工作空间
        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => 'active',
        ]);

        // 设置当前工作空间
        $this->user->update(['current_workspace_id' => $this->workspace->id]);

        // 认证用户
        $this->actingAs($this->user);
    }

    public function test_can_get_ingress_classes()
    {
        $response = $this->getJson('/api/ingresses-classes');

        $response->assertStatus(200)
            ->assertJsonStructure(['*']);
    }

    public function test_can_create_ingress()
    {
        $ingressData = [
            'name' => 'test-ingress-'.time(),
            'ingress_class' => 'traefik',
            'rules' => [
                [
                    'host' => 'test-'.time().'.example.com',
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/ingresses', $ingressData);

        if ($response->status() !== 201) {
            echo 'Response status: '.$response->status()."\n";
            echo 'Response body: '.$response->getContent()."\n";
        }

        $response->assertStatus(201)
            ->assertJsonStructure([
                'name',
                'namespace',
                'class_name',
                'rules',
                'status',
            ]);

        // 验证数据库记录
        $this->assertDatabaseHas('ingresses', [
            'workspace_id' => $this->workspace->id,
            'name' => $ingressData['name'],
            'ingress_class' => 'traefik',
        ]);
    }

    public function test_can_list_ingresses()
    {
        // 先创建一个 Ingress
        $ingress = Ingress::factory()->create([
            'workspace_id' => $this->workspace->id,
            'name' => 'test-list-ingress',
            'ingress_class' => 'traefik',
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/ingresses');

        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'name',
                    'namespace',
                    'class_name',
                    'status',
                ],
            ]);
    }

    public function test_can_get_single_ingress()
    {
        // 创建测试 Ingress
        $ingressData = [
            'name' => 'test-show-ingress-'.time(),
            'ingress_class' => 'traefik',
            'rules' => [
                [
                    'host' => 'show-test-'.time().'.example.com',
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $createResponse = $this->postJson('/api/ingresses', $ingressData);
        $createResponse->assertStatus(201);

        // 获取创建的 Ingress
        $response = $this->getJson('/api/ingresses/'.$ingressData['name']);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'name',
                'namespace',
                'class_name',
                'rules',
                'status',
            ])
            ->assertJson([
                'name' => $ingressData['name'],
            ]);
    }

    public function test_can_delete_ingress()
    {
        // 创建测试 Ingress
        $ingressData = [
            'name' => 'test-delete-ingress-'.time(),
            'ingress_class' => 'traefik',
            'rules' => [
                [
                    'host' => 'delete-test-'.time().'.example.com',
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $createResponse = $this->postJson('/api/ingresses', $ingressData);
        $createResponse->assertStatus(201);

        // 删除 Ingress
        $response = $this->deleteJson('/api/ingresses/'.$ingressData['name']);

        $response->assertStatus(204);

        // 验证数据库记录已删除
        $this->assertDatabaseMissing('ingresses', [
            'workspace_id' => $this->workspace->id,
            'name' => $ingressData['name'],
        ]);
    }

    public function test_can_check_domain_conflicts()
    {
        $domains = [
            [
                'host' => 'conflict-test.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                        ],
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/ingresses/check-domains', [
            'rules' => $domains,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'conflicts',
            ]);
    }

    protected function tearDown(): void
    {
        // 清理测试创建的 Ingress
        try {
            $ingresses = Ingress::where('workspace_id', $this->workspace->id)->get();
            foreach ($ingresses as $ingress) {
                // 尝试从 K8s 删除
                try {
                    $this->workspace->cluster->http()
                        ->delete("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$ingress->name}");
                } catch (\Exception $e) {
                    // 忽略删除错误
                }
            }
        } catch (\Exception $e) {
            // 忽略清理错误
        }

        parent::tearDown();
    }
}
