<?php

namespace Tests\Feature;

use App\Models\RedeemCode;
use App\Models\User;
use App\Service\BalanceService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RedeemCodeTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->balanceService = app(BalanceService::class);
    }

    public function test_can_create_redeem_code(): void
    {
        $code = RedeemCode::create([
            'code' => 'TEST12345678',
            'amount' => 100.00,
            'max_uses' => 1,
            'expires_at' => now()->addDays(30),
            'description' => '测试兑换码',
        ]);

        $this->assertDatabaseHas('redeem_codes', [
            'code' => 'TEST12345678',
            'amount' => 100.00,
            'max_uses' => 1,
            'used_count' => 0,
            'is_active' => true,
        ]);
    }

    public function test_can_redeem_code_successfully(): void
    {
        // 创建兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'TEST12345678',
            'amount' => 100.00,
            'max_uses' => 1,
            'expires_at' => now()->addDays(30),
            'description' => '测试兑换码',
        ]);

        // 获取用户初始余额
        $initialBalance = $this->user->current_balance;

        // 兑换码
        $result = $this->balanceService->redeemCode($this->user, 'TEST12345678', '127.0.0.1', 'test-agent');

        // 验证结果
        $this->assertTrue($result['success']);
        $this->assertEquals(100.00, $result['amount']);

        // 验证用户余额增加
        $this->user->refresh();
        $this->assertEquals($initialBalance + 100.00, $this->user->current_balance);

        // 验证兑换码使用次数增加
        $redeemCode->refresh();
        $this->assertEquals(1, $redeemCode->used_count);

        // 验证使用记录
        $this->assertDatabaseHas('redeem_code_usages', [
            'user_id' => $this->user->id,
            'redeem_code_id' => $redeemCode->id,
            'amount' => 100.00,
            'ip_address' => '127.0.0.1',
        ]);

        // 验证充值记录
        $this->assertDatabaseHas('top_up_records', [
            'user_id' => $this->user->id,
            'amount' => 100.00,
            'payment_method' => 'redeem_code',
            'status' => 'completed',
        ]);
    }

    public function test_cannot_redeem_expired_code(): void
    {
        // 创建过期的兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'EXPIRED123456',
            'amount' => 100.00,
            'max_uses' => 1,
            'expires_at' => now()->subDays(1), // 昨天过期
            'description' => '过期兑换码',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('兑换码已过期');

        $this->balanceService->redeemCode($this->user, 'EXPIRED123456', '127.0.0.1', 'test-agent');
    }

    public function test_cannot_redeem_exhausted_code(): void
    {
        // 创建已用完的兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'EXHAUSTED1234',
            'amount' => 100.00,
            'max_uses' => 1,
            'used_count' => 1, // 已用完
            'expires_at' => now()->addDays(30),
            'description' => '已用完兑换码',
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('兑换码使用次数已用完');

        $this->balanceService->redeemCode($this->user, 'EXHAUSTED1234', '127.0.0.1', 'test-agent');
    }

    public function test_cannot_redeem_same_single_use_code_twice(): void
    {
        // 创建单次使用兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'SINGLEUSE123',
            'amount' => 100.00,
            'max_uses' => 1,
            'expires_at' => now()->addDays(30),
            'description' => '单次使用兑换码',
        ]);

        // 第一次兑换成功
        $result = $this->balanceService->redeemCode($this->user, 'SINGLEUSE123', '127.0.0.1', 'test-agent');
        $this->assertTrue($result['success']);

        // 第二次兑换应该失败
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('该兑换码你已经使用过了');

        $this->balanceService->redeemCode($this->user, 'SINGLEUSE123', '127.0.0.1', 'test-agent');
    }

    public function test_can_redeem_multi_use_code_multiple_times(): void
    {
        // 创建多次使用兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'MULTIUSE1234',
            'amount' => 50.00,
            'max_uses' => 3,
            'expires_at' => now()->addDays(30),
            'description' => '多次使用兑换码',
        ]);

        $initialBalance = $this->user->current_balance;

        // 第一次兑换
        $result1 = $this->balanceService->redeemCode($this->user, 'MULTIUSE1234', '127.0.0.1', 'test-agent');
        $this->assertTrue($result1['success']);

        // 第二次兑换
        $result2 = $this->balanceService->redeemCode($this->user, 'MULTIUSE1234', '127.0.0.1', 'test-agent');
        $this->assertTrue($result2['success']);

        // 验证余额
        $this->user->refresh();
        $this->assertEquals($initialBalance + 100.00, $this->user->current_balance);

        // 验证使用次数
        $redeemCode->refresh();
        $this->assertEquals(2, $redeemCode->used_count);
    }

    public function test_cannot_redeem_nonexistent_code(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('兑换码不存在');

        $this->balanceService->redeemCode($this->user, 'NONEXISTENT', '127.0.0.1', 'test-agent');
    }

    public function test_web_redeem_route_works(): void
    {
        // 创建兑换码
        $redeemCode = RedeemCode::create([
            'code' => 'WEBTEST12345',
            'amount' => 100.00,
            'max_uses' => 1,
            'expires_at' => now()->addDays(30),
            'description' => 'Web测试兑换码',
        ]);

        // 登录用户
        $response = $this->actingAs($this->user)
            ->post(route('balance.redeem'), [
                'code' => 'WEBTEST12345',
            ]);

        // 验证重定向和成功消息
        $response->assertRedirect();
        $response->assertSessionHas('success');

        // 验证余额增加
        $this->user->refresh();
        $this->assertEquals(100.00, $this->user->current_balance);
    }
}
