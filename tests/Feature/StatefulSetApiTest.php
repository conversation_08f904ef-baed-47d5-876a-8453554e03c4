<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StatefulSetApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建测试集群
        $this->cluster = Cluster::factory()->token()->create([
            'name' => 'test-cluster',
        ]);

        // 创建测试工作空间
        $this->workspace = Workspace::factory()->create([
            'name' => 'test-workspace',
            'cluster_id' => $this->cluster->id,
            'namespace' => 'test-namespace',
        ]);

        // 设置用户当前工作空间
        $this->user->current_workspace_id = $this->workspace->id;
        $this->user->save();
    }

    public function test_can_list_statefulsets()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/statefulsets');

        // 如果集群不可用，期望 422 错误，否则期望 200 成功
        $statusCode = $response->getStatusCode();
        $this->assertTrue(in_array($statusCode, [200, 422, 500]), "Unexpected status code: {$statusCode}. Response: ".$response->getContent());

        if ($response->getStatusCode() === 200) {
            $response->assertJsonStructure([
                '*' => [
                    'name',
                    'namespace',
                    'replicas',
                    'readyReplicas',
                    'status',
                    'containers',
                    'serviceName',
                    'volumeClaimTemplates',
                ],
            ]);
        }
    }

    public function test_can_create_statefulset()
    {
        $statefulSetData = [
            'name' => 'test-statefulset',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/statefulsets', $statefulSetData);

        // 如果集群不可用，期望 422 错误
        $this->assertTrue(in_array($response->getStatusCode(), [201, 422]));
    }

    public function test_validate_statefulset_name()
    {
        $statefulSetData = [
            'name' => 'INVALID_NAME',  // 无效的名称（大写）
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/statefulsets', $statefulSetData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_validate_volume_claim_template_size()
    {
        $statefulSetData = [
            'name' => 'test-statefulset',
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/statefulsets', $statefulSetData);

        $response->assertStatus(422);
    }

    public function test_scale_statefulset()
    {
        $response = $this->actingAs($this->user)
            ->patchJson('/api/statefulsets/test-statefulset/scale', [
                'replicas' => 3,
            ]);

        // 如果 StatefulSet 不存在或集群不可用，期望 422 错误
        $this->assertTrue(in_array($response->getStatusCode(), [200, 422]));
    }

    public function test_requires_workspace()
    {
        // 创建没有工作空间的用户
        $userWithoutWorkspace = User::factory()->create();

        $response = $this->actingAs($userWithoutWorkspace)
            ->getJson('/api/statefulsets');

        $response->assertStatus(422)
            ->assertJson(['message' => '请先选择工作空间']);
    }
}
