<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ImageValidationFeatureTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Workspace $workspace;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->workspace = Workspace::factory()->active()->create(['user_id' => $this->user->id]);
        $this->user->update(['current_workspace_id' => $this->workspace->id]);
    }

    public function test_deployment_creation_with_image_validation()
    {
        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        // 注意：这个测试会实际请求外部服务，在 CI 环境中可能需要 mock
        // 或者设置 K8S_IMAGE_VALIDATION_STRICT_MODE=false
        config(['k8s.imageValidation.strict_mode' => false]);

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 根据网络状况，可能成功也可能失败，但不应该因为镜像验证而直接报错
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201), // 成功创建
                $this->equalTo(422), // 验证失败
                $this->equalTo(500), // 服务器错误
                $this->equalTo(400)  // 工作空间问题
            )
        );
    }

    public function test_deployment_creation_with_custom_registry_image()
    {
        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'app',
                    'image' => 'example.com/myapp:v1.0',
                    'ports' => [
                        [
                            'container_port' => 8080,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        // 禁用严格验证，因为这是一个不存在的镜像
        config(['k8s.imageValidation.strict_mode' => false]);

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 应该能够创建，因为不是严格验证模式
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201),
                $this->equalTo(422),
                $this->equalTo(500),
                $this->equalTo(400)
            )
        );
    }

    public function test_deployment_creation_with_image_pull_secrets()
    {
        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'private-app',
                    'image' => 'private-registry.com/myapp:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
            'image_pull_secrets' => ['my-registry-secret'],
        ];

        config(['k8s.imageValidation.strict_mode' => false]);

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 测试镜像拉取密钥是否被正确处理
        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201),
                $this->equalTo(422),
                $this->equalTo(500),
                $this->equalTo(400)
            )
        );
    }

    public function test_statefulset_creation_with_image_validation()
    {
        $this->actingAs($this->user);

        $statefulSetData = [
            'name' => 'test-statefulset',
            'replicas' => 1,
            'service_name' => 'test-service',
            'containers' => [
                [
                    'name' => 'database',
                    'image' => 'postgres:13',
                    'ports' => [
                        [
                            'container_port' => 5432,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 1024,
                        'cpu' => 1000,
                    ],
                    'env' => [
                        [
                            'name' => 'POSTGRES_PASSWORD',
                            'value' => 'secret',
                        ],
                    ],
                ],
            ],
        ];

        config(['k8s.imageValidation.strict_mode' => false]);

        $response = $this->postJson('/api/statefulsets', $statefulSetData);

        $this->assertThat(
            $response->status(),
            $this->logicalOr(
                $this->equalTo(201),
                $this->equalTo(422),
                $this->equalTo(500),
                $this->equalTo(400)
            )
        );
    }

    public function test_image_size_limit_validation()
    {
        $this->actingAs($this->user);

        // 设置一个很小的镜像大小限制进行测试
        config(['k8s.imageValidation.max_size_mb' => 1]); // 1MB 限制
        config(['k8s.imageValidation.strict_mode' => true]);

        $deploymentData = [
            'name' => 'test-large-image',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'large-app',
                    'image' => 'nginx:latest', // 通常大于 1MB
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 根据网络状况和严格验证设置，可能会因为镜像太大而失败
        if ($response->status() === 422) {
            $this->assertStringContainsString('镜像大小', $response->getContent());
        }
    }

    public function test_invalid_image_name_validation()
    {
        $this->actingAs($this->user);

        $deploymentData = [
            'name' => 'test-invalid-image',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'invalid-app',
                    'image' => 'nonexistent-registry.com/nonexistent/app:nonexistent',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        config(['k8s.imageValidation.strict_mode' => true]);

        $response = $this->postJson('/api/deployments', $deploymentData);

        // 在严格验证模式下，无效镜像应该导致验证失败
        if ($response->status() === 422) {
            $this->assertStringContainsString('镜像验证失败', $response->getContent());
        }
    }
}
