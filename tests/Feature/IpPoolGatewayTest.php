<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class IpPoolGatewayTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cluster = Cluster::factory()->create();
    }

    public function test_can_create_ipv4_pool_with_gateway()
    {
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-ipv4-pool',
            'ip_version' => IpPool::IP_VERSION_IPV4,
            'subnet_v4' => '***********/24',
            'gateway_v4' => '***********',
            'driver' => 'metallb',
        ]);

        $this->assertDatabaseHas('ip_pools', [
            'name' => 'test-ipv4-pool',
            'gateway_v4' => '***********',
            'driver' => 'metallb',
        ]);

        $this->assertEquals('***********', $ipPool->gateway_v4);
    }

    public function test_can_create_ipv6_pool_with_gateway()
    {
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-ipv6-pool',
            'ip_version' => IpPool::IP_VERSION_IPV6,
            'subnet_v6' => 'fd00::/120',
            'gateway_v6' => 'fd00::1',
        ]);

        $this->assertDatabaseHas('ip_pools', [
            'name' => 'test-ipv6-pool',
            'gateway_v6' => 'fd00::1',
        ]);
        $this->assertEquals('fd00::1', $ipPool->gateway_v6);
    }

    public function test_can_create_dual_stack_pool_with_gateways()
    {
        $ipPool = IpPool::factory()->create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-dual-pool',
            'ip_version' => IpPool::IP_VERSION_DUAL,
            'subnet_v4' => '***********/24',
            'gateway_v4' => '***********',
            'subnet_v6' => 'fd01::/120',
            'gateway_v6' => 'fd01::1',
        ]);

        $this->assertDatabaseHas('ip_pools', [
            'name' => 'test-dual-pool',
            'gateway_v4' => '***********',
            'gateway_v6' => 'fd01::1',
        ]);
    }

    public function test_purelb_config_generation_for_ipv4()
    {
        $ipPool = IpPool::create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-ipv4-pool',
            'description' => '测试 IPv4 IP 池',
            'allocation_strategy' => 'least_used',
            'is_active' => true,
            'gateway_v4' => '***********',
            'subnet_v4' => '***********/24',
            'service_group_name' => 'test-ipv4-pool',
            'service_group_namespace' => 'purelb',
            'aggregation' => 'default',
            'ip_version' => 'ipv4',
        ]);

        // 添加一些 IPv4 地址
        PoolIp::create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '***********00',
            'port_range_start' => 20000,
            'port_range_end' => 21000,
            'usage_count' => 0,
            'is_active' => true,
        ]);

        PoolIp::create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => '***********01',
            'port_range_start' => 20000,
            'port_range_end' => 21000,
            'usage_count' => 0,
            'is_active' => true,
        ]);

        $config = $ipPool->getPureLbConfig();

        $this->assertEquals('purelb.io/v1', $config['apiVersion']);
        $this->assertEquals('ServiceGroup', $config['kind']);
        $this->assertEquals('test-ipv4-pool', $config['metadata']['name']);
        $this->assertEquals('purelb', $config['metadata']['namespace']);

        $this->assertArrayHasKey('v4pools', $config['spec']['local']);
        $this->assertArrayNotHasKey('v6pools', $config['spec']['local']);

        $v4pools = $config['spec']['local']['v4pools'];
        $this->assertNotEmpty($v4pools);

        foreach ($v4pools as $pool) {
            $this->assertEquals('***********/24', $pool['subnet']);
            $this->assertEquals('default', $pool['aggregation']);
            $this->assertNotEmpty($pool['pool']);
        }
    }

    public function test_purelb_config_generation_for_ipv6()
    {
        $ipPool = IpPool::create([
            'cluster_id' => $this->cluster->id,
            'name' => 'test-ipv6-pool',
            'description' => '测试 IPv6 IP 池',
            'allocation_strategy' => 'least_used',
            'is_active' => true,
            'gateway_v6' => 'fd53:9ef0:8683::1',
            'subnet_v6' => 'fd53:9ef0:8683::/120',
            'service_group_name' => 'test-ipv6-pool',
            'service_group_namespace' => 'purelb',
            'aggregation' => 'default',
            'ip_version' => 'ipv6',
        ]);

        // 添加一些 IPv6 地址
        PoolIp::create([
            'ip_pool_id' => $ipPool->id,
            'ip_address' => 'fd53:9ef0:8683::100',
            'port_range_start' => 20000,
            'port_range_end' => 21000,
            'usage_count' => 0,
            'is_active' => true,
        ]);

        $config = $ipPool->getPureLbConfig();

        $this->assertEquals('purelb.io/v1', $config['apiVersion']);
        $this->assertEquals('ServiceGroup', $config['kind']);
        $this->assertEquals('test-ipv6-pool', $config['metadata']['name']);

        $this->assertArrayNotHasKey('v4pools', $config['spec']['local']);
        $this->assertArrayHasKey('v6pools', $config['spec']['local']);

        $v6pools = $config['spec']['local']['v6pools'];
        $this->assertNotEmpty($v6pools);

        foreach ($v6pools as $pool) {
            $this->assertEquals('fd53:9ef0:8683::/120', $pool['subnet']);
            $this->assertEquals('default', $pool['aggregation']);
            $this->assertNotEmpty($pool['pool']);
        }
    }

    public function test_service_group_name_defaults_to_pool_name()
    {
        $ipPool = IpPool::create([
            'cluster_id' => $this->cluster->id,
            'name' => 'my-pool',
            'description' => '测试 IP 池',
            'allocation_strategy' => 'least_used',
            'is_active' => true,
            'ip_version' => 'ipv4',
            // 不设置 service_group_name
        ]);

        $this->assertEquals('my-pool', $ipPool->service_group_name);
    }

    public function test_factory_creates_valid_pools()
    {
        $ipv4Pool = IpPool::factory()->ipv4()->create(['cluster_id' => $this->cluster->id]);
        $this->assertEquals('ipv4', $ipv4Pool->ip_version);
        $this->assertNotNull($ipv4Pool->gateway_v4);
        $this->assertNotNull($ipv4Pool->subnet_v4);
        $this->assertNull($ipv4Pool->gateway_v6);
        $this->assertNull($ipv4Pool->subnet_v6);

        $ipv6Pool = IpPool::factory()->ipv6()->create(['cluster_id' => $this->cluster->id]);
        $this->assertEquals('ipv6', $ipv6Pool->ip_version);
        $this->assertNull($ipv6Pool->gateway_v4);
        $this->assertNull($ipv6Pool->subnet_v4);
        $this->assertNotNull($ipv6Pool->gateway_v6);
        $this->assertNotNull($ipv6Pool->subnet_v6);

        $dualPool = IpPool::factory()->dual()->create(['cluster_id' => $this->cluster->id]);
        $this->assertEquals('dual', $dualPool->ip_version);
        $this->assertNotNull($dualPool->gateway_v4);
        $this->assertNotNull($dualPool->subnet_v4);
        $this->assertNotNull($dualPool->gateway_v6);
        $this->assertNotNull($dualPool->subnet_v6);
    }

    public function test_sync_status_tracking()
    {
        $ipPool = IpPool::factory()->create(['cluster_id' => $this->cluster->id]);

        $this->assertFalse($ipPool->synced_to_k8s);
        $this->assertNull($ipPool->last_sync_at);
        $this->assertNull($ipPool->sync_error);

        // 模拟同步成功
        $ipPool->update([
            'synced_to_k8s' => true,
            'last_sync_at' => now(),
            'sync_error' => null,
        ]);

        $this->assertTrue($ipPool->fresh()->synced_to_k8s);
        $this->assertNotNull($ipPool->fresh()->last_sync_at);

        // 模拟同步失败
        $ipPool->update([
            'synced_to_k8s' => false,
            'sync_error' => '同步失败：连接超时',
        ]);

        $this->assertFalse($ipPool->fresh()->synced_to_k8s);
        $this->assertEquals('同步失败：连接超时', $ipPool->fresh()->sync_error);
    }
}
