<?php

namespace Tests\Feature;

use App\Models\User;
use App\Service\PaymentGateways\TestPaymentGateway;
use App\Service\PaymentManagerService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TestPaymentGatewayTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private PaymentManagerService $paymentManager;

    private TestPaymentGateway $testGateway;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->paymentManager = app(PaymentManagerService::class);
        $this->testGateway = $this->paymentManager->getGateway('test_payment');
    }

    public function test_test_payment_gateway_properties()
    {
        $this->assertEquals('测试支付', $this->testGateway->getName());
        $this->assertEquals('仅用于测试的支付方式，会重定向到模拟支付页面', $this->testGateway->getDescription());
    }

    public function test_create_test_payment()
    {
        $amount = 100.00;
        $extra = ['subject' => '测试充值'];

        $result = $this->testGateway->createPayment($this->user, $amount, $extra);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('url', $result);
        $this->assertArrayHasKey('order_id', $result);
        $this->assertArrayHasKey('payment_data', $result);

        // 验证URL包含回调地址
        $this->assertStringContainsString('/payment/callback/test_payment', $result['url']);

        // 验证URL包含必要参数
        $parsedUrl = parse_url($result['url']);
        parse_str($parsedUrl['query'], $queryParams);

        $this->assertArrayHasKey('order_id', $queryParams);
        $this->assertArrayHasKey('amount', $queryParams);
        $this->assertArrayHasKey('user_id', $queryParams);
        $this->assertArrayHasKey('signature', $queryParams);
        $this->assertArrayHasKey('status', $queryParams);
        $this->assertArrayHasKey('transaction_id', $queryParams);

        $this->assertEquals($amount, $queryParams['amount']);
        $this->assertEquals($this->user->id, $queryParams['user_id']);
        $this->assertEquals('success', $queryParams['status']);
    }

    public function test_verify_test_payment_callback()
    {
        // 先创建支付订单
        $amount = 100.00;
        $paymentResult = $this->testGateway->createPayment($this->user, $amount);

        // 解析回调URL参数
        $parsedUrl = parse_url($paymentResult['url']);
        parse_str($parsedUrl['query'], $callbackData);

        // 验证回调
        $verifyResult = $this->testGateway->verifyCallback($callbackData);

        $this->assertTrue($verifyResult['success']);
        $this->assertEquals($callbackData['order_id'], $verifyResult['order_id']);
        $this->assertEquals($callbackData['transaction_id'], $verifyResult['transaction_id']);
        $this->assertEquals($amount, $verifyResult['amount']);
        $this->assertEquals($this->user->id, $verifyResult['user_id']);
    }

    public function test_verify_callback_with_invalid_signature()
    {
        $callbackData = [
            'order_id' => 'TEST_ORDER_123',
            'amount' => 100.00,
            'user_id' => $this->user->id,
            'signature' => 'invalid_signature',
            'status' => 'success',
            'transaction_id' => 'TEST_123',
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('签名验证失败');

        $this->testGateway->verifyCallback($callbackData);
    }

    public function test_verify_callback_with_failed_status()
    {
        // 先创建支付订单
        $paymentResult = $this->testGateway->createPayment($this->user, 100.00);

        // 解析回调URL参数并修改状态为失败
        $parsedUrl = parse_url($paymentResult['url']);
        parse_str($parsedUrl['query'], $callbackData);
        $callbackData['status'] = 'failed';

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('支付失败');

        $this->testGateway->verifyCallback($callbackData);
    }

    public function test_test_payment_refund()
    {
        $record = \App\Models\TopUpRecord::factory()->create([
            'user_id' => $this->user->id,
            'amount' => 100.00,
            'remaining_amount' => 100.00,
            'status' => \App\Models\TopUpRecord::STATUS_COMPLETED,
            'payment_method' => 'test_payment',
        ]);

        $refundResult = $this->testGateway->refund($record, 50.00, '测试退款');

        $this->assertTrue($refundResult['success']);
        $this->assertEquals(50.00, $refundResult['refund_amount']);
        $this->assertArrayHasKey('refund_id', $refundResult);
        $this->assertArrayHasKey('refunded_at', $refundResult);
    }

    public function test_payment_callback_api_endpoint()
    {
        // 先创建支付订单
        $paymentResult = $this->testGateway->createPayment($this->user, 100.00);

        // 解析回调URL参数
        $parsedUrl = parse_url($paymentResult['url']);
        parse_str($parsedUrl['query'], $callbackData);

        // 创建待支付记录
        $record = \App\Models\TopUpRecord::factory()->create([
            'user_id' => $this->user->id,
            'transaction_number' => 'TXN123_'.$callbackData['order_id'],
            'amount' => 100.00,
            'remaining_amount' => 0,
            'status' => \App\Models\TopUpRecord::STATUS_PENDING,
            'payment_method' => 'test_payment',
        ]);

        // 调用回调路由
        $response = $this->post('/payment/callback/test_payment', $callbackData);

        $response->assertStatus(302); // 重定向
        $response->assertRedirect(route('balance.index'));
        $response->assertSessionHas('success', '充值成功！');

        // 验证记录状态已更新
        $record->refresh();
        $this->assertEquals(\App\Models\TopUpRecord::STATUS_COMPLETED, $record->status);
        $this->assertEquals(100.00, $record->remaining_amount);

        // 验证用户余额已更新
        $this->user->refresh();
        $this->assertEquals(100.00, $this->user->current_balance);
    }

    public function test_web_top_up_with_test_payment()
    {
        $this->actingAs($this->user);

        $response = $this->post('/balance/top-up', [
            'amount' => 100.00,
            'payment_method' => 'test_payment',
        ]);

        // 应该重定向到支付URL
        $response->assertRedirect();
        $redirectUrl = $response->headers->get('Location');

        // 验证重定向URL是回调地址
        $this->assertStringContainsString('/payment/callback/test_payment', $redirectUrl);

        // 验证数据库中创建了待支付记录
        $this->assertDatabaseHas('top_up_records', [
            'user_id' => $this->user->id,
            'amount' => 100.00,
            'status' => \App\Models\TopUpRecord::STATUS_PENDING,
            'payment_method' => 'test_payment',
        ]);
    }
}
