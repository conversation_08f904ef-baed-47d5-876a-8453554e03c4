<?php

namespace Tests\Feature;

use App\Models\BillingRecord;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\User;
use App\Models\Workspace;
use App\Service\BalanceService;
use App\Service\BillingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OverdueBillingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    private ClusterPricing $pricing;

    private Workspace $workspace;

    private BillingService $billingService;

    private BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['current_balance' => 10.0]);
        $this->cluster = Cluster::factory()->create(['name' => 'test-cluster']);

        $this->pricing = ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => 100.0000,  // 更高的价格便于测试
            'cpu_price_per_core' => 60.0000,
            'storage_price_per_gb' => 20.0000,
            'loadbalancer_price_per_service' => 200.0000,
            'billing_enabled' => true,
            'description' => '测试高价定价',
        ]);

        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => 'active',
            'name' => 'test-workspace',
            'namespace' => 'test-workspace-ns',
        ]);

        $this->billingService = new BillingService(
            app(\App\Service\ResourceCollectionService::class),
            app(BalanceService::class)
        );

        $this->balanceService = app(BalanceService::class);

        // Mock HTTP responses for K8s API calls
        Http::fake([
            '*/api/v1/namespaces/*/pods' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/persistentvolumeclaims' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/services' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/deployments' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/statefulsets' => Http::response(['items' => []]),
        ]);
    }

    /**
     * @test
     * 测试用户完全没有余额时的计费
     */
    public function user_with_zero_balance_gets_charged_and_suspended()
    {
        Event::fake();

        // 设置用户余额为0
        $this->user->update(['current_balance' => 0]);

        // 创建一个计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1024, 'cpu_m' => 1000],
            'memory_cost' => '5.00000000',
            'cpu_cost' => '3.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '8.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 模拟计费过程中的欠费处理
        try {
            $this->balanceService->deductBalance($this->user, 8.0, '测试计费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        // 标记为失败并处理欠费
        $record->markAsFailed();

        // 模拟欠费处理
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 8.0000,
            'last_overdue_at' => now(),
        ]);

        $this->assertEquals('suspended', $this->workspace->fresh()->status);
        $this->assertEquals(8.0000, $this->workspace->fresh()->overdue_amount);
        $this->assertEquals(BillingRecord::STATUS_FAILED, $record->fresh()->status);
    }

    /**
     * @test
     * 测试用户余额部分不足的情况
     */
    public function user_with_partial_balance_gets_charged_partially()
    {
        Event::fake();

        // 先给用户添加一些余额
        $this->balanceService->addBalance($this->user, 5.0, 'manual', null, ['remark' => '部分余额']);

        // 创建一个超过用户余额的计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 2048, 'cpu_m' => 2000],
            'memory_cost' => '10.00000000',
            'cpu_cost' => '8.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '18.00000000', // 超过用户总余额15.0
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $userBalanceBefore = $this->user->fresh()->current_balance;

        // 尝试扣费应该失败
        try {
            $this->balanceService->deductBalance($this->user, 18.0, '测试计费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        // 验证用户余额没有被扣除
        $this->assertEquals($userBalanceBefore, $this->user->fresh()->current_balance);
    }

    /**
     * @test
     * 测试多次小额欠费累计的情况
     */
    public function multiple_small_overdue_amounts_accumulate()
    {
        Event::fake();

        // 清空用户的原始余额，重新设置
        $this->user->update(['current_balance' => 0]);

        // 给用户少量余额（只够一次计费）
        $this->balanceService->addBalance($this->user, 1.5, 'manual', null, ['remark' => '少量余额']);
        $initialBalance = $this->user->fresh()->current_balance;

        // 第一次计费 - 成功
        $record1 = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinutes(3),
            'billing_end_at' => now()->subMinutes(2),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '1.50000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '1.50000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $this->balanceService->deductBalance($this->user, 1.5, '第一次计费');
        $record1->markAsCharged();

        $balanceAfterFirst = $this->user->fresh()->current_balance;
        $this->assertEquals(0.0, $balanceAfterFirst); // 余额已用完

        // 第二次计费 - 应该失败（余额不足）
        $record2 = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinutes(2),
            'billing_end_at' => now()->subMinute(),
            'resource_usage' => ['memory_mi' => 1024],
            'memory_cost' => '3.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '3.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        try {
            $this->balanceService->deductBalance($this->user, 3.0, '第二次计费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        $record2->markAsFailed();

        // 模拟累计欠费
        $currentOverdue = $this->workspace->overdue_amount ?? 0;
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => $currentOverdue + 3.0000,
            'last_overdue_at' => now(),
        ]);

        // 第三次计费 - 也应该失败并继续累计
        $record3 = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '1.50000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '1.50000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $record3->markAsFailed();

        // 累计更多欠费
        $this->workspace->update([
            'overdue_amount' => $this->workspace->overdue_amount + 1.5000,
        ]);

        // 验证累计欠费金额
        $this->assertEquals(4.5000, $this->workspace->fresh()->overdue_amount);
        $this->assertEquals('suspended', $this->workspace->fresh()->status);
    }

    /**
     * @test
     * 测试用户充值后恢复服务的边界情况
     */
    public function user_topup_recovery_edge_cases()
    {
        Event::fake();

        // 设置工作空间为欠费状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 50.0000,
            'last_overdue_at' => now(),
        ]);

        // 案例1: 充值金额刚好等于欠费金额
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '刚好够还欠费']);

        $userBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(60.0, $userBalance); // 10(原始) + 50(充值)

        // 模拟恢复逻辑
        $overdueAmount = $this->workspace->overdue_amount;
        $this->balanceService->deductBalance($this->user, (float) $overdueAmount, '支付欠费');

        $this->workspace->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspension_reason' => null,
            'overdue_amount' => null,
            'last_overdue_at' => null,
        ]);

        $this->assertEquals(10.0, $this->user->fresh()->current_balance);
        $this->assertEquals('active', $this->workspace->fresh()->status);
        $this->assertNull($this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试充值金额不足以支付欠费的情况
     */
    public function insufficient_topup_for_overdue_amount()
    {
        Event::fake();

        // 设置更高的欠费金额
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 100.0000,
            'last_overdue_at' => now(),
        ]);

        // 充值金额不足
        $this->balanceService->addBalance($this->user, 30.0, 'manual', null, ['remark' => '不够还欠费']);

        $userBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(40.0, $userBalance); // 10(原始) + 30(充值)

        // 验证余额不足以支付欠费
        $overdueAmount = $this->workspace->overdue_amount;
        $this->assertTrue($userBalance < $overdueAmount);

        // 尝试支付欠费应该失败
        try {
            $this->balanceService->deductBalance($this->user, (float) $overdueAmount, '尝试支付欠费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        // 工作空间应该仍然是暂停状态
        $this->assertEquals('suspended', $this->workspace->fresh()->status);
        $this->assertEquals(100.0000, $this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试多次充值逐步支付欠费
     */
    public function multiple_topups_to_clear_overdue()
    {
        Event::fake();

        // 清空用户的原始余额
        $this->user->update(['current_balance' => 0]);

        // 设置欠费状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 80.0000,
            'last_overdue_at' => now(),
        ]);

        // 第一次充值 - 不够
        $this->balanceService->addBalance($this->user, 20.0, 'manual', null, ['remark' => '第一次充值']);
        $balance1 = $this->user->fresh()->current_balance;
        $this->assertEquals(20.0, $balance1); // 0 + 20

        // 验证仍然不足
        $this->assertTrue($balance1 < $this->workspace->overdue_amount);

        // 第二次充值 - 还是不够
        $this->balanceService->addBalance($this->user, 30.0, 'manual', null, ['remark' => '第二次充值']);
        $balance2 = $this->user->fresh()->current_balance;
        $this->assertEquals(50.0, $balance2); // 20 + 30

        // 仍然不足
        $this->assertTrue($balance2 < $this->workspace->overdue_amount);

        // 第三次充值 - 终于够了
        $this->balanceService->addBalance($this->user, 35.0, 'manual', null, ['remark' => '第三次充值']);
        $balance3 = $this->user->fresh()->current_balance;
        $this->assertEquals(85.0, $balance3); // 50 + 35

        // 现在可以支付欠费了
        $this->assertTrue($balance3 >= $this->workspace->overdue_amount);

        // 支付欠费
        $overdueAmount = $this->workspace->overdue_amount;
        $this->balanceService->deductBalance($this->user, (float) $overdueAmount, '支付累计欠费');

        $this->workspace->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspension_reason' => null,
            'overdue_amount' => null,
            'last_overdue_at' => null,
        ]);

        // 验证最终状态
        $finalBalance = $this->user->fresh()->current_balance;
        $this->assertEquals(5.0, $finalBalance); // 85 - 80
        $this->assertEquals('active', $this->workspace->fresh()->status);
        $this->assertNull($this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试已经暂停的工作空间继续产生计费的情况
     */
    public function suspended_workspace_continues_billing()
    {
        Event::fake();

        // 先设置工作空间为暂停状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now()->subHour(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 20.0000,
            'last_overdue_at' => now()->subHour(),
        ]);

        // 给用户一点余额
        $this->balanceService->addBalance($this->user, 5.0, 'manual', null, ['remark' => '少量余额']);

        // 创建新的计费记录（即使工作空间暂停了，理论上仍可能有资源在运行）
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '2.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '2.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 可以成功扣费
        $this->balanceService->deductBalance($this->user, 2.0, '暂停状态计费');
        $record->markAsCharged();

        // 验证扣费成功
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
        $this->assertEquals(13.0, $this->user->fresh()->current_balance); // 10 + 5 - 2

        // 但工作空间仍然是暂停状态
        $this->assertEquals('suspended', $this->workspace->fresh()->status);
        $this->assertEquals(20.0000, $this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试精确的小额计费（测试BCMath精度）
     */
    public function precise_small_amount_billing()
    {
        Event::fake();

        // 给用户添加精确的小额余额
        $this->balanceService->addBalance($this->user, 0.01, 'manual', null, ['remark' => '0.01元测试']);

        // 创建非常小的计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1],
            'memory_cost' => '0.00500000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '0.00500000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $initialBalance = $this->user->fresh()->current_balance;

        // 应该可以成功扣费
        $this->balanceService->deductBalance($this->user, 0.005, '小额计费测试');
        $record->markAsCharged();

        $finalBalance = $this->user->fresh()->current_balance;

        // 验证精确的计算
        $expectedBalance = bcsub((string) $initialBalance, '0.005', 8);
        $this->assertEquals($expectedBalance, (string) $finalBalance);
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->fresh()->status);
    }

    /**
     * @test
     * 测试恢复操作的事务性
     */
    public function recovery_operation_is_transactional()
    {
        Event::fake();

        // 设置欠费状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 30.0000,
            'last_overdue_at' => now(),
        ]);

        // 给用户足够的余额
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '足够恢复']);

        $initialBalance = $this->user->fresh()->current_balance;

        // 模拟恢复过程中的错误（比如K8s API调用失败）
        // 这里我们先正常扣费，然后模拟后续操作失败
        $overdueAmount = $this->workspace->overdue_amount;

        try {
            // 开始事务
            \DB::transaction(function () use ($overdueAmount) {
                // 扣费
                $this->balanceService->deductBalance($this->user, (float) $overdueAmount, '支付欠费');

                // 模拟K8s操作失败
                throw new \Exception('K8s API调用失败');
            });
        } catch (\Exception $e) {
            // 事务回滚，余额应该没有变化
        }

        // 验证事务回滚后余额没有变化
        $this->assertEquals($initialBalance, $this->user->fresh()->current_balance);
        $this->assertEquals('suspended', $this->workspace->fresh()->status);
        $this->assertEquals(30.0000, $this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试极端情况：用户余额为负数
     */
    public function user_with_negative_balance()
    {
        Event::fake();

        // 人为设置负余额（理论上不应该发生，但测试边界情况）
        $this->user->update(['current_balance' => -5.0]);

        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '1.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '1.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 尝试从负余额扣费应该失败
        try {
            $this->balanceService->deductBalance($this->user, 1.0, '负余额测试');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        $record->markAsFailed();

        // 验证余额没有进一步减少
        $this->assertEquals(-5.0, $this->user->fresh()->current_balance);
        $this->assertEquals(BillingRecord::STATUS_FAILED, $record->fresh()->status);
    }

    /**
     * @test
     * 测试工作空间删除时的欠费处理
     */
    public function workspace_deletion_with_overdue_amount()
    {
        Event::fake();

        // 设置工作空间欠费
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 25.0000,
            'last_overdue_at' => now(),
        ]);

        $workspaceId = $this->workspace->id;
        $overdueAmount = $this->workspace->overdue_amount;

        // 验证欠费记录存在
        $this->assertEquals(25.0000, $overdueAmount);

        // 删除工作空间（模拟用户删除欠费的工作空间）
        $this->workspace->delete();

        // 验证工作空间已删除
        $this->assertDatabaseMissing('workspaces', ['id' => $workspaceId]);

        // 相关的计费记录应该也被清理（如果设置了级联删除）
        $this->assertDatabaseMissing('billing_records', ['workspace_id' => $workspaceId]);
    }

    /**
     * @test
     * 测试欠费金额为0或负数时的处理
     */
    public function invalid_overdue_amount_handling()
    {
        Event::fake();

        // 测试案例1: 人为设置欠费金额为0
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 0.0000,
            'last_overdue_at' => now(),
        ]);

        // 给用户充值
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '测试充值']);

        // 尝试恢复应该被跳过，因为欠费金额无效
        $results = $this->billingService->checkAndResumeOverdueWorkspaces();

        // 验证没有处理这个工作空间
        $this->assertEmpty($results);
        $this->assertEquals('suspended', $this->workspace->fresh()->status);

        // 测试案例2: 人为设置欠费金额为负数
        $this->workspace->update([
            'overdue_amount' => -10.0000,
        ]);

        $results = $this->billingService->checkAndResumeOverdueWorkspaces();

        // 验证没有处理这个工作空间
        $this->assertEmpty($results);
        $this->assertEquals('suspended', $this->workspace->fresh()->status);

        // 测试案例3: 设置有效的欠费金额
        $this->workspace->update([
            'overdue_amount' => 15.0000,
        ]);

        $results = $this->billingService->checkAndResumeOverdueWorkspaces();

        // 验证工作空间被成功恢复
        $this->assertCount(1, $results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('active', $this->workspace->fresh()->status);
        $this->assertNull($this->workspace->fresh()->overdue_amount);
    }

    /**
     * @test
     * 测试计费记录费用无效时的处理
     */
    public function invalid_billing_record_cost_handling()
    {
        Event::fake();

        // 记录工作空间的初始状态
        $initialStatus = $this->workspace->status;
        $initialOverdueAmount = $this->workspace->overdue_amount;

        // 创建无效费用的计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '0.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '0.00000000', // 无效的费用（为0）
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 模拟扣费失败并处理欠费
        $billingService = new BillingService(
            app(\App\Service\ResourceCollectionService::class),
            app(\App\Service\BalanceService::class)
        );

        // 通过反射调用受保护的方法
        $reflection = new \ReflectionClass($billingService);
        $method = $reflection->getMethod('handleUserOverdue');
        $method->setAccessible(true);

        $method->invoke($billingService, $record);

        // 验证工作空间状态没有被改变（因为费用无效）
        $this->assertEquals($initialStatus, $this->workspace->fresh()->status);
        $this->assertEquals($initialOverdueAmount, $this->workspace->fresh()->overdue_amount);
    }
}
