<?php

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\TlsCertificateService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 创建测试用户
    $this->user = User::factory()->create();

    // 创建测试集群
    $this->cluster = Cluster::factory()->create();

    // 创建测试工作空间
    $this->workspace = Workspace::factory()->create([
        'user_id' => $this->user->id,
        'cluster_id' => $this->cluster->id,
        'name' => 'test-workspace',
        'namespace' => 'test-namespace',
        'status' => 'active',
    ]);

    $this->tlsService = new TlsCertificateService($this->workspace);
});

test('能够生成自签名证书', function () {
    $hosts = ['example.com', 'www.example.com'];

    $certificate = $this->tlsService->generateSelfSignedCertificate($hosts);

    expect($certificate)->toHaveKeys(['cert', 'key']);
    expect($certificate['cert'])->toContain('BEGIN CERTIFICATE');
    expect($certificate['key'])->toContain('BEGIN PRIVATE KEY');
});

test('能够验证证书和私钥匹配', function () {
    $hosts = ['test.example.com'];

    $certificate = $this->tlsService->generateSelfSignedCertificate($hosts);

    $isValid = $this->tlsService->validateCertificate(
        $certificate['cert'],
        $certificate['key']
    );

    expect($isValid)->toBeTrue();
});

test('能够从证书中提取主机名', function () {
    $originalHosts = ['example.com', 'www.example.com', 'api.example.com'];

    $certificate = $this->tlsService->generateSelfSignedCertificate($originalHosts);

    $extractedHosts = $this->tlsService->extractHostsFromCertificate($certificate['cert']);

    // 检查是否包含所有原始主机名
    foreach ($originalHosts as $host) {
        expect($extractedHosts)->toContain($host);
    }
});

test('能够检测证书过期状态', function () {
    $hosts = ['test.example.com'];

    $certificate = $this->tlsService->generateSelfSignedCertificate($hosts);

    // 新生成的证书应该不会在 30 天内过期
    $isExpiring = $this->tlsService->isCertificateExpiring($certificate['cert'], 30);
    expect($isExpiring)->toBeFalse();

    // 但应该在 400 天内过期（证书有效期 365 天）
    $isExpiring = $this->tlsService->isCertificateExpiring($certificate['cert'], 400);
    expect($isExpiring)->toBeTrue();
});

test('无效证书应该验证失败', function () {
    $isValid = $this->tlsService->validateCertificate('invalid-cert', 'invalid-key');
    expect($isValid)->toBeFalse();
});

test('生成证书需要至少一个主机名', function () {
    if (! extension_loaded('openssl')) {
        $this->markTestSkipped('OpenSSL 扩展未安装');
    }

    expect(fn () => $this->tlsService->generateSelfSignedCertificate([]))
        ->toThrow(\Exception::class);
});

test('没有 OpenSSL 扩展时应该抛出异常', function () {
    // 这个测试只在没有 OpenSSL 扩展时才有意义
})->skip(extension_loaded('openssl'), '需要在没有 OpenSSL 扩展的环境中测试');
