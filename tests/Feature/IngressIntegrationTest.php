<?php

use App\Models\Cluster;
use App\Models\Ingress;
use App\Models\User;
use App\Models\Workspace;
use App\Service\IngressClassManager;
use App\Service\IngressService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 创建用户
    $this->user = User::create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);

    // 使用现有的集群（如果存在）或创建新的
    $this->cluster = Cluster::first();
    if (! $this->cluster) {
        $this->cluster = Cluster::create([
            'name' => 'test-cluster',
            'server_url' => 'https://192.168.81.102:6443',
            'certificate_authority_data' => 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJZnNDSUJQZ1JxbmN3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1qVXdNREE0TURCYUZ3MHpOakEyTWpNd01ERXpNREJhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUMwR290STNOZklmcWNvUnQvWko5N1F5WUFRcFhMZ2NjMVE1RFI2NlNVYmtjRjgxbktzRE1rSTFISFEKV05rdjU2SE9uMnVzelFRN0lRQTl1eGZ1RWg5WllrcC9kZFlzTzBpSnVUc2RRVUlFYnNGYjJZTUF6NHRZOStzNwpWWStjKzhKcDRtM09EbkMwSEhzTG5HTEdCa1ljM05qemNoYit6VUp4dXJRYkN2ZFRoY2NxV1pJNGd6NkVLQVp1CnIrWWtacHgxV2VEQTl5ZkI5VDBYdGMycUNmdWpnc3RGc0x4MTN5VWk4SG8rc2l2THhYQjh1Y1pCZ0wyZzlWUmEKU3N6eFFsV21Xb2VNYmxNektWQTQyWHFqWWNadjZ3aDl2dVdlcFdHRy9UMDZTdzJuVzFpdU5UZ1FxZlJGbXVoYgptWUxrRlNCTktUN29EUllpTGJrRXNxZ0Z4SWdoQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJUK3J4d09HRWxMelBkaEkzRjF1bUxCdFIxb0ZUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQXdaRjllM0lyQgpkWHY3K2g3bVFsNmM0YTc2R3FJSDc2YUE0c1hyNkZ0ZkY5c0lFckE4UzdIdkZYZHJkVkV1MlRVY3FOV1ZReHFVCkd2MktoQm9rblJuUGhJazc1UjNET0Z6VG5Kb2tWYWFkR3ZKbVN5UTVCR2pPQ2xjT1A1TVNTUWR1c044MEI4WGQKcGZEbzFPRjhMZElIeTFER2xNTzMzMXc5ZUpMMVF6dHFzWmZMTGtSZnNsUkQ4RWpBTkxwYzBUaWtCaU9UQ0FxQgp0WXBhN2pkWTNPMTdOTCtDU2ZaS3lJMWljbE1HSUUvaFB0U3VzRmJlb09kelVGUmg5d3dmeDVnTFZUbXZQb3FtCjZwQjc0MDh1dG5vOUVFY0E1UXhoSVI0T0NtQXpUWlc4Z21xcGpxUkRrbThnbGNXeUM3YmpqVzdvMmVQc2hxRGYKUFh2US9rbmczS296Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K',
            'auth_type' => 'certificate',
            'client_certificate_data' => 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURLVENDQWhHZ0F3SUJBZ0lJT1dyT05XanBySG93RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1qVXdNREE0TURCYUZ3MHlOakEyTWpVd01ERXpNREJhTUR3eApIekFkQmdOVkJBb1RGbXQxWW1WaFpHMDZZMngxYzNSbGNpMWhaRzFwYm5NeEdUQVhCZ05WQkFNVEVHdDFZbVZ5CmJtVjBaWE10WVdSdGFXNHdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDc0NLaUMKNTZPRFhaRFNrekFVNVJseVVXK1pjQ0dncWdHaHZFK3ZMMi9oejc3OGE0R2hnNE9GVExpL09XbE5LT2lrZktvWgpCMi95ZTZXcDZlV0Yzbk9haDIxQnVCb3pLNVdKRTlQcTZja0pxZXpGYkRxWmJRZ2t5bUdhOUZhdVp6NWlVOXdUCnc5Mzhid1E1ZFJCUHVCcnFwai9zdm90c0djN25VditSUXUyNXdxanFab3ZUM0J0ZEprdEtKZ0M2SXN2TUtQMHYKd1Z1L1h3dTF4bFM4eldPYWF5akM2WG5zdzZhbFBmRXo0bHhsV1FjdHB5VldzNGJKOW5FVzR2NFBVbW4vWXNwbwpHS3hvK1NOK2xyU3N2bHJwOGYwd282SE4zS2JhVmg4WXhVb3pVTGFBU05MeHlrcEJGYUtQZVFnMEVjSGtQalkzCmNmYlNXVC9sVGFWS3V5MkJBZ01CQUFHalZqQlVNQTRHQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUsKQmdnckJnRUZCUWNEQWpBTUJnTlZIUk1CQWY4RUFqQUFNQjhHQTFVZEl3UVlNQmFBRlA2dkhBNFlTVXZNOTJFagpjWFc2WXNHMUhXZ1ZNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUNXRmZuVUNXd0NhTjM5SmVBcUw4a3VjMWVrCktiM1I4SlA0MS8zdUxnUTZrNVFQejRHcjlEVFpuelBYWEpEMlR2SUx3bWNGa085M0tST0orVWdST2tCYWlkcmwKY003T1FWMnZOdjlYQUtLQjFjODZoRlJ4S1E0QmE1VURkWTdaT3BTazlLVG9rZUliVFJIRW5hcFBrREJYV3hIQgpWMDlCY3c5bFZ4d3JYaVRxZ0VucUFrN0NHM2g1d09kdmR1UEtDbmJKZmwvNDVsM1FITFNTNFNpc09Dbm01OUhGCmI4SU9jMkwra2NXQ1dOL2xBaEREMjNrSGJtNWFxU0dwZjg0c2U4eVJ1Q0VBQ2lGc2pMdGJLVmcySmdUNERqbHUKMnVXQ256NmJwTEZlKy8yaGZTU1NDaEhHRjBtYWNNYitWZFc4V3k5K0dOZzk3MldSc0M2NE9RRG43K2t2Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K',
            'client_key_data' => '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',
            'insecure_skip_tls_verify' => true, // 跳过 TLS 验证以避免证书问题
        ]);
    } else {
        // 如果使用现有集群，设置跳过 TLS 验证
        $this->cluster->update(['insecure_skip_tls_verify' => true]);
    }

    // 创建真实的工作空间（不用工厂）
    $this->workspace = Workspace::create([
        'user_id' => $this->user->id,
        'cluster_id' => $this->cluster->id,
        'name' => 'test-workspace-'.time(),
        'namespace' => 'test-ns-'.substr(md5(time()), 0, 8),
        'status' => 'active',
    ]);

    $this->ingressService = new IngressService($this->workspace);

    // 确保命名空间存在
    try {
        $this->workspace->cluster->http()
            ->post('/api/v1/namespaces', [
                'apiVersion' => 'v1',
                'kind' => 'Namespace',
                'metadata' => [
                    'name' => $this->workspace->namespace,
                    'labels' => $this->workspace->buildDefaultLabels(),
                ],
            ]);
    } catch (\Exception $e) {
        // 命名空间可能已存在，忽略错误
    }

    // 创建测试服务（Ingress 需要后端服务）
    try {
        $this->workspace->cluster->http()
            ->post("/api/v1/namespaces/{$this->workspace->namespace}/services", [
                'apiVersion' => 'v1',
                'kind' => 'Service',
                'metadata' => [
                    'name' => 'test-service',
                    'namespace' => $this->workspace->namespace,
                ],
                'spec' => [
                    'selector' => ['app' => 'test'],
                    'ports' => [
                        [
                            'port' => 80,
                            'targetPort' => 8080,
                        ],
                    ],
                ],
            ]);
    } catch (\Exception $e) {
        // 服务可能已存在，忽略错误
    }

    // 创建简单的测试 Pod（确保服务有后端）
    try {
        $this->workspace->cluster->http()
            ->post("/api/v1/namespaces/{$this->workspace->namespace}/pods", [
                'apiVersion' => 'v1',
                'kind' => 'Pod',
                'metadata' => [
                    'name' => 'test-pod',
                    'namespace' => $this->workspace->namespace,
                    'labels' => ['app' => 'test'],
                ],
                'spec' => [
                    'containers' => [
                        [
                            'name' => 'test-container',
                            'image' => 'nginx:alpine',
                            'ports' => [
                                [
                                    'containerPort' => 8080,
                                ],
                            ],
                        ],
                    ],
                ],
            ]);
    } catch (\Exception $e) {
        // Pod 可能已存在，忽略错误
    }
});

afterEach(function () {
    // 清理测试数据
    try {
        // 删除命名空间中的所有 Ingress
        $response = $this->workspace->cluster->http()
            ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses");

        $ingresses = $response->json('items', []);
        foreach ($ingresses as $ingress) {
            $this->workspace->cluster->http()
                ->delete("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$ingress['metadata']['name']}");
        }

        // 删除测试 Pod
        $this->workspace->cluster->http()
            ->delete("/api/v1/namespaces/{$this->workspace->namespace}/pods/test-pod");

        // 删除测试服务
        $this->workspace->cluster->http()
            ->delete("/api/v1/namespaces/{$this->workspace->namespace}/services/test-service");

        // 删除命名空间
        $this->workspace->cluster->http()
            ->delete("/api/v1/namespaces/{$this->workspace->namespace}");

    } catch (\Exception $e) {
        // 忽略清理错误
    }
});

test('Traefik 驱动能够创建基本的 Ingress', function () {
    $ingressData = [
        'name' => 'test-traefik-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'test-traefik.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $result = $this->ingressService->createIngress($ingressData);

    expect($result)->toBeInstanceOf(\App\DTOs\IngressDTO::class);
    expect($result->name)->toBe('test-traefik-ingress');
    expect($result->ingressClassName)->toBe('traefik');

    // 验证数据库记录
    $dbIngress = Ingress::where('name', 'test-traefik-ingress')->first();
    expect($dbIngress)->not->toBeNull();
    expect($dbIngress->status)->toBe(Ingress::STATUS_ACTIVE);
    expect($dbIngress->ingress_class)->toBe('traefik');
    expect($dbIngress->getAllDomains())->toContain('test-traefik.example.com');

    // 验证 K8s 中的 Ingress
    $k8sResponse = $this->workspace->cluster->http()
        ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/test-traefik-ingress");

    $k8sIngress = $k8sResponse->json();
    expect($k8sIngress['spec']['ingressClassName'])->toBe('traefik');
    expect($k8sIngress['metadata']['annotations'])->toHaveKey('traefik.ingress.kubernetes.io/router.entrypoints');
    expect($k8sIngress['metadata']['labels'])->toHaveKey('ingress.provider');
    expect($k8sIngress['metadata']['labels']['ingress.provider'])->toBe('traefik');
});

test('Nginx 驱动能够创建带有严格验证的 Ingress', function () {
    $ingressData = [
        'name' => 'test-nginx-ingress',
        'ingress_class' => 'nginx',
        'rules' => [
            [
                'host' => 'test-nginx.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $result = $this->ingressService->createIngress($ingressData);

    expect($result)->toBeInstanceOf(\App\DTOs\IngressDTO::class);
    expect($result->ingressClassName)->toBe('nginx');

    // 验证 K8s 中的 Ingress 包含 Nginx 特定的注解
    $k8sResponse = $this->workspace->cluster->http()
        ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/test-nginx-ingress");

    $k8sIngress = $k8sResponse->json();
    expect($k8sIngress['metadata']['annotations'])->toHaveKey('nginx.ingress.kubernetes.io/ssl-redirect');
    expect($k8sIngress['metadata']['annotations'])->toHaveKey('nginx.ingress.kubernetes.io/proxy-body-size');
});

test('能够创建带有用户指定 TLS Secret 的 Ingress', function () {
    // 首先创建一个 TLS Secret
    $secretData = [
        'apiVersion' => 'v1',
        'kind' => 'Secret',
        'metadata' => [
            'name' => 'user-tls-secret',
            'namespace' => $this->workspace->namespace,
        ],
        'type' => 'kubernetes.io/tls',
        'data' => [
            'tls.crt' => base64_encode('fake-cert'),
            'tls.key' => base64_encode('fake-key'),
        ],
    ];

    $this->workspace->cluster->http()
        ->post("/api/v1/namespaces/{$this->workspace->namespace}/secrets", $secretData);

    $ingressData = [
        'name' => 'test-user-tls-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'secure.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'tls' => [
            [
                'hosts' => ['secure.example.com'],
                'secretName' => 'user-tls-secret',
            ],
        ],
    ];

    $result = $this->ingressService->createIngress($ingressData);

    expect($result)->toBeInstanceOf(\App\DTOs\IngressDTO::class);
    expect($result->tls)->not->toBeEmpty();

    // 验证数据库记录
    $dbIngress = Ingress::where('name', 'test-user-tls-ingress')->first();
    expect($dbIngress->hasTls())->toBeTrue();
    expect($dbIngress->tls_config)->not->toBeEmpty();

    // 验证 K8s 中的 TLS 配置
    $k8sResponse = $this->workspace->cluster->http()
        ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/test-user-tls-ingress");

    $k8sIngress = $k8sResponse->json();
    expect($k8sIngress['spec'])->toHaveKey('tls');
    expect($k8sIngress['spec']['tls'][0]['secretName'])->toBe('user-tls-secret');
});

test('能够创建自动生成 TLS 证书的 Ingress', function () {
    $ingressData = [
        'name' => 'test-auto-tls-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'auto-tls.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        // 故意不设置 tls 配置，让系统自动生成
    ];

    $result = $this->ingressService->createIngress($ingressData);

    expect($result)->toBeInstanceOf(\App\DTOs\IngressDTO::class);
    expect($result->tls)->not->toBeEmpty();

    // 验证数据库记录
    $dbIngress = Ingress::where('name', 'test-auto-tls-ingress')->first();
    expect($dbIngress->hasTls())->toBeTrue();
    expect($dbIngress->tls_config)->not->toBeEmpty();

    // 验证自动生成的 secret 名称格式
    $tlsConfig = $dbIngress->tls_config[0];
    expect($tlsConfig['secretName'])->toStartWith('auto-tls-');

    // 验证 K8s 中的 TLS 配置
    $k8sResponse = $this->workspace->cluster->http()
        ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/test-auto-tls-ingress");

    $k8sIngress = $k8sResponse->json();
    expect($k8sIngress['spec'])->toHaveKey('tls');
    expect($k8sIngress['spec']['tls'][0]['hosts'])->toContain('auto-tls.example.com');
    expect($k8sIngress['spec']['tls'][0]['secretName'])->toStartWith('auto-tls-');

    // 验证自动生成的 TLS Secret 确实存在
    $secretName = $k8sIngress['spec']['tls'][0]['secretName'];
    $secretResponse = $this->workspace->cluster->http()
        ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$secretName}");

    expect($secretResponse->successful())->toBeTrue();
    $secret = $secretResponse->json();
    expect($secret['type'])->toBe('kubernetes.io/tls');
    expect($secret['data'])->toHaveKeys(['tls.crt', 'tls.key']);
});

test('域名冲突检测正常工作', function () {
    // 先创建一个 Ingress
    $firstIngressData = [
        'name' => 'first-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'conflict.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $this->ingressService->createIngress($firstIngressData);

    // 尝试创建冲突的 Ingress
    $conflictIngressData = [
        'name' => 'conflict-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'conflict.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    expect(fn () => $this->ingressService->createIngress($conflictIngressData))
        ->toThrow(\Exception::class, '域名冲突');
});

test('泛域名冲突检测正常工作', function () {
    // 先创建一个泛域名 Ingress
    $wildcardIngressData = [
        'name' => 'wildcard-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => '*.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $this->ingressService->createIngress($wildcardIngressData);

    // 尝试创建被泛域名覆盖的子域名
    $subdomainIngressData = [
        'name' => 'subdomain-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'api.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    expect(fn () => $this->ingressService->createIngress($subdomainIngressData))
        ->toThrow(\Exception::class, '域名冲突');
});

test('能够更新现有的 Ingress', function () {
    // 先创建一个 Ingress
    $originalData = [
        'name' => 'update-test-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'original.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $this->ingressService->createIngress($originalData);

    // 更新 Ingress
    $updateData = [
        'name' => 'update-test-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'updated.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'annotations' => [
            'custom.annotation' => 'custom-value',
        ],
    ];

    $result = $this->ingressService->updateIngress('update-test-ingress', $updateData);

    expect($result)->toBeInstanceOf(\App\DTOs\IngressDTO::class);

    // 验证数据库更新
    $dbIngress = Ingress::where('name', 'update-test-ingress')->first();
    expect($dbIngress->getAllDomains())->toContain('updated.example.com');
    expect($dbIngress->getAllDomains())->not->toContain('original.example.com');

    // 验证 K8s 更新
    $k8sResponse = $this->workspace->cluster->http()
        ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/update-test-ingress");

    $k8sIngress = $k8sResponse->json();
    expect($k8sIngress['spec']['rules'][0]['host'])->toBe('updated.example.com');
    expect($k8sIngress['metadata']['annotations'])->toHaveKey('custom.annotation');
});

test('能够删除 Ingress', function () {
    // 先创建一个 Ingress
    $ingressData = [
        'name' => 'delete-test-ingress',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'delete.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $this->ingressService->createIngress($ingressData);

    // 验证 Ingress 存在
    $dbIngress = Ingress::where('name', 'delete-test-ingress')->first();
    expect($dbIngress)->not->toBeNull();

    // 删除 Ingress
    $this->ingressService->deleteIngress('delete-test-ingress');

    // 验证数据库记录已删除
    $dbIngress = Ingress::where('name', 'delete-test-ingress')->first();
    expect($dbIngress)->toBeNull();

    // 验证 K8s 中的 Ingress 已删除
    expect(function () {
        $this->workspace->cluster->http()
            ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/delete-test-ingress");
    })->toThrow(\Exception::class);
});

test('IngressClassManager 正常工作', function () {
    $manager = new IngressClassManager;

    // 测试可用驱动
    $drivers = $manager->getAvailableDrivers();
    expect($drivers)->toContain('traefik');
    expect($drivers)->toContain('nginx');

    // 测试获取驱动
    $traefikDriver = $manager->driver('traefik');
    expect($traefikDriver->getIngressClassName())->toBe('traefik');

    $nginxDriver = $manager->driver('nginx');
    expect($nginxDriver->getIngressClassName())->toBe('nginx');

    // 测试构建 spec
    $data = [
        'rules' => [
            [
                'host' => 'test.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $spec = $manager->buildIngressSpec('traefik', $data);
    expect($spec)->toHaveKey('ingressClassName');
    expect($spec['ingressClassName'])->toBe('traefik');
    expect($spec)->toHaveKey('rules');
});

test('验证配置失败时抛出异常', function () {
    $invalidData = [
        'name' => 'invalid-ingress',
        'ingress_class' => 'nginx',
        'rules' => [
            [
                // 缺少 host
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            // 缺少 backend
                        ],
                    ],
                ],
            ],
        ],
    ];

    expect(fn () => $this->ingressService->createIngress($invalidData))
        ->toThrow(\Exception::class, '配置验证失败');
});

test('不支持的 Ingress 类型时抛出异常', function () {
    $invalidData = [
        'name' => 'unsupported-ingress',
        'ingress_class' => 'unsupported',
        'rules' => [
            [
                'host' => 'test.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    expect(fn () => $this->ingressService->createIngress($invalidData))
        ->toThrow(\Exception::class, '不支持的 Ingress 类型');
});

test('能够获取集群域名使用情况', function () {
    // 创建多个 Ingress
    $ingress1Data = [
        'name' => 'usage-test-1',
        'ingress_class' => 'traefik',
        'rules' => [
            [
                'host' => 'usage1.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $ingress2Data = [
        'name' => 'usage-test-2',
        'ingress_class' => 'nginx',
        'rules' => [
            [
                'host' => 'usage2.example.com',
                'http' => [
                    'paths' => [
                        [
                            'path' => '/',
                            'pathType' => 'Prefix',
                            'backend' => [
                                'service' => [
                                    'name' => 'test-service',
                                    'port' => [
                                        'number' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    $this->ingressService->createIngress($ingress1Data);
    $this->ingressService->createIngress($ingress2Data);

    $usage = $this->ingressService->getClusterDomainUsage();

    expect($usage)->toHaveKey('usage1.example.com');
    expect($usage)->toHaveKey('usage2.example.com');
    expect($usage['usage1.example.com'][0]['ingress_class'])->toBe('traefik');
    expect($usage['usage2.example.com'][0]['ingress_class'])->toBe('nginx');
});
