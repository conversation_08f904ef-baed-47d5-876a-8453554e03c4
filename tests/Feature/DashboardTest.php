<?php

use App\Models\User;
use App\Models\Workspace;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('guests are redirected to the login page', function () {
    $response = $this->get('/dashboard');
    $response->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function () {
    $user = User::factory()->create();
    $workspace = Workspace::factory()->create(['user_id' => $user->id]);
    $this->actingAs($user);

    // 设置当前工作空间
    session(['workspace_id' => $workspace->id]);

    $response = $this->get('/dashboard');
    $response->assertStatus(200);
});
