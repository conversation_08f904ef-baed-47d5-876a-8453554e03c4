<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\DeploymentService;
use App\Service\StatefulSetService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RealClusterDeploymentTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Workspace $workspace;

    protected Cluster $cluster;

    protected function setUp(): void
    {
        parent::setUp();

        // 跳过这些测试，如果不想与真实集群交互
        if (! env('TEST_REAL_CLUSTER', false)) {
            $this->markTestSkipped('Real cluster tests are disabled');
        }

        // 创建测试用户
        $this->user = User::factory()->create();

        // 从 kubeconfig 解析集群配置
        $kubeconfig = $this->parseKubeconfig();

        // 创建真实的集群配置
        $this->cluster = Cluster::create([
            'name' => 'test-k3s-cluster',
            'server_url' => $kubeconfig['server'],
            'certificate_authority_data' => $kubeconfig['ca_data'],
            'auth_type' => 'certificate',
            'client_certificate_data' => $kubeconfig['cert_data'],
            'client_key_data' => $kubeconfig['key_data'],
            'insecure_skip_tls_verify' => false,
        ]);

        // 创建测试工作空间
        $this->workspace = Workspace::factory()->create([
            'name' => 'test-workspace-'.time(),
            'cluster_id' => $this->cluster->id,
            'namespace' => 'test-namespace-'.time(),
        ]);

        // 设置用户当前工作空间
        $this->user->current_workspace_id = $this->workspace->id;
        $this->user->save();
    }

    protected function tearDown(): void
    {
        // 清理测试资源
        if (isset($this->workspace) && isset($this->cluster)) {
            try {
                $deploymentService = new DeploymentService($this->workspace);
                $deploymentService->deleteDeployment('test-nginx-deployment');
            } catch (\Exception $e) {
                // 忽略删除错误
            }
        }

        parent::tearDown();
    }

    public function test_can_list_deployments_from_real_cluster()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $deployments = $deploymentService->getDeployments();

        $this->assertIsArray($deployments);
        // 空列表也是有效的
    }

    public function test_can_create_and_delete_deployment_in_real_cluster()
    {
        $deploymentService = new DeploymentService($this->workspace);

        $deploymentData = [
            'name' => 'test-nginx-deployment',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'nginx',
                    'image' => 'nginx:alpine',
                    'ports' => [
                        [
                            'name' => 'http',
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'env' => [
                        [
                            'name' => 'TEST_ENV',
                            'value' => 'test-value',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        // 创建 Deployment
        $deployment = $deploymentService->createDeployment($deploymentData);

        $this->assertEquals('test-nginx-deployment', $deployment->name);
        $this->assertEquals($this->workspace->namespace, $deployment->namespace);
        $this->assertEquals(1, $deployment->replicas);
        $this->assertCount(1, $deployment->containers);

        // 验证容器配置
        $container = $deployment->containers[0];
        $this->assertEquals('nginx', $container['name']);
        $this->assertEquals('nginx:alpine', $container['image']);
        $this->assertCount(1, $container['ports']);
        $this->assertCount(1, $container['env']);

        // 获取创建的 Deployment
        $retrievedDeployment = $deploymentService->getDeployment('test-nginx-deployment');
        $this->assertEquals('test-nginx-deployment', $retrievedDeployment->name);

        // 扩容测试
        $scaledDeployment = $deploymentService->scaleDeployment('test-nginx-deployment', 2);
        $this->assertEquals(2, $scaledDeployment->replicas);

        // 删除 Deployment
        $result = $deploymentService->deleteDeployment('test-nginx-deployment');
        $this->assertTrue($result);
    }

    public function test_can_create_and_delete_statefulset_in_real_cluster()
    {
        $statefulSetService = new StatefulSetService($this->workspace);

        $statefulSetData = [
            'name' => 'test-redis-statefulset',
            'replicas' => 1,
            'containers' => [
                [
                    'name' => 'redis',
                    'image' => 'redis:alpine',
                    'ports' => [
                        [
                            'name' => 'redis',
                            'container_port' => 6379,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ];

        // 创建 StatefulSet
        $statefulSet = $statefulSetService->createStatefulSet($statefulSetData);

        $this->assertEquals('test-redis-statefulset', $statefulSet->name);
        $this->assertEquals($this->workspace->namespace, $statefulSet->namespace);
        $this->assertEquals(1, $statefulSet->replicas);
        $this->assertCount(1, $statefulSet->containers);
        $this->assertCount(1, $statefulSet->volumeClaimTemplates);

        // 获取创建的 StatefulSet
        $retrievedStatefulSet = $statefulSetService->getStatefulSet('test-redis-statefulset');
        $this->assertEquals('test-redis-statefulset', $retrievedStatefulSet->name);

        // 扩容测试
        $scaledStatefulSet = $statefulSetService->scaleStatefulSet('test-redis-statefulset', 2);
        $this->assertEquals(2, $scaledStatefulSet->replicas);

        // 删除 StatefulSet
        $result = $statefulSetService->deleteStatefulSet('test-redis-statefulset');
        $this->assertTrue($result);
    }

    protected function parseKubeconfig(): array
    {
        // 直接返回硬编码的配置，从 kubeconfig 文件中提取
        return [
            'server' => 'https://192.168.81.102:6443',
            'ca_data' => 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJlRENDQVIyZ0F3SUJBZ0lCQURBS0JnZ3Foa2pPUFFRREFqQWpNU0V3SHdZRFZRUUREQmhyTTNNdGMyVnkKZG1WeUxXTmhRREUzTlRBMk1USXhNVFF3SGhjTk1qVXdOakl5TVRjd09ETTBXaGNOTXpVd05qSXdNVGN3T0RNMApXakFqTVNFd0h3WURWUVFEREJock0zTXRjMlZ5ZG1WeUxXTmhRREUzTlRBMk1USXhNVFF3V1RBVEJnY3Foa2pPClBRSUJCZ2dxaGtqT1BRTUJCd05DQUFRc1A4aFVKemd1Q0F6RDV1aDE5QXN3QURDWVZ3TG52My9uZjdwdFJkZWoKRUpJTVRxWVg5MHdSTUYreUF2bVpCWVlNSUJHVE1RY01TUE16cGt0aWZsTi9vMEl3UURBT0JnTlZIUThCQWY4RQpCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVUdkN3hQd0crWS9TdHFvKzVWdmlvCmtVS2RiS1l3Q2dZSUtvWkl6ajBFQXdJRFNRQXdSZ0loQUkwTDRKQnNzOFh2cEFtREUzeWdrZFJOYkFwaFltYTAKbTc4bER6M2VYSGFJQWlFQXY5amZVTkhBVm8yYnVnblJRdGNrWHdWdnZ4YWgrTWNWYy9iUE9GdnpNQWs9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K',
            'cert_data' => 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJrakNDQVRlZ0F3SUJBZ0lJR0RvTmZnQWY2WVF3Q2dZSUtvWkl6ajBFQXdJd0l6RWhNQjhHQTFVRUF3d1kKYXpOekxXTnNhV1Z1ZEMxallVQXhOelV3TmpFeU1URTBNQjRYRFRJMU1EWXlNakUzTURnek5Gb1hEVEkyTURZeQpNakUzTURnek5Gb3dNREVYTUJVR0ExVUVDaE1PYzNsemRHVnRPbTFoYzNSbGNuTXhGVEFUQmdOVkJBTVRESE41CmMzUmxiVHBoWkcxcGJqQlpNQk1HQnlxR1NNNDlBZ0VHQ0NxR1NNNDlBd0VIQTBJQUJPRU1tS0VwWm45eCtPaXUKeTBxOWhEN3FKOXVXSDY3MUtoME4rTC9xV0trdFJzaUpWQ2U4dzNsa1BXRTJjTzh5dDAyV3NQMUgxMFEvQmMyeQpVN3lKQkUyalNEQkdNQTRHQTFVZER3RUIvd1FFQXdJRm9EQVRCZ05WSFNVRUREQUtCZ2dyQmdFRkJRY0RBakFmCkJnTlZIU01FR0RBV2dCU1gzSmsxMisxTGNla1RjbXJ6OWE0NlZOc2d3akFLQmdncWhrak9QUVFEQWdOSkFEQkcKQWlFQWlQOTloV2k2a0xXSG10VnE2ZTQxbUdoSzgvZTZyYVpWenBkcmZTY3JNeDRDSVFEdVkzSjNiKytUTVJiaAo0QUJPMm0vVCswMG1qbVZKNlJ4dE13bFRaZ1QrT1E9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlCZGpDQ0FSMmdBd0lCQWdJQkFEQUtCZ2dxaGtqT1BRUURBakFqTVNFd0h3WURWUVFEREJock0zTXRZMnhwClpXNTBMV05oUURFM05UQTJNVEl4TVRRd0hoY05NalV3TmpJeU1UY3dPRE0wV2hjTk16VXdOakl3TVRjd09ETTAKV2pBak1TRXdId1lEVlFRRERCaHJNM010WTJ4cFpXNTBMV05oUURFM05UQTJNVEl4TVRRd1dUQVRCZ2NxaGtqTwpQUUlCQmdncWhrak9QUU1CQndOQ0FBUU90MDlJMHpPb1E3Z3FSMnkzenV2aUFIWW1RaWcrSVBLWGhQSVUxRFo1CmgzV1RGY3JLQXFqTmdSWTAxV3d2YWNBK3kyQVp6UHNkZ1dsWHdHdWx1MEsybzBJd1FEQU9CZ05WSFE4QkFmOEUKQkFNQ0FxUXdEd1lEVlIwVEFRSC9CQVV3QXdFQi96QWRCZ05WSFE0RUZnUVVsOXlaTmR2dFMzSHBFM0pxOC9XdQpPbFRiSU1Jd0NnWUlLb1pJemowRUF3SURSd0F3UkFJZ2UxcnVUQnZKTzRlaTduZkMzUG15VFhHRE0vWUdMYjgwCmMzVjBvQkdMbk9BQ0lDekp6WjM0NGFhSm9IdFp1K2JscS95STVsYXIxNGR6cHNHdklqYWlOTGM4Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K',
            'key_data' => '****************************************************************************************************************************************************************************************************************************************************************************************************************',
        ];
    }
}
