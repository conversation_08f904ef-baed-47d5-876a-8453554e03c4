<?php

namespace Tests\Feature;

use App\Events\UserBalanceChanged;
use App\Models\BillingRecord;
use App\Models\Cluster;
use App\Models\ClusterPricing;
use App\Models\User;
use App\Models\Workspace;
use App\Service\BalanceService;
use App\Service\BillingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class BillingSystemTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Cluster $cluster;

    private ClusterPricing $pricing;

    private Workspace $workspace;

    private BillingService $billingService;

    private BalanceService $balanceService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'current_balance' => 0,
        ]);

        $this->cluster = Cluster::factory()->create();

        $this->pricing = ClusterPricing::create([
            'cluster_id' => $this->cluster->id,
            'memory_price_per_gb' => 10.0,
            'cpu_price_per_core' => 20.0,
            'storage_price_per_gb' => 5.0,
            'loadbalancer_price_per_service' => 15.0,
            'billing_enabled' => true,
        ]);

        $this->workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'status' => 'active',
        ]);

        // 确保工作空间状态为active
        $this->workspace->update(['status' => 'active']);
        $this->workspace->refresh();

        $this->billingService = $this->app->make(BillingService::class);
        $this->balanceService = $this->app->make(BalanceService::class);

        // 配置HTTP Mock，使用实际的工作空间名称
        $this->configureHttpMocks();
    }

    /**
     * 配置HTTP Mock响应
     */
    protected function configureHttpMocks(): void
    {
        Http::fake([
            '*/api/v1/namespaces/*/pods' => Http::response([
                'items' => [
                    [
                        'metadata' => [
                            'name' => 'test-pod',
                            'labels' => [
                                'run.leaflow.cn/workspace' => $this->workspace->name,
                            ],
                        ],
                        'status' => ['phase' => 'Running'],
                        'spec' => [
                            'containers' => [
                                [
                                    'resources' => [
                                        'limits' => [
                                            'memory' => '512Mi',
                                            'cpu' => '500m',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]),
            '*/api/v1/namespaces/*/persistentvolumeclaims' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/services' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/deployments' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/statefulsets' => Http::response(['items' => []]),
        ]);
    }

    /**
     * @test
     * 测试完整的计费流水线
     */
    public function billing_pipeline_processes_workspace_successfully()
    {
        Event::fake();

        // 为用户充值
        $this->balanceService->addBalance($this->user, 100.0, 'manual', null, ['remark' => '流水线测试']);

        // 确保工作空间状态为active（可能被事件监听器修改）
        $this->workspace->update(['status' => 'active']);
        $this->workspace->refresh();

        // 验证工作空间设置
        $this->assertEquals('active', $this->workspace->status);
        $this->assertNotNull($this->workspace->name);
        $this->assertNotNull($this->workspace->namespace);

        // 执行计费流水线
        $results = $this->billingService->processAllClustersBilling();

        // 验证流水线执行成功
        $this->assertTrue($results['summary']['successful_clusters'] > 0);
        $this->assertEquals(1, $results['summary']['total_billable_workspaces']);
        $this->assertTrue(bccomp($results['summary']['total_revenue'], '0', 8) > 0);

        // 验证计费记录被创建
        $this->assertDatabaseHas('billing_records', [
            'workspace_id' => $this->workspace->id,
            'user_id' => $this->user->id,
            'status' => BillingRecord::STATUS_CHARGED,
        ]);

        // 验证用户余额被扣除
        $this->user->refresh();
        $this->assertLessThan(100.0, $this->user->current_balance);
    }

    /**
     * @test
     * 测试余额不足时的欠费处理
     */
    public function pipeline_handles_insufficient_balance_correctly()
    {
        Event::fake();

        // 确保工作空间状态为active
        $this->workspace->update(['status' => 'active']);

        // 不充值，保持零余额
        $this->assertEquals(0, $this->user->current_balance);

        // 执行计费流水线
        $results = $this->billingService->processAllClustersBilling();

        // 验证有欠费工作空间
        $this->assertEquals(1, $results['summary']['total_overdue_workspaces']);

        // 验证工作空间被暂停
        $this->workspace->refresh();
        $this->assertEquals('suspended', $this->workspace->status);
        $this->assertEquals('overdue', $this->workspace->suspension_reason);
        $this->assertNotNull($this->workspace->overdue_amount);
        $this->assertTrue(bccomp($this->workspace->overdue_amount, '0', 4) > 0);

        // 验证计费记录存在但状态为失败
        $this->assertDatabaseHas('billing_records', [
            'workspace_id' => $this->workspace->id,
            'user_id' => $this->user->id,
            'status' => BillingRecord::STATUS_FAILED,
        ]);
    }

    /**
     * @test
     * 测试流水线各阶段的执行
     */
    public function pipeline_phases_execute_in_correct_order()
    {
        Event::fake();

        // 确保工作空间状态为active
        $this->workspace->update(['status' => 'active']);

        // 为用户充值
        $this->balanceService->addBalance($this->user, 50.0, 'manual', null, ['remark' => '阶段测试']);

        // 执行单集群计费流水线
        $result = $this->billingService->processClusterBilling($this->cluster);

        // 验证流水线成功执行
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('phases', $result);

        $phases = $result['phases'];

        // 验证所有阶段都存在
        $expectedPhases = [
            'resource_collection',
            'cost_calculation',
            'billing_record',
            'balance_deduction',
            'overdue_handling',
        ];

        foreach ($expectedPhases as $phaseName) {
            $this->assertArrayHasKey($phaseName, $phases);
            $this->assertTrue($phases[$phaseName]['success']);
            $this->assertArrayHasKey('execution_time_ms', $phases[$phaseName]);
        }

        // 验证资源收集阶段
        $this->assertEquals(1, $phases['resource_collection']['successful_collections']);
        $this->assertEquals(0, $phases['resource_collection']['failed_collections']);

        // 验证成本计算阶段
        $this->assertEquals(1, $phases['cost_calculation']['billable_workspaces']);
        $this->assertEquals(0, $phases['cost_calculation']['zero_cost_workspaces']);

        // 验证计费记录阶段
        $this->assertEquals(1, $phases['billing_record']['records_created']);
        $this->assertEquals(0, $phases['billing_record']['records_failed']);

        // 验证余额扣费阶段
        $this->assertEquals(1, $phases['balance_deduction']['successful_deductions']);
        $this->assertEquals(0, $phases['balance_deduction']['failed_deductions']);

        // 验证欠费处理阶段
        $this->assertEquals(0, $phases['overdue_handling']['overdue_processed']);
    }

    /**
     * @test
     * 测试没有资源使用时的跳过逻辑
     */
    public function pipeline_skips_workspaces_without_resources()
    {
        Event::fake();

        // Mock 空的资源响应
        Http::fake([
            '*/api/v1/namespaces/*/pods' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/persistentvolumeclaims' => Http::response(['items' => []]),
            '*/api/v1/namespaces/*/services' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/deployments' => Http::response(['items' => []]),
            '*/apis/apps/v1/namespaces/*/statefulsets' => Http::response(['items' => []]),
        ]);

        // 执行计费流水线
        $results = $this->billingService->processAllClustersBilling();

        // 验证没有计费工作空间
        $this->assertEquals(0, $results['summary']['total_billable_workspaces']);
        $this->assertEquals('0.00000000', $results['summary']['total_revenue']);

        // 验证没有创建计费记录
        $this->assertDatabaseMissing('billing_records', [
            'workspace_id' => $this->workspace->id,
        ]);
    }

    /**
     * @test
     * 测试暂停工作空间的恢复
     */
    public function pipeline_can_resume_suspended_workspaces()
    {
        Event::fake();

        // 先让工作空间因欠费被暂停
        $this->workspace->update([
            'status' => 'suspended',
            'suspension_reason' => 'overdue',
            'overdue_amount' => 5.0,
            'suspended_at' => now(),
        ]);

        // 为用户充值足够的余额
        $this->balanceService->addBalance($this->user, 20.0, 'manual', null, ['remark' => '恢复测试']);

        // 执行恢复操作
        $resumeResults = $this->billingService->checkAndResumeOverdueWorkspaces();

        // 验证工作空间被恢复
        $this->assertCount(1, $resumeResults);
        $this->assertTrue($resumeResults[0]['success']);
        $this->assertEquals(5.0, $resumeResults[0]['paid_amount']);

        // 验证工作空间状态
        $this->workspace->refresh();
        $this->assertEquals('active', $this->workspace->status);
        $this->assertNull($this->workspace->suspension_reason);
        $this->assertNull($this->workspace->overdue_amount);

        // 验证用户余额被正确扣除
        $this->user->refresh();
        $this->assertEquals(15.0, $this->user->current_balance);
    }

    /**
     * @test
     * 测试计费统计功能
     */
    public function pipeline_generates_correct_statistics()
    {
        Event::fake();

        // 确保工作空间状态为active
        $this->workspace->update(['status' => 'active']);

        // 为用户充值
        $this->balanceService->addBalance($this->user, 100.0, 'manual', null, ['remark' => '统计测试']);

        // 执行计费流水线
        $results = $this->billingService->processAllClustersBilling();

        // 验证批处理摘要
        $summary = $results['summary'];

        $this->assertArrayHasKey('total_billable_workspaces', $summary);
        $this->assertArrayHasKey('total_revenue', $summary);
        $this->assertArrayHasKey('total_overdue_workspaces', $summary);
        $this->assertArrayHasKey('successful_clusters', $summary);
        $this->assertArrayHasKey('failed_clusters', $summary);

        $this->assertEquals(1, $summary['total_billable_workspaces']);
        $this->assertTrue(bccomp($summary['total_revenue'], '0', 8) > 0);
        $this->assertEquals(0, $summary['total_overdue_workspaces']);
        $this->assertEquals(1, $summary['successful_clusters']);
        $this->assertEquals(0, $summary['failed_clusters']);
    }

    /**
     * @test
     * 测试计费被禁用时的处理
     */
    public function pipeline_skips_clusters_with_billing_disabled()
    {
        Event::fake();

        // 禁用集群计费
        $this->cluster->pricing->update(['billing_enabled' => false]);

        // 执行计费流水线
        $results = $this->billingService->processAllClustersBilling();

        // 验证没有集群被处理
        $this->assertEquals(0, $results['summary']['successful_clusters']);
        $this->assertEmpty($results['clusters']);

        // 验证没有创建计费记录
        $this->assertDatabaseMissing('billing_records', [
            'cluster_id' => $this->cluster->id,
        ]);
    }

    /**
     * @test
     * 测试集群定价配置
     */
    public function cluster_pricing_configuration_works()
    {
        $this->assertEquals(10.0, $this->pricing->memory_price_per_gb);
        $this->assertEquals(20.0, $this->pricing->cpu_price_per_core);
        $this->assertEquals(5.0, $this->pricing->storage_price_per_gb);
        $this->assertEquals(15.0, $this->pricing->loadbalancer_price_per_service);
        $this->assertTrue($this->pricing->billing_enabled);
    }

    /**
     * @test
     * 测试价格计算功能
     */
    public function price_calculation_is_accurate()
    {
        // 测试内存价格计算（512Mi = 0.5GB）
        $memoryPrice = $this->pricing->calculateMemoryPricePerMinute(512);
        $expectedMemoryPrice = bcdiv(
            bcmul(bcdiv('512', '1024', 8), '10', 8),
            '43200',
            8
        );
        $this->assertEquals($expectedMemoryPrice, $memoryPrice);

        // 测试CPU价格计算（1000m = 1 core）
        $cpuPrice = $this->pricing->calculateCpuPricePerMinute(1000);
        $expectedCpuPrice = bcdiv('20', '43200', 8);
        $this->assertEquals($expectedCpuPrice, $cpuPrice);

        // 测试存储价格计算
        $storagePrice = $this->pricing->calculateStoragePricePerMinute(10);
        $expectedStoragePrice = bcdiv(bcmul('10', '5', 8), '43200', 8);
        $this->assertEquals($expectedStoragePrice, $storagePrice);

        // 测试LoadBalancer价格计算
        $lbPrice = $this->pricing->calculateLoadBalancerPricePerMinute(2);
        $expectedLbPrice = bcdiv(bcmul('2', '15', 8), '43200', 8);
        $this->assertEquals($expectedLbPrice, $lbPrice);
    }

    /**
     * @test
     * 测试价格预览功能
     */
    public function price_preview_works_correctly()
    {
        $resources = [
            'memory_mi' => 1024,  // 1GB
            'cpu_m' => 1000,      // 1 core
            'storage_gi' => 10,   // 10GB
            'loadbalancer_count' => 1,
        ];

        $preview = $this->pricing->calculatePricePreview($resources);

        $this->assertArrayHasKey('memory_price_per_minute', $preview);
        $this->assertArrayHasKey('cpu_price_per_minute', $preview);
        $this->assertArrayHasKey('storage_price_per_minute', $preview);
        $this->assertArrayHasKey('loadbalancer_price_per_minute', $preview);
        $this->assertArrayHasKey('total_price_per_minute', $preview);

        // 验证总价格是各项之和
        $expectedTotal = bcadd(
            bcadd($preview['memory_price_per_minute'], $preview['cpu_price_per_minute'], 8),
            bcadd($preview['storage_price_per_minute'], $preview['loadbalancer_price_per_minute'], 8),
            8
        );
        $this->assertEquals($expectedTotal, $preview['total_price_per_minute']);
    }

    /**
     * @test
     * 测试计费记录创建
     */
    public function billing_record_creation_works()
    {
        $usage = [
            'memory_mi' => 512,
            'cpu_m' => 500,
            'storage_gi' => 5,
            'loadbalancer_count' => 0,
        ];

        $costs = [
            'memory_cost' => '0.01000000',
            'cpu_cost' => '0.00500000',
            'storage_cost' => '0.00200000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '0.01700000',
        ];

        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => $usage,
            'memory_cost' => $costs['memory_cost'],
            'cpu_cost' => $costs['cpu_cost'],
            'storage_cost' => $costs['storage_cost'],
            'loadbalancer_cost' => $costs['loadbalancer_cost'],
            'total_cost' => $costs['total_cost'],
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $this->assertTrue($record->canCharge());
        $this->assertEquals(BillingRecord::STATUS_PENDING, $record->status);
        $this->assertEquals($usage, $record->resource_usage);
    }

    /**
     * @test
     * 测试计费扣费功能
     */
    public function billing_charge_works_correctly()
    {
        Event::fake();

        // 先为用户添加充值记录，这样才能扣费
        $this->balanceService->addBalance($this->user, 100.0, 'manual', null, ['remark' => '测试充值']);

        // 创建一个小额计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 512],
            'memory_cost' => '1.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '1.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        $oldBalance = $this->user->fresh()->current_balance;

        // 模拟扣费
        $this->balanceService->deductBalance($this->user, 1.0, '测试计费');
        $record->markAsCharged();

        $this->user->refresh();
        $this->assertEquals($oldBalance - 1.0, $this->user->current_balance);
        $this->assertEquals(BillingRecord::STATUS_CHARGED, $record->status);
        $this->assertNotNull($record->charged_at);
    }

    /**
     * @test
     * 测试用户欠费处理
     */
    public function user_overdue_handling_works()
    {
        Event::fake();

        // 设置用户余额很低
        $this->user->update(['current_balance' => 0.5]);

        // 创建一个超过余额的计费记录
        $record = BillingRecord::create([
            'workspace_id' => $this->workspace->id,
            'cluster_id' => $this->cluster->id,
            'user_id' => $this->user->id,
            'billing_start_at' => now()->subMinute(),
            'billing_end_at' => now(),
            'resource_usage' => ['memory_mi' => 1024],
            'memory_cost' => '10.00000000',
            'cpu_cost' => '0.00000000',
            'storage_cost' => '0.00000000',
            'loadbalancer_cost' => '0.00000000',
            'total_cost' => '10.00000000',
            'status' => BillingRecord::STATUS_PENDING,
        ]);

        // 尝试扣费应该失败
        try {
            $this->balanceService->deductBalance($this->user, 10.0, '测试计费');
            $this->fail('应该抛出余额不足异常');
        } catch (\Exception $e) {
            $this->assertStringContainsString('余额不足', $e->getMessage());
        }

        // 标记为失败
        $record->markAsFailed();
        $this->assertEquals(BillingRecord::STATUS_FAILED, $record->status);
    }

    /**
     * @test
     * 测试工作空间暂停功能
     */
    public function workspace_suspension_works()
    {
        // 更新工作空间为欠费状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 50.0000,
            'last_overdue_at' => now(),
        ]);

        $this->assertEquals('suspended', $this->workspace->status);
        $this->assertEquals('overdue', $this->workspace->suspension_reason);
        $this->assertEquals(50.0000, $this->workspace->overdue_amount);
        $this->assertNotNull($this->workspace->suspended_at);
    }

    /**
     * @test
     * 测试工作空间恢复功能
     */
    public function workspace_recovery_works()
    {
        // 首先设置为暂停状态
        $this->workspace->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'suspension_reason' => 'overdue',
            'overdue_amount' => 20.0000,
            'last_overdue_at' => now(),
        ]);

        // 先为用户添加充值记录，确保有余额可扣除
        $this->balanceService->addBalance($this->user, 100.0, 'manual', null, ['remark' => '测试充值']);

        // 模拟恢复功能
        $overdueAmount = $this->workspace->overdue_amount;
        $this->balanceService->deductBalance($this->user, (float) $overdueAmount, '支付欠费');

        $this->workspace->update([
            'status' => 'active',
            'suspended_at' => null,
            'suspension_reason' => null,
            'overdue_amount' => null,
            'last_overdue_at' => null,
        ]);

        $this->assertEquals('active', $this->workspace->status);
        $this->assertNull($this->workspace->suspended_at);
        $this->assertNull($this->workspace->suspension_reason);
        $this->assertNull($this->workspace->overdue_amount);
    }

    /**
     * @test
     * 测试余额变化事件触发
     */
    public function balance_change_events_are_triggered()
    {
        Event::fake();

        $oldBalance = $this->user->current_balance;
        $addAmount = 50.0;

        $this->balanceService->addBalance(
            $this->user,
            $addAmount,
            'manual',
            null,
            ['remark' => '测试充值']
        );

        Event::assertDispatched(UserBalanceChanged::class, function ($event) use ($oldBalance, $addAmount) {
            return $event->user->id === $this->user->id &&
                   $event->oldBalance === (string) $oldBalance &&
                   $event->changeAmount === (string) $addAmount &&
                   $event->changeType === 'topup';
        });
    }

    /**
     * @test
     * 测试bcmath精度计算
     */
    public function bcmath_precision_calculations_work()
    {
        // 测试复杂的精度计算
        $price1 = '12.34567890';
        $price2 = '9.87654321';

        $sum = bcadd($price1, $price2, 8);
        $this->assertEquals('22.22222211', $sum);

        $product = bcmul($price1, '2', 8);
        $this->assertEquals('24.69135780', $product);

        $division = bcdiv($price1, '43200', 8);
        $this->assertEquals('0.00028577', $division);
    }

    /**
     * @test
     * 测试金额格式化函数
     */
    public function amount_formatting_functions_work()
    {
        $amount = 123.456789;

        $formatted = formatAmount($amount);
        $this->assertEquals('123.46', $formatted);

        $formattedWithSymbol = formatAmount($amount, true);
        $this->assertEquals('¥ 123.46', $formattedWithSymbol);

        // formatAmountForCli保留8位小数
        $formattedForCli = formatAmountForCli($amount);
        $this->assertEquals('123.45678900 元', $formattedForCli);
    }
}
