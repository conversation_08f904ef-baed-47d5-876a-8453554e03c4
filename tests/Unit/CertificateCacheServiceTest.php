<?php

use App\Service\CertificateCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

uses(TestCase::class);
uses(RefreshDatabase::class);

beforeEach(function () {
    // 确保测试环境下的存储目录干净
    Storage::deleteDirectory('k8s');
});

afterEach(function () {
    // 清理测试文件
    Storage::deleteDirectory('k8s');
});

test('能够缓存证书文件', function () {
    $testData = base64_encode('test certificate data');
    $type = 'cert';

    $filePath = CertificateCacheService::getCachedCertificateFile($type, $testData);

    expect($filePath)->toBeString();
    expect(file_exists($filePath))->toBeTrue();
    expect(file_get_contents($filePath))->toBe('test certificate data');
});

test('相同数据返回相同文件路径', function () {
    $testData = base64_encode('test certificate data');
    $type = 'cert';

    $filePath1 = CertificateCacheService::getCachedCertificateFile($type, $testData);
    $filePath2 = CertificateCacheService::getCachedCertificateFile($type, $testData);

    expect($filePath1)->toBe($filePath2);
});

test('不同类型的相同数据创建不同文件', function () {
    $testData = base64_encode('test certificate data');

    $certFile = CertificateCacheService::getCachedCertificateFile('cert', $testData);
    $keyFile = CertificateCacheService::getCachedCertificateFile('key', $testData);
    $caFile = CertificateCacheService::getCachedCertificateFile('ca', $testData);

    expect($certFile)->not->toBe($keyFile);
    expect($certFile)->not->toBe($caFile);
    expect($keyFile)->not->toBe($caFile);
});

test('无效的 base64 数据抛出异常', function () {
    expect(fn () => CertificateCacheService::getCachedCertificateFile('cert', 'invalid-base64-data'))
        ->toThrow(\Exception::class);
});

test('能够获取缓存统计信息', function () {
    // 创建一些测试文件
    CertificateCacheService::getCachedCertificateFile('cert', base64_encode('cert data'));
    CertificateCacheService::getCachedCertificateFile('key', base64_encode('key data'));
    CertificateCacheService::getCachedCertificateFile('ca', base64_encode('ca data'));

    $stats = CertificateCacheService::getCacheStats();

    expect($stats)->toHaveKeys(['total_files', 'total_size', 'ca_files', 'cert_files', 'key_files']);
    expect($stats['total_files'])->toBe(3);
    expect($stats['cert_files'])->toBe(1);
    expect($stats['key_files'])->toBe(1);
    expect($stats['ca_files'])->toBe(1);
    expect($stats['total_size'])->toBeGreaterThan(0);
});

test('能够清理所有证书缓存', function () {
    // 创建一些测试文件
    CertificateCacheService::getCachedCertificateFile('cert', base64_encode('cert data'));
    CertificateCacheService::getCachedCertificateFile('key', base64_encode('key data'));

    $statsBefore = CertificateCacheService::getCacheStats();
    expect($statsBefore['total_files'])->toBe(2);

    $cleaned = CertificateCacheService::clearAllCertificates();

    expect($cleaned)->toBe(2);

    $statsAfter = CertificateCacheService::getCacheStats();
    expect($statsAfter['total_files'])->toBe(0);
});

test('能够清理过期的证书缓存', function () {
    // 创建测试文件
    $filePath = CertificateCacheService::getCachedCertificateFile('cert', base64_encode('cert data'));

    // 修改文件修改时间为过去
    $pastTime = time() - 10000; // 设置为过去的时间
    touch($filePath, $pastTime);

    $cleaned = CertificateCacheService::cleanupExpiredCertificates(5000); // 5000秒内的文件保留

    expect($cleaned)->toBe(1);
    expect(file_exists($filePath))->toBeFalse();
});

test('不会清理未过期的证书缓存', function () {
    // 创建测试文件
    $filePath = CertificateCacheService::getCachedCertificateFile('cert', base64_encode('cert data'));

    $cleaned = CertificateCacheService::cleanupExpiredCertificates(604800); // 7天内的文件保留

    expect($cleaned)->toBe(0);
    expect(file_exists($filePath))->toBeTrue();
});

test('缓存目录不存在时统计信息返回空值', function () {
    // 确保 k8s 目录不存在
    Storage::deleteDirectory('k8s');

    $stats = CertificateCacheService::getCacheStats();

    expect($stats['total_files'])->toBe(0);
    expect($stats['total_size'])->toBe(0);
    expect($stats['ca_files'])->toBe(0);
    expect($stats['cert_files'])->toBe(0);
    expect($stats['key_files'])->toBe(0);
});

test('文件权限设置正确', function () {
    $testData = base64_encode('test certificate data');
    $filePath = CertificateCacheService::getCachedCertificateFile('cert', $testData);

    // 检查文件权限是否为 600 (只有所有者可读写)
    $permissions = fileperms($filePath) & 0777;
    expect($permissions)->toBe(0600);
});
