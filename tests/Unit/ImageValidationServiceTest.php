<?php

namespace Tests\Unit;

use App\Models\Workspace;
use App\Service\ImageValidationService;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ImageValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    private Workspace $workspace;

    private ImageValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->workspace = Workspace::factory()->create();
        $this->service = new ImageValidationService($this->workspace);
    }

    public function test_parse_docker_hub_official_image()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('parseImageName');
        $method->setAccessible(true);

        [$registry, $repository, $tag] = $method->invoke($this->service, 'nginx');

        $this->assertEquals('registry-1.docker.io', $registry);
        $this->assertEquals('library/nginx', $repository);
        $this->assertEquals('latest', $tag);
    }

    public function test_parse_docker_hub_official_image_with_tag()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('parseImageName');
        $method->setAccessible(true);

        [$registry, $repository, $tag] = $method->invoke($this->service, 'nginx:1.21');

        $this->assertEquals('registry-1.docker.io', $registry);
        $this->assertEquals('library/nginx', $repository);
        $this->assertEquals('1.21', $tag);
    }

    public function test_parse_docker_hub_organization_image()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('parseImageName');
        $method->setAccessible(true);

        [$registry, $repository, $tag] = $method->invoke($this->service, 'bitnami/nginx:latest');

        $this->assertEquals('registry-1.docker.io', $registry);
        $this->assertEquals('bitnami/nginx', $repository);
        $this->assertEquals('latest', $tag);
    }

    public function test_parse_custom_registry_image()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('parseImageName');
        $method->setAccessible(true);

        [$registry, $repository, $tag] = $method->invoke($this->service, 'example.com/nginx:latest');

        $this->assertEquals('example.com', $registry);
        $this->assertEquals('nginx', $repository);
        $this->assertEquals('latest', $tag);
    }

    public function test_parse_custom_registry_with_port()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('parseImageName');
        $method->setAccessible(true);

        [$registry, $repository, $tag] = $method->invoke($this->service, 'registry.example.com:5000/myapp:v1.0');

        $this->assertEquals('registry.example.com:5000', $registry);
        $this->assertEquals('myapp', $repository);
        $this->assertEquals('v1.0', $tag);
    }

    public function test_validate_docker_hub_image_success()
    {
        // Mock HTTP responses
        $mock = new MockHandler([
            // Auth token request
            new Response(200, [], json_encode(['token' => 'test-token'])),
            // Manifest request
            new Response(200, [], json_encode([
                'layers' => [
                    ['size' => 1000000],
                    ['size' => 2000000],
                ],
                'config' => ['size' => 50000],
            ])),
        ]);

        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        // Use reflection to set the mock client
        $reflection = new \ReflectionClass($this->service);
        $property = $reflection->getProperty('httpClient');
        $property->setAccessible(true);
        $property->setValue($this->service, $client);

        $result = $this->service->validateImageSize('nginx:latest');

        $this->assertTrue($result['valid']);
        $this->assertEquals(3050000, $result['size_bytes']);
        $this->assertEquals(2.91, $result['size_mb']);
        $this->assertEquals(2, $result['layers_count']);
    }

    public function test_validate_multi_arch_image()
    {
        // Mock HTTP responses for multi-arch image
        $mock = new MockHandler([
            // Auth token request
            new Response(200, [], json_encode(['token' => 'test-token'])),
            // Multi-arch manifest request
            new Response(200, [], json_encode([
                'manifests' => [
                    [
                        'platform' => ['architecture' => 'arm64'],
                        'digest' => 'sha256:arm64-digest',
                    ],
                    [
                        'platform' => ['architecture' => 'amd64'],
                        'digest' => 'sha256:amd64-digest',
                    ],
                ],
            ])),
            // amd64 specific manifest request
            new Response(200, [], json_encode([
                'layers' => [
                    ['size' => 5000000],
                ],
                'config' => ['size' => 100000],
            ])),
        ]);

        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        $reflection = new \ReflectionClass($this->service);
        $property = $reflection->getProperty('httpClient');
        $property->setAccessible(true);
        $property->setValue($this->service, $client);

        $result = $this->service->validateImageSize('nginx:latest');

        $this->assertTrue($result['valid']);
        $this->assertEquals(5100000, $result['size_bytes']);
        $this->assertEquals(4.86, $result['size_mb']);
        $this->assertEquals(1, $result['layers_count']);
    }

    public function test_validate_image_failure()
    {
        // Mock HTTP responses for failure
        $mock = new MockHandler([
            new Response(404, [], 'Not Found'),
        ]);

        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);

        $reflection = new \ReflectionClass($this->service);
        $property = $reflection->getProperty('httpClient');
        $property->setAccessible(true);
        $property->setValue($this->service, $client);

        $result = $this->service->validateImageSize('nonexistent:latest');

        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('error', $result);
    }
}
