<?php

namespace Tests\Unit;

use App\Http\Requests\BaseWorkloadValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class BaseWorkloadValidationTest extends TestCase
{
    private $testRequest;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a mock request class that uses the trait
        $this->testRequest = new class extends FormRequest
        {
            use BaseWorkloadValidation;

            public function authorize(): bool
            {
                return true;
            }

            public function rules(): array
            {
                return $this->getBaseRules();
            }

            public function messages(): array
            {
                return $this->getBaseMessages();
            }

            // Expose trait methods for testing
            public function test_validate_resources($validator): void
            {
                $this->validateResources($validator);
            }

            public function test_validate_container_resources(array $resources, int $containerIndex, $validator): void
            {
                $this->validateContainerResources($resources, $containerIndex, $validator);
            }
        };
    }

    public function test_memory_must_be_multiple_of_512()
    {
        $data = [
            'containers' => [
                [
                    'name' => 'test',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 300, // Invalid: not a multiple of 512
                    ],
                ],
            ],
        ];

        $this->testRequest->merge($data);

        $validator = Validator::make($data, $this->testRequest->rules());
        $this->testRequest->testValidateResources($validator);

        $this->assertTrue($validator->fails());
        $this->assertContains('内存必须是 512Mi 的倍数', $validator->errors()->get('containers.0.resources.memory'));
    }

    public function test_cpu_must_be_multiple_of_500()
    {
        $data = [
            'containers' => [
                [
                    'name' => 'test',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'cpu' => 300, // Invalid: not a multiple of 500
                    ],
                ],
            ],
        ];

        $this->testRequest->merge($data);

        $validator = Validator::make($data, $this->testRequest->rules());
        $this->testRequest->testValidateResources($validator);

        $this->assertTrue($validator->fails());
        $this->assertContains('CPU 必须是 500m 的倍数', $validator->errors()->get('containers.0.resources.cpu'));
    }

    public function test_valid_resources_pass_validation()
    {
        $data = [
            'containers' => [
                [
                    'name' => 'test',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512, // Valid: multiple of 512
                        'cpu' => 1000,   // Valid: multiple of 500
                    ],
                ],
            ],
        ];

        $this->testRequest->merge($data);

        $validator = Validator::make($data, $this->testRequest->rules());
        $this->testRequest->testValidateResources($validator);

        $this->assertFalse($validator->fails());
    }

    public function test_container_resources_validation_with_multiple_containers()
    {
        $data = [
            'containers' => [
                [
                    'name' => 'container1',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 512, // Valid
                        'cpu' => 500,    // Valid
                    ],
                ],
                [
                    'name' => 'container2',
                    'image' => 'redis:latest',
                    'resources' => [
                        'memory' => 300, // Invalid: not multiple of 512
                        'cpu' => 1000,   // Valid
                    ],
                ],
            ],
        ];

        $this->testRequest->merge($data);

        $validator = Validator::make($data, $this->testRequest->rules());
        $this->testRequest->testValidateResources($validator);

        $this->assertTrue($validator->fails());

        // First container should pass
        $this->assertEmpty($validator->errors()->get('containers.0.resources.memory'));
        $this->assertEmpty($validator->errors()->get('containers.0.resources.cpu'));

        // Second container should fail memory validation
        $this->assertContains('内存必须是 512Mi 的倍数', $validator->errors()->get('containers.1.resources.memory'));
        $this->assertEmpty($validator->errors()->get('containers.1.resources.cpu'));
    }
}
