<?php

namespace Tests\Unit;

use App\Models\IpPool;
use App\Models\PoolIp;
use App\Models\PortAllocation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PoolIpModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_pool_ip_can_be_created(): void
    {
        $poolIp = PoolIp::factory()->create([
            'ip_address' => '************',
            'port_range_start' => 10000,
            'port_range_end' => 20000,
        ]);

        $this->assertDatabaseHas('pool_ips', [
            'ip_address' => '************',
            'port_range_start' => 10000,
            'port_range_end' => 20000,
            'is_active' => true,
        ]);
    }

    public function test_pool_ip_belongs_to_ip_pool(): void
    {
        $ipPool = IpPool::factory()->create();
        $poolIp = PoolIp::factory()->create(['ip_pool_id' => $ipPool->id]);

        $this->assertInstanceOf(IpPool::class, $poolIp->ipPool);
        $this->assertEquals($ipPool->id, $poolIp->ipPool->id);
    }

    public function test_pool_ip_validates_ipv4_address(): void
    {
        $poolIp = PoolIp::factory()->create(['ip_address' => '************']);
        $this->assertTrue($poolIp->isValidIpAddress());
        $this->assertEquals('IPv4', $poolIp->getIpType());
    }

    public function test_pool_ip_validates_ipv6_address(): void
    {
        $poolIp = PoolIp::factory()->ipv6()->create();
        $this->assertTrue($poolIp->isValidIpAddress());
        $this->assertEquals('IPv6', $poolIp->getIpType());
    }

    public function test_pool_ip_calculates_available_ports(): void
    {
        $poolIp = PoolIp::factory()->create([
            'port_range_start' => 10000,
            'port_range_end' => 10010, // 11 个端口
        ]);

        // 分配 3 个端口
        PortAllocation::factory()->count(3)->create([
            'pool_ip_id' => $poolIp->id,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);

        $this->assertEquals(8, $poolIp->available_ports_count);
        $this->assertEquals(3, $poolIp->used_ports_count);
        $this->assertEquals(27.27, $poolIp->port_utilization);
    }

    public function test_pool_ip_checks_port_in_range(): void
    {
        $poolIp = PoolIp::factory()->create([
            'port_range_start' => 10000,
            'port_range_end' => 20000,
        ]);

        $this->assertTrue($poolIp->isPortInRange(15000));
        $this->assertTrue($poolIp->isPortInRange(10000));
        $this->assertTrue($poolIp->isPortInRange(20000));
        $this->assertFalse($poolIp->isPortInRange(9999));
        $this->assertFalse($poolIp->isPortInRange(20001));
    }

    public function test_pool_ip_checks_port_availability(): void
    {
        $poolIp = PoolIp::factory()->create([
            'port_range_start' => 10000,
            'port_range_end' => 10010,
        ]);

        // 分配端口 10005
        PortAllocation::factory()->create([
            'pool_ip_id' => $poolIp->id,
            'port' => 10005,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);

        $this->assertTrue($poolIp->isPortAvailable(10000));
        $this->assertFalse($poolIp->isPortAvailable(10005));
        $this->assertTrue($poolIp->isPortAvailable(10006));
        $this->assertFalse($poolIp->isPortAvailable(9999)); // 超出范围
    }

    public function test_pool_ip_gets_next_available_port(): void
    {
        $poolIp = PoolIp::factory()->create([
            'port_range_start' => 10000,
            'port_range_end' => 10005,
        ]);

        // 分配前两个端口
        PortAllocation::factory()->create([
            'pool_ip_id' => $poolIp->id,
            'port' => 10000,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);
        PortAllocation::factory()->create([
            'pool_ip_id' => $poolIp->id,
            'port' => 10001,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);

        $nextPort = $poolIp->getNextAvailablePort();
        $this->assertEquals(10002, $nextPort);
    }

    public function test_pool_ip_returns_null_when_no_ports_available(): void
    {
        $poolIp = PoolIp::factory()->create([
            'port_range_start' => 10000,
            'port_range_end' => 10001, // 只有 2 个端口
        ]);

        // 分配所有端口
        PortAllocation::factory()->create([
            'pool_ip_id' => $poolIp->id,
            'port' => 10000,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);
        PortAllocation::factory()->create([
            'pool_ip_id' => $poolIp->id,
            'port' => 10001,
            'status' => PortAllocation::STATUS_ALLOCATED,
        ]);

        $nextPort = $poolIp->getNextAvailablePort();
        $this->assertNull($nextPort);
    }

    public function test_pool_ip_usage_count_methods(): void
    {
        $poolIp = PoolIp::factory()->create(['usage_count' => 5]);

        $poolIp->incrementUsage();
        $this->assertEquals(6, $poolIp->fresh()->usage_count);

        $poolIp->decrementUsage();
        $this->assertEquals(5, $poolIp->fresh()->usage_count);

        // 不能减少到负数
        $poolIp->update(['usage_count' => 0]);
        $poolIp->decrementUsage();
        $this->assertEquals(0, $poolIp->fresh()->usage_count);
    }
}
