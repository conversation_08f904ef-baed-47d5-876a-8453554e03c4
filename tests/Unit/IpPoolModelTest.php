<?php

namespace Tests\Unit;

use App\Models\Cluster;
use App\Models\IpPool;
use App\Models\PoolIp;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class IpPoolModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_ip_pool_can_be_created(): void
    {
        $cluster = Cluster::factory()->create();

        $ipPool = IpPool::factory()->create([
            'cluster_id' => $cluster->id,
            'name' => 'test-pool',
            'allocation_strategy' => IpPool::STRATEGY_LEAST_USED,
        ]);

        $this->assertDatabaseHas('ip_pools', [
            'cluster_id' => $cluster->id,
            'name' => 'test-pool',
            'allocation_strategy' => IpPool::STRATEGY_LEAST_USED,
            'is_active' => true,
        ]);
    }

    public function test_ip_pool_belongs_to_cluster(): void
    {
        $cluster = Cluster::factory()->create();
        $ipPool = IpPool::factory()->create(['cluster_id' => $cluster->id]);

        $this->assertInstanceOf(Cluster::class, $ipPool->cluster);
        $this->assertEquals($cluster->id, $ipPool->cluster->id);
    }

    public function test_ip_pool_has_many_pool_ips(): void
    {
        $ipPool = IpPool::factory()->create();
        $poolIp1 = PoolIp::factory()->create(['ip_pool_id' => $ipPool->id]);
        $poolIp2 = PoolIp::factory()->create(['ip_pool_id' => $ipPool->id]);

        $this->assertCount(2, $ipPool->poolIps);
        $this->assertTrue($ipPool->poolIps->contains($poolIp1));
        $this->assertTrue($ipPool->poolIps->contains($poolIp2));
    }

    public function test_ip_pool_can_get_active_pool_ips(): void
    {
        $ipPool = IpPool::factory()->create();
        $activeIp = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'is_active' => true,
        ]);
        $inactiveIp = PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'is_active' => false,
        ]);

        $activeIps = $ipPool->activePoolIps;
        $this->assertCount(1, $activeIps);
        $this->assertTrue($activeIps->contains($activeIp));
        $this->assertFalse($activeIps->contains($inactiveIp));
    }

    public function test_ip_pool_stats_calculation(): void
    {
        $ipPool = IpPool::factory()->create();

        // 创建一些 IP 地址
        PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'is_active' => true,
            'usage_count' => 5,
        ]);
        PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'is_active' => true,
            'usage_count' => 3,
        ]);
        PoolIp::factory()->create([
            'ip_pool_id' => $ipPool->id,
            'is_active' => false,
            'usage_count' => 2,
        ]);

        $stats = $ipPool->stats;

        $this->assertEquals(3, $stats['total_ips']);
        $this->assertEquals(2, $stats['active_ips']);
        $this->assertEquals(1, $stats['inactive_ips']);
        $this->assertEquals(10, $stats['total_usage']);
        $this->assertEquals(5.0, $stats['average_usage']);
    }

    public function test_get_available_strategies(): void
    {
        $strategies = IpPool::getAvailableStrategies();

        $this->assertIsArray($strategies);
        $this->assertArrayHasKey(IpPool::STRATEGY_LEAST_USED, $strategies);
        $this->assertArrayHasKey(IpPool::STRATEGY_ROUND_ROBIN, $strategies);
        $this->assertArrayHasKey(IpPool::STRATEGY_RANDOM, $strategies);
    }

    public function test_active_scope(): void
    {
        $activePool = IpPool::factory()->create(['is_active' => true]);
        $inactivePool = IpPool::factory()->create(['is_active' => false]);

        $activePools = IpPool::active()->get();

        $this->assertCount(1, $activePools);
        $this->assertTrue($activePools->contains($activePool));
        $this->assertFalse($activePools->contains($inactivePool));
    }
}
