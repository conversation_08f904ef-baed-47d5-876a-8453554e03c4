<?php

namespace Tests\Unit\Http\Requests;

use App\Http\Requests\BaseWorkloadValidation;
use Illuminate\Foundation\Http\FormRequest;
use Tests\TestCase;

class BaseWorkloadValidationTest extends TestCase
{
    protected function createValidationTestRequest(array $data = []): TestRequest
    {
        $request = new TestRequest;
        $request->merge($data);

        return $request;
    }

    public function test_base_rules_validation()
    {
        $request = $this->createValidationTestRequest([
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'container_port' => 80,
                            'protocol' => 'TCP',
                        ],
                    ],
                    'resources' => [
                        'memory' => 512,
                        'cpu' => 500,
                    ],
                ],
            ],
        ]);

        $validator = $this->app['validator']->make(
            $request->all(),
            $request->getPublicBaseRules()
        );

        $this->assertTrue($validator->passes());
    }

    public function test_invalid_container_port()
    {
        $request = $this->createValidationTestRequest([
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'ports' => [
                        [
                            'container_port' => 70000, // 超出范围
                            'protocol' => 'TCP',
                        ],
                    ],
                ],
            ],
        ]);

        $validator = $this->app['validator']->make(
            $request->all(),
            $request->getPublicBaseRules()
        );

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('containers.0.ports.0.container_port', $validator->errors()->toArray());
    }

    public function test_resource_validation()
    {
        $request = $this->createValidationTestRequest([
            'containers' => [
                [
                    'name' => 'test-container',
                    'image' => 'nginx:latest',
                    'resources' => [
                        'memory' => 300, // 小于 512
                        'cpu' => 200,    // 小于 500
                    ],
                ],
            ],
        ]);

        $validator = $this->app['validator']->make(
            $request->all(),
            $request->getPublicBaseRules()
        );

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('containers.0.resources.memory', $validator->errors()->toArray());
        $this->assertArrayHasKey('containers.0.resources.cpu', $validator->errors()->toArray());
    }

    public function test_name_rules_for_create()
    {
        $request = $this->createValidationTestRequest();

        $rules = $request->getPublicNameRulesForCreate('Deployment');

        $this->assertArrayHasKey('name', $rules);
        $this->assertContains('required', $rules['name']);
        $this->assertContains('string', $rules['name']);
        $this->assertContains('max:63', $rules['name']);
    }

    public function test_invalid_name_format()
    {
        $request = $this->createValidationTestRequest([
            'name' => 'INVALID-NAME', // 大写字母不允许
        ]);

        $validator = $this->app['validator']->make(
            $request->all(),
            $request->getPublicNameRulesForCreate('Deployment')
        );

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }
}

class TestRequest extends FormRequest
{
    use BaseWorkloadValidation;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [];
    }

    // 暴露受保护的方法用于测试
    public function getPublicBaseRules(): array
    {
        return $this->getBaseRules();
    }

    public function getPublicNameRulesForCreate(string $entityType): array
    {
        return $this->getNameRulesForCreate($entityType);
    }
}
