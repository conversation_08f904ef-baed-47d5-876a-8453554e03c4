<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class PermissionConversionTest extends TestCase
{
    /**
     * 测试八进制权限转换为十进制
     */
    public function test_octal_permission_conversion()
    {
        // 测试常见的权限格式
        $this->assertEquals(420, convertOctalPermissionToDecimal('644'));
        $this->assertEquals(420, convertOctalPermissionToDecimal('0644'));
        $this->assertEquals(384, convertOctalPermissionToDecimal('600'));
        $this->assertEquals(384, convertOctalPermissionToDecimal('0600'));
        $this->assertEquals(511, convertOctalPermissionToDecimal('777'));
        $this->assertEquals(511, convertOctalPermissionToDecimal('0777'));
        $this->assertEquals(493, convertOctalPermissionToDecimal('755'));

        // 测试边界情况
        $this->assertEquals(0, convertOctalPermissionToDecimal('0'));
        $this->assertEquals(0, convertOctalPermissionToDecimal('000'));
        $this->assertNull(convertOctalPermissionToDecimal(null));
        $this->assertNull(convertOctalPermissionToDecimal(''));

        // 测试无效输入
        $this->assertNull(convertOctalPermissionToDecimal('888')); // 包含非八进制数字
        $this->assertNull(convertOctalPermissionToDecimal('abc')); // 非数字
        $this->assertNull(convertOctalPermissionToDecimal('1234')); // 超过3位

        // 测试数字输入
        $this->assertEquals(420, convertOctalPermissionToDecimal(644));
        $this->assertEquals(384, convertOctalPermissionToDecimal(600));
    }

    /**
     * 测试十进制权限转换为八进制
     */
    public function test_decimal_permission_conversion()
    {
        // 测试常见的权限格式
        $this->assertEquals('644', convertDecimalPermissionToOctal(420));
        $this->assertEquals('600', convertDecimalPermissionToOctal(384));
        $this->assertEquals('777', convertDecimalPermissionToOctal(511));
        $this->assertEquals('755', convertDecimalPermissionToOctal(493));

        // 测试边界情况
        $this->assertEquals('000', convertDecimalPermissionToOctal(0));
        $this->assertNull(convertDecimalPermissionToOctal(null));

        // 测试三位数格式
        $this->assertEquals('007', convertDecimalPermissionToOctal(7));
        $this->assertEquals('077', convertDecimalPermissionToOctal(63));
    }

    /**
     * 测试双向转换的一致性
     */
    public function test_round_trip_conversion()
    {
        $testCases = ['644', '600', '755', '777', '000', '007', '077'];

        foreach ($testCases as $originalOctal) {
            $decimal = convertOctalPermissionToDecimal($originalOctal);
            $backToOctal = convertDecimalPermissionToOctal($decimal);

            // 格式化原始八进制为3位数进行比较
            $expectedOctal = sprintf('%03s', ltrim($originalOctal, '0') ?: '0');
            $this->assertEquals($expectedOctal, $backToOctal, "Round trip failed for {$originalOctal}");
        }
    }
}
