<?php

namespace Tests\Unit;

use App\Models\Workspace;
use App\Service\StorageService;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

class StorageServiceTest extends TestCase
{
    public function test_validates_storage_size_minimum()
    {
        $workspace = $this->createMock(Workspace::class);
        $service = new StorageService($workspace);

        // 使用反射来访问受保护的方法
        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('validateStorageSize');
        $method->setAccessible(true);

        // 测试小于最小值的情况
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('存储容量不能小于 512Mi');
        $method->invoke($service, 256);
    }

    public function test_validates_storage_size_multiple_of_512()
    {
        $workspace = $this->createMock(Workspace::class);
        $service = new StorageService($workspace);

        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('validateStorageSize');
        $method->setAccessible(true);

        // 测试不是 512 倍数的情况
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('存储容量必须是 512Mi 的倍数');
        $method->invoke($service, 700);
    }

    public function test_validates_storage_size_valid_values()
    {
        $workspace = $this->createMock(Workspace::class);
        $service = new StorageService($workspace);

        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('validateStorageSize');
        $method->setAccessible(true);

        // 测试有效值（不应该抛出异常）
        $validSizes = [512, 1024, 1536, 2048, 5120];

        foreach ($validSizes as $size) {
            try {
                $method->invoke($service, $size);
                $this->assertTrue(true); // 如果没有异常，测试通过
            } catch (\Exception $e) {
                $this->fail("Valid size {$size} should not throw exception: ".$e->getMessage());
            }
        }
    }

    public function test_parses_size_to_mi_correctly()
    {
        $workspace = $this->createMock(Workspace::class);
        $service = new StorageService($workspace);

        $reflection = new ReflectionClass($service);
        $method = $reflection->getMethod('parseSizeToMi');
        $method->setAccessible(true);

        // 测试各种格式的解析
        $testCases = [
            '512Mi' => 512,
            '1Gi' => 1024,
            '2Gi' => 2048,
            '1024Ki' => 1,
            '1M' => 1,
            '2G' => 2048,
            '' => 0,
        ];

        foreach ($testCases as $input => $expected) {
            $result = $method->invoke($service, $input);
            $this->assertEquals($expected, $result, "Failed to parse '{$input}' correctly");
        }
    }
}
