<?php

namespace Tests\Unit\DTOs;

use App\DTOs\MetricsDTO;
use PHPUnit\Framework\TestCase;

class MetricsDTOTest extends TestCase
{
    public function test_normalizes_cpu_to_millicores()
    {
        // 测试纳核格式 (n)
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '4246440n',
                        'memory' => '100Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('4.25m', $dto->containers[0]['usage']['cpu']);

        // 测试核格式 (无单位)
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0.5',
                        'memory' => '100Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('500m', $dto->containers[0]['usage']['cpu']);

        // 测试毫核格式 (m)
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '250m',
                        'memory' => '100Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('250m', $dto->containers[0]['usage']['cpu']);

        // 测试零值
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0',
                        'memory' => '100Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('0m', $dto->containers[0]['usage']['cpu']);
    }

    public function test_normalizes_memory_to_mi()
    {
        // 测试 Ki 格式
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '713900Ki',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('697.17Mi', $dto->containers[0]['usage']['memory']);

        // 测试 Gi 格式
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '2.5Gi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('2560Mi', $dto->containers[0]['usage']['memory']);

        // 测试 Mi 格式
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '256Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('256Mi', $dto->containers[0]['usage']['memory']);

        // 测试字节格式 (无单位)
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'test',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '104857600', // 100MB in bytes
                    ],
                ],
            ],
        ]);

        $this->assertEquals('100Mi', $dto->containers[0]['usage']['memory']);
    }

    public function test_calculates_total_cpu_usage()
    {
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'container1',
                    'usage' => [
                        'cpu' => '100m',
                        'memory' => '100Mi',
                    ],
                ],
                [
                    'name' => 'container2',
                    'usage' => [
                        'cpu' => '200m',
                        'memory' => '200Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('300m', $dto->getTotalCpuUsage());
    }

    public function test_calculates_total_memory_usage()
    {
        // 测试 Mi 结果
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'container1',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '400Mi',
                    ],
                ],
                [
                    'name' => 'container2',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '500Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('900Mi', $dto->getTotalMemoryUsage());

        // 测试 Gi 结果
        $dto = MetricsDTO::fromK8sResource([
            'containers' => [
                [
                    'name' => 'container1',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '800Mi',
                    ],
                ],
                [
                    'name' => 'container2',
                    'usage' => [
                        'cpu' => '0m',
                        'memory' => '500Mi',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('1.27Gi', $dto->getTotalMemoryUsage());
    }

    public function test_real_world_example()
    {
        $dto = MetricsDTO::fromK8sResource([
            'timestamp' => '2025-07-01T10:23:14Z',
            'window' => '25.073s',
            'containers' => [
                [
                    'name' => 'minecraft',
                    'usage' => [
                        'cpu' => '4246440n',
                        'memory' => '713900Ki',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('4.25m', $dto->containers[0]['usage']['cpu']);
        $this->assertEquals('697.17Mi', $dto->containers[0]['usage']['memory']);

        $dto = MetricsDTO::fromK8sResource([
            'timestamp' => '2025-07-01T10:23:06Z',
            'window' => '18.853s',
            'containers' => [
                [
                    'name' => 'mysql',
                    'usage' => [
                        'cpu' => '7100461n',
                        'memory' => '555520Ki',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('7.1m', $dto->containers[0]['usage']['cpu']);
        $this->assertEquals('542.5Mi', $dto->containers[0]['usage']['memory']);

        $dto = MetricsDTO::fromK8sResource([
            'timestamp' => '2025-07-01T10:23:08Z',
            'window' => '19.913s',
            'containers' => [
                [
                    'name' => 'nginx',
                    'usage' => [
                        'cpu' => '0',
                        'memory' => '20708Ki',
                    ],
                ],
            ],
        ]);

        $this->assertEquals('0m', $dto->containers[0]['usage']['cpu']);
        $this->assertEquals('20.22Mi', $dto->containers[0]['usage']['memory']);
    }
}
