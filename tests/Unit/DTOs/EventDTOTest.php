<?php

namespace Tests\Unit\DTOs;

use App\DTOs\EventDTO;
use App\DTOs\NamespaceEventsDTO;
use PHPUnit\Framework\TestCase;

class EventDTOTest extends TestCase
{
    public function test_event_dto_from_k8s_resource()
    {
        $k8sData = [
            'type' => 'Warning',
            'reason' => 'FailedScheduling',
            'message' => 'pod has unbound immediate PersistentVolumeClaims',
            'firstTimestamp' => '2023-01-01T00:00:00Z',
            'lastTimestamp' => '2023-01-01T00:01:00Z',
            'count' => 5,
            'source' => [
                'component' => 'default-scheduler',
            ],
            'involvedObject' => [
                'kind' => 'Pod',
                'name' => 'test-pod',
                'uid' => '12345678-1234-1234-1234-123456789012',
            ],
        ];

        $dto = EventDTO::fromK8sResource($k8sData);

        $this->assertEquals('Warning', $dto->type);
        $this->assertEquals('FailedScheduling', $dto->reason);
        $this->assertEquals('pod has unbound immediate PersistentVolumeClaims', $dto->message);
        $this->assertEquals('2023-01-01T00:01:00Z', $dto->timestamp);
        $this->assertEquals('2023-01-01T00:00:00Z', $dto->firstTimestamp);
        $this->assertEquals('2023-01-01T00:01:00Z', $dto->lastTimestamp);
        $this->assertEquals(5, $dto->count);
        $this->assertEquals('default-scheduler', $dto->source);
        $this->assertEquals('Pod', $dto->involvedObject['kind']);
        $this->assertEquals('test-pod', $dto->involvedObject['name']);
        $this->assertTrue($dto->isWarning());
        $this->assertTrue($dto->involvesResource('Pod', 'test-pod'));
        $this->assertFalse($dto->involvesResource('Deployment', 'test-pod'));
    }

    public function test_namespace_events_dto_from_k8s_resource()
    {
        $k8sData = [
            [
                'type' => 'Warning',
                'reason' => 'FailedScheduling',
                'message' => 'pod has unbound immediate PersistentVolumeClaims',
                'firstTimestamp' => '2023-01-01T00:00:00Z',
                'lastTimestamp' => '2023-01-01T00:01:00Z',
                'count' => 5,
                'source' => [
                    'component' => 'default-scheduler',
                ],
                'involvedObject' => [
                    'kind' => 'Pod',
                    'name' => 'test-pod-1',
                    'uid' => '12345678-1234-1234-1234-123456789012',
                ],
            ],
            [
                'type' => 'Normal',
                'reason' => 'Started',
                'message' => 'Started container',
                'firstTimestamp' => '2023-01-01T00:02:00Z',
                'lastTimestamp' => '2023-01-01T00:02:00Z',
                'count' => 1,
                'source' => [
                    'component' => 'kubelet',
                ],
                'involvedObject' => [
                    'kind' => 'Pod',
                    'name' => 'test-pod-2',
                    'uid' => '*************-4321-4321-************',
                ],
            ],
        ];

        $dto = NamespaceEventsDTO::fromK8sResource($k8sData);

        $this->assertEquals(2, $dto->getEventCount());
        $this->assertEquals(1, $dto->getWarningEventCount());

        $warningEvents = $dto->getWarningEvents();
        $this->assertCount(1, $warningEvents);
        $this->assertEquals('FailedScheduling', $warningEvents[0]->reason);

        $podEvents = $dto->getResourceEvents('Pod', 'test-pod-1');
        $this->assertCount(1, $podEvents);
        $this->assertEquals('test-pod-1', $podEvents[0]->involvedObject['name']);
    }
}
