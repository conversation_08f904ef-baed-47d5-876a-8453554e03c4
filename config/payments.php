<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认支付网关
    |--------------------------------------------------------------------------
    |
    | 当没有指定支付网关时使用的默认网关
    |
    */
    'default' => env('PAYMENT_DEFAULT_GATEWAY', 'alipay'),

    /*
    |--------------------------------------------------------------------------
    | 支付网关配置
    |--------------------------------------------------------------------------
    |
    | 配置所有可用的支付网关，键名为支付网关标识符
    |
    */
    'gateways' => [
        'test_payment' => [
            'class' => \App\Service\PaymentGateways\TestPaymentGateway::class,
            'name' => '测试支付',
            'description' => '仅用于测试的支付方式',
            'enabled' => env('PAYMENT_TEST_ENABLED', true),
            'config' => [
                'auto_success' => env('PAYMENT_TEST_AUTO_SUCCESS', true),
                'delay_seconds' => env('PAYMENT_TEST_DELAY', 0),
            ],
        ],

        'manual' => [
            'class' => \App\Service\PaymentGateways\ManualGateway::class,
            'name' => '手动充值',
            'description' => '管理员手动添加余额',
            'enabled' => env('PAYMENT_MANUAL_ENABLED', true),
            'config' => [
                'require_admin' => true,
                'auto_approve' => env('PAYMENT_MANUAL_AUTO_APPROVE', true),
            ],
        ],

        'redeem_code' => [
            'class' => \App\Service\PaymentGateways\RedeemCodeGateway::class,
            'name' => '兑换码',
            'description' => '使用兑换码进行充值',
            'enabled' => env('PAYMENT_REDEEM_CODE_ENABLED', true),
            'config' => [
                'allow_multiple_use' => true,
                'log_usage' => true,
            ],
        ],

        'alipay' => [
            'class' => \App\Service\PaymentGateways\AlipayGateway::class,
            'name' => '支付宝',
            'description' => '使用支付宝进行在线支付',
            'enabled' => env('PAYMENT_ALIPAY_ENABLED', false),
            'config' => [
                'app_id' => env('ALIPAY_APP_ID'),
                'private_key' => env('ALIPAY_PRIVATE_KEY'),
                'public_key' => env('ALIPAY_PUBLIC_KEY'),
                'sandbox' => env('ALIPAY_SANDBOX', false),
            ],
        ],

        'wechat' => [
            'class' => \App\Service\PaymentGateways\WechatGateway::class,
            'name' => '微信支付',
            'description' => '使用微信支付进行在线支付',
            'enabled' => env('PAYMENT_WECHAT_ENABLED', false),
            'config' => [
                'app_id' => env('WECHAT_APP_ID'),
                'mch_id' => env('WECHAT_MCH_ID'),
                'key' => env('WECHAT_KEY'),
                'cert_path' => env('WECHAT_CERT_PATH'),
                'key_path' => env('WECHAT_KEY_PATH'),
                'sandbox' => env('WECHAT_SANDBOX', false),
            ],
        ],

        'bank_card' => [
            'class' => \App\Service\PaymentGateways\BankCardGateway::class,
            'name' => '银行卡',
            'description' => '使用银行卡进行在线支付',
            'enabled' => env('PAYMENT_BANK_CARD_ENABLED', false),
            'config' => [
                'merchant_id' => env('BANK_MERCHANT_ID'),
                'private_key' => env('BANK_PRIVATE_KEY'),
                'public_key' => env('BANK_PUBLIC_KEY'),
                'gateway_url' => env('BANK_GATEWAY_URL'),
                'sandbox' => env('BANK_SANDBOX', false),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 支付回调路由配置
    |--------------------------------------------------------------------------
    |
    | 各个支付网关的回调路由名称配置
    |
    */
    'callback_routes' => [
        'alipay' => 'payment.callback',
        'wechat' => 'payment.callback',
        'bank_card' => 'payment.callback',
        'test_payment' => 'payment.callback',
    ],

    /*
    |--------------------------------------------------------------------------
    | 支付限制配置
    |--------------------------------------------------------------------------
    |
    | 支付金额和频率限制
    |
    */
    'limits' => [
        'min_amount' => env('PAYMENT_MIN_AMOUNT', 1),
        'max_amount' => env('PAYMENT_MAX_AMOUNT', 10000),
        'daily_limit' => env('PAYMENT_DAILY_LIMIT', 50000),
        'monthly_limit' => env('PAYMENT_MONTHLY_LIMIT', 100000),
    ],

    /*
    |--------------------------------------------------------------------------
    | 退款配置
    |--------------------------------------------------------------------------
    |
    | 退款相关配置
    |
    */
    'refund' => [
        'enabled' => env('PAYMENT_REFUND_ENABLED', true),
        'auto_approve_threshold' => env('PAYMENT_REFUND_AUTO_APPROVE_THRESHOLD', 100),
        'timeout_days' => env('PAYMENT_REFUND_TIMEOUT_DAYS', 7),
    ],

    /*
    |--------------------------------------------------------------------------
    | 日志配置
    |--------------------------------------------------------------------------
    |
    | 支付相关日志配置
    |
    */
    'logging' => [
        'enabled' => env('PAYMENT_LOGGING_ENABLED', true),
        'channel' => env('PAYMENT_LOG_CHANNEL', 'daily'),
        'level' => env('PAYMENT_LOG_LEVEL', 'info'),
    ],
];
