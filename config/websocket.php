<?php

return [
    'websocket_url' => env('WEBSOCKET_URL', 'ws://127.0.0.1:8282'),
    /*
    |--------------------------------------------------------------------------
    | WebSocket Server Configuration
    |--------------------------------------------------------------------------
    */
    'server' => [
        'host' => env('WEBSOCKET_HOST', '127.0.0.1'),
        'port' => env('WEBSOCKET_PORT', 8282),
        'worker_count' => env('WEBSOCKET_WORKER_COUNT', 4),
    ],

    /*
    |--------------------------------------------------------------------------
    | JWT Configuration
    |--------------------------------------------------------------------------
    */
    'jwt' => [
        'algorithm' => env('WEBSOCKET_JWT_ALGORITHM', 'RS256'),
        'ttl' => env('WEBSOCKET_JWT_TTL', 3600), // 1 hour
        'private_key_path' => storage_path('jwt/private.pem'),
        'public_key_path' => storage_path('jwt/public.pem'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Pod Terminal Configuration
    |--------------------------------------------------------------------------
    */
    'terminal' => [
        'timeout' => env('WEBSOCKET_TERMINAL_TIMEOUT', 300), // 5 minutes
        'buffer_size' => env('WEBSOCKET_TERMINAL_BUFFER_SIZE', 4096),
    ],
];
