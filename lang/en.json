{"(and :count more error)": "(and :count more error)", "(and :count more errors)": "(and :count more error)|(and :count more errors)|(and :count more errors)", "A decryption key is required.": "A decryption key is required.", "All rights reserved.": "All rights reserved.", "Encrypted environment file already exists.": "Encrypted environment file already exists.", "Encrypted environment file not found.": "Encrypted environment file not found.", "Environment file already exists.": "Environment file already exists.", "Environment file not found.": "Environment file not found.", "errors": "errors", "Forbidden": "Forbidden", "Go to page :page": "Go to page :page", "Hello!": "Hello!", "If you did not create an account, no further action is required.": "If you did not create an account, no further action is required.", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:", "Invalid filename.": "Invalid filename.", "Invalid JSON was returned from the route.": "Invalid JSON was returned from the route.", "length": "length", "Location": "Location", "Login": "<PERSON><PERSON>", "Logout": "Logout", "Not Found": "Not Found", "of": "of", "Page Expired": "Page Expired", "Pagination Navigation": "Pagination Navigation", "Payment Required": "Payment Required", "Please click the button below to verify your email address.": "Please click the button below to verify your email address.", "Regards,": "<PERSON><PERSON>,", "Register": "Register", "Reset Password": "Reset Password", "Reset Password Notification": "Reset Password Notification", "results": "results", "Server Error": "Server Error", "Service Unavailable": "Service Unavailable", "Showing": "Showing", "The given data was invalid.": "The given data was invalid.", "The response is not a streamed response.": "The response is not a streamed response.", "The response is not a view.": "The response is not a view.", "This action is unauthorized.": "This action is unauthorized.", "This password reset link will expire in :count minutes.": "This password reset link will expire in :count minutes.", "to": "to", "Toggle navigation": "Toggle navigation", "Too Many Requests": "Too Many Requests", "Unauthorized": "Unauthorized", "Verify Email Address": "Verify Em<PERSON> Address", "Whoops!": "Whoops!", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account."}