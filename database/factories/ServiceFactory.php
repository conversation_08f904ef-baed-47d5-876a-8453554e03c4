<?php

namespace Database\Factories;

use App\Models\Service;
use App\Models\Workspace;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'workspace_id' => Workspace::factory(),
            'name' => $this->faker->unique()->slug(2),
            'type' => Service::TYPE_CLUSTER_IP,
            'ports' => [
                [
                    'name' => 'http',
                    'port' => 80,
                    'targetPort' => 8080,
                    'protocol' => 'TCP',
                ],
            ],
            'selector' => [
                'app' => $this->faker->slug(2),
            ],
            'session_affinity' => Service::SESSION_AFFINITY_NONE,
            'external_traffic_policy' => Service::EXTERNAL_TRAFFIC_POLICY_CLUSTER,
            'allow_shared_ip' => false,
            'status' => Service::STATUS_PENDING,
        ];
    }

    /**
     * 创建 LoadBalancer 类型的 Service
     */
    public function loadBalancer(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Service::TYPE_LOAD_BALANCER,
        ]);
    }

    /**
     * 创建 ClusterIP 类型的 Service
     */
    public function clusterIp(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Service::TYPE_CLUSTER_IP,
        ]);
    }

    /**
     * 设置状态为活跃
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Service::STATUS_ACTIVE,
        ]);
    }

    /**
     * 设置状态为失败
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Service::STATUS_FAILED,
            'status_message' => $this->faker->sentence(),
        ]);
    }

    /**
     * 允许 IP 共享
     */
    public function withSharedIp(): static
    {
        return $this->state(fn (array $attributes) => [
            'allow_shared_ip' => true,
            'external_traffic_policy' => Service::EXTERNAL_TRAFFIC_POLICY_CLUSTER, // 强制为 Cluster
        ]);
    }

    /**
     * 设置多个端口
     */
    public function withMultiplePorts(): static
    {
        return $this->state(fn (array $attributes) => [
            'ports' => [
                [
                    'name' => 'http',
                    'port' => 80,
                    'targetPort' => 8080,
                    'protocol' => 'TCP',
                ],
                [
                    'name' => 'https',
                    'port' => 443,
                    'targetPort' => 8443,
                    'protocol' => 'TCP',
                ],
            ],
        ]);
    }
}
