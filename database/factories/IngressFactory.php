<?php

namespace Database\Factories;

use App\Models\Ingress;
use App\Models\Workspace;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ingress>
 */
class IngressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'workspace_id' => Workspace::factory(),
            'name' => 'ingress-'.$this->faker->unique()->slug(2),
            'ingress_class' => $this->faker->randomElement([Ingress::CLASS_TRAEFIK, Ingress::CLASS_NGINX]),
            'domains' => [
                [
                    'host' => $this->faker->domainName(),
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'tls_config' => null,
            'annotations' => [],
            'labels' => [],
            'status' => Ingress::STATUS_ACTIVE,
            'status_message' => null,
        ];
    }

    /**
     * 指示 Ingress 状态为待处理
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Ingress::STATUS_PENDING,
        ]);
    }

    /**
     * 指示 Ingress 状态为失败
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Ingress::STATUS_FAILED,
            'status_message' => 'Failed to create ingress',
        ]);
    }

    /**
     * 使用 Traefik Ingress 类
     */
    public function traefik(): static
    {
        return $this->state(fn (array $attributes) => [
            'ingress_class' => Ingress::CLASS_TRAEFIK,
        ]);
    }

    /**
     * 使用 Nginx Ingress 类
     */
    public function nginx(): static
    {
        return $this->state(fn (array $attributes) => [
            'ingress_class' => Ingress::CLASS_NGINX,
        ]);
    }

    /**
     * 使用泛域名
     */
    public function withWildcardDomain(): static
    {
        return $this->state(fn (array $attributes) => [
            'domains' => [
                [
                    'host' => '*.'.$this->faker->domainName(),
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 使用多个域名
     */
    public function withMultipleDomains(): static
    {
        return $this->state(fn (array $attributes) => [
            'domains' => [
                [
                    'host' => $this->faker->domainName(),
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service-1',
                                        'port' => [
                                            'number' => 80,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'host' => $this->faker->domainName(),
                    'http' => [
                        'paths' => [
                            [
                                'path' => '/api',
                                'pathType' => 'Prefix',
                                'backend' => [
                                    'service' => [
                                        'name' => 'test-service-2',
                                        'port' => [
                                            'number' => 8080,
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /**
     * 使用 TLS 配置
     */
    public function withTls(): static
    {
        return $this->state(function (array $attributes) {
            $hosts = [];
            foreach ($attributes['domains'] as $domain) {
                if (isset($domain['host'])) {
                    $hosts[] = $domain['host'];
                }
            }

            return [
                'tls_config' => [
                    [
                        'hosts' => $hosts,
                        'secretName' => 'tls-secret-'.$this->faker->slug(2),
                    ],
                ],
            ];
        });
    }
}
