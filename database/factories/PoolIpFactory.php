<?php

namespace Database\Factories;

use App\Models\IpPool;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PoolIp>
 */
class PoolIpFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'ip_pool_id' => IpPool::factory(),
            'ip_address' => $this->faker->ipv4(),
            'port_range_start' => 10000,
            'port_range_end' => 30000,
            'usage_count' => 0,
            'is_active' => true,
        ];
    }

    /**
     * 创建 IPv6 地址
     */
    public function ipv6(): static
    {
        return $this->state(fn (array $attributes) => [
            'ip_address' => $this->faker->ipv6(),
        ]);
    }

    /**
     * 创建不活跃的 IP
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * 设置使用次数
     */
    public function withUsageCount(int $count): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_count' => $count,
        ]);
    }

    /**
     * 设置端口范围
     */
    public function withPortRange(int $start, int $end): static
    {
        return $this->state(fn (array $attributes) => [
            'port_range_start' => $start,
            'port_range_end' => $end,
        ]);
    }
}
