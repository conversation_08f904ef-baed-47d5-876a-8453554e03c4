<?php

namespace Database\Factories;

use App\Models\TopUpRecord;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TopUpRecord>
 */
class TopUpRecordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->randomFloat(8, 1, 1000);
        $status = $this->faker->randomElement([
            TopUpRecord::STATUS_PENDING,
            TopUpRecord::STATUS_COMPLETED,
            TopUpRecord::STATUS_FAILED,
        ]);

        return [
            'user_id' => User::factory(),
            'transaction_number' => 'TXN'.date('YmdHis').str_pad($this->faker->numberBetween(0, 9999), 4, '0', STR_PAD_LEFT),
            'amount' => $amount,
            'remaining_amount' => $status === TopUpRecord::STATUS_COMPLETED ? $this->faker->randomFloat(8, 0, $amount) : 0,
            'status' => $status,
            'payment_method' => $this->faker->randomElement([
                'alipay',
                'wechat',
                'bank_card',
                'manual',
                'test_payment',
            ]),
            'payment_gateway_response' => $status !== TopUpRecord::STATUS_PENDING ? [
                'gateway_order_id' => $this->faker->uuid,
                'gateway_transaction_id' => $this->faker->uuid,
                'response_time' => now()->toISOString(),
            ] : null,
            'completed_at' => $status === TopUpRecord::STATUS_COMPLETED ? $this->faker->dateTimeBetween('-1 month', 'now') : null,
            'refunded_at' => null,
            'refund_amount' => null,
            'remark' => $this->faker->optional(0.3)->sentence(),
        ];
    }

    /**
     * 待支付状态
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TopUpRecord::STATUS_PENDING,
            'remaining_amount' => 0,
            'payment_gateway_response' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * 已完成状态
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TopUpRecord::STATUS_COMPLETED,
            'remaining_amount' => $attributes['amount'],
            'completed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'payment_gateway_response' => [
                'gateway_order_id' => $this->faker->uuid,
                'gateway_transaction_id' => $this->faker->uuid,
                'response_time' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * 失败状态
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TopUpRecord::STATUS_FAILED,
            'remaining_amount' => 0,
            'payment_gateway_response' => [
                'error_code' => 'PAYMENT_FAILED',
                'error_message' => $this->faker->sentence(),
                'response_time' => now()->toISOString(),
            ],
            'completed_at' => null,
        ]);
    }

    /**
     * 已退款状态
     */
    public function refunded(): static
    {
        return $this->state(function (array $attributes) {
            $refundAmount = $this->faker->randomFloat(8, 1, $attributes['amount']);

            return [
                'status' => TopUpRecord::STATUS_REFUNDED,
                'remaining_amount' => $attributes['amount'] - $refundAmount,
                'refund_amount' => $refundAmount,
                'refunded_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
                'completed_at' => $this->faker->dateTimeBetween('-1 month', '-1 week'),
            ];
        });
    }
}
