<?php

namespace Database\Factories;

use App\Models\Cluster;
use App\Models\IpPool;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\IpPool>
 */
class IpPoolFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ipVersion = $this->faker->randomElement([
            IpPool::IP_VERSION_IPV4,
            IpPool::IP_VERSION_IPV6,
            IpPool::IP_VERSION_DUAL,
        ]);

        return [
            'cluster_id' => Cluster::factory(),
            'name' => $this->faker->unique()->word.'-pool',
            'description' => $this->faker->sentence(),
            'driver' => 'metallb', // 默认驱动
            'allocation_strategy' => $this->faker->randomElement([
                IpPool::STRATEGY_LEAST_USED,
                IpPool::STRATEGY_ROUND_ROBIN,
                IpPool::STRATEGY_RANDOM,
            ]),
            'is_active' => true,
            // 网关配置
            'gateway_v4' => $ipVersion !== IpPool::IP_VERSION_IPV6 ? $this->faker->ipv4() : null,
            'gateway_v6' => $ipVersion !== IpPool::IP_VERSION_IPV4 ? $this->faker->ipv6() : null,
            // IP 版本和子网信息
            'ip_version' => $ipVersion,
            'subnet_v4' => $ipVersion !== IpPool::IP_VERSION_IPV6 ? $this->faker->ipv4().'/24' : null,
            'subnet_v6' => $ipVersion !== IpPool::IP_VERSION_IPV4 ? $this->faker->ipv6().'/120' : null,
            // PureLB 同步状态
            'synced_to_k8s' => false,
            'last_sync_at' => null,
            'sync_error' => null,
        ];
    }

    /**
     * 创建不活跃的 IP 池
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * 设置特定的分配策略
     */
    public function withStrategy(string $strategy): static
    {
        return $this->state(fn (array $attributes) => [
            'allocation_strategy' => $strategy,
        ]);
    }

    /**
     * 创建 IPv4 IP 池
     */
    public function ipv4(): static
    {
        return $this->state(fn (array $attributes) => [
            'ip_version' => IpPool::IP_VERSION_IPV4,
            'gateway_v4' => $this->faker->ipv4(),
            'gateway_v6' => null,
            'subnet_v4' => $this->faker->ipv4().'/24',
            'subnet_v6' => null,
        ]);
    }

    /**
     * 创建 IPv6 IP 池
     */
    public function ipv6(): static
    {
        return $this->state(fn (array $attributes) => [
            'ip_version' => IpPool::IP_VERSION_IPV6,
            'gateway_v4' => null,
            'gateway_v6' => $this->faker->ipv6(),
            'subnet_v4' => null,
            'subnet_v6' => $this->faker->ipv6().'/120',
        ]);
    }

    /**
     * 创建双栈 IP 池
     */
    public function dual(): static
    {
        return $this->state(fn (array $attributes) => [
            'ip_version' => IpPool::IP_VERSION_DUAL,
            'gateway_v4' => $this->faker->ipv4(),
            'gateway_v6' => $this->faker->ipv6(),
            'subnet_v4' => $this->faker->ipv4().'/24',
            'subnet_v6' => $this->faker->ipv6().'/120',
        ]);
    }

    /**
     * 设置为已同步状态
     */
    public function synced(): static
    {
        return $this->state(fn (array $attributes) => [
            'synced_to_k8s' => true,
            'last_sync_at' => now(),
            'sync_error' => null,
        ]);
    }
}
