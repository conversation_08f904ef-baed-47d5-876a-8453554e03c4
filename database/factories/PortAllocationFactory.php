<?php

namespace Database\Factories;

use App\Models\PoolIp;
use App\Models\PortAllocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PortAllocation>
 */
class PortAllocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'pool_ip_id' => PoolIp::factory(),
            'port' => $this->faker->numberBetween(10000, 30000),
            'service_name' => $this->faker->slug(2),
            'namespace' => 'ns-'.$this->faker->slug(1),
            'status' => PortAllocation::STATUS_ALLOCATED,
            'allocated_at' => now(),
        ];
    }

    /**
     * 设置为已释放状态
     */
    public function released(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => PortAllocation::STATUS_RELEASED,
            'released_at' => now(),
        ]);
    }

    /**
     * 设置特定端口
     */
    public function withPort(int $port): static
    {
        return $this->state(fn (array $attributes) => [
            'port' => $port,
        ]);
    }

    /**
     * 设置特定服务
     */
    public function forService(string $serviceName, string $namespace): static
    {
        return $this->state(fn (array $attributes) => [
            'service_name' => $serviceName,
            'namespace' => $namespace,
        ]);
    }
}
