<?php

namespace Database\Factories;

use App\Models\Cluster;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cluster>
 */
class ClusterFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Cluster::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->word().'-cluster',
            'server_url' => 'https://'.$this->faker->domainName().':6443',
            'certificate_authority_data' => base64_encode($this->faker->text(100)),
            'auth_type' => $this->faker->randomElement(['token', 'certificate', 'username_password']),
            'client_certificate_data' => null,
            'client_key_data' => null,
            'token' => null,
            'username' => null,
            'password' => null,
            'insecure_skip_tls_verify' => $this->faker->boolean(),
        ];
    }

    /**
     * Configure the factory for token authentication.
     */
    public function token(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'auth_type' => 'token',
                'token' => 'token-'.$this->faker->uuid(),
                'client_certificate_data' => null,
                'client_key_data' => null,
                'username' => null,
                'password' => null,
            ];
        });
    }

    /**
     * Configure the factory for certificate authentication.
     */
    public function certificate(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'auth_type' => 'certificate',
                'client_certificate_data' => base64_encode($this->faker->text(200)),
                'client_key_data' => base64_encode($this->faker->text(200)),
                'token' => null,
                'username' => null,
                'password' => null,
            ];
        });
    }

    /**
     * Configure the factory for username/password authentication.
     */
    public function usernamePassword(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'auth_type' => 'username_password',
                'username' => $this->faker->userName(),
                'password' => $this->faker->password(),
                'token' => null,
                'client_certificate_data' => null,
                'client_key_data' => null,
            ];
        });
    }

    /**
     * Configure the factory for insecure clusters.
     */
    public function insecure(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'insecure_skip_tls_verify' => true,
                'certificate_authority_data' => null,
            ];
        });
    }
}
