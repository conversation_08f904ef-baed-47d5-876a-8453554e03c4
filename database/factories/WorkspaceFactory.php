<?php

namespace Database\Factories;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Workspace>
 */
class WorkspaceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Workspace::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->unique()->lexify('??????');

        return [
            'user_id' => User::factory(),
            'cluster_id' => Cluster::factory(),
            'name' => $name,
            'status' => $this->faker->randomElement(['active', 'suspended', 'pending']),
            'namespace' => 'workspace-'.$name,
            'suspended_at' => null,
            'suspension_reason' => null,
            'overdue_amount' => null,
            'last_overdue_at' => null,
        ];
    }

    /**
     * Configure the factory for active workspace.
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'suspended_at' => null,
                'suspension_reason' => null,
            ];
        });
    }

    /**
     * Configure the factory for suspended workspace.
     */
    public function suspended(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'suspended',
                'suspended_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
                'suspension_reason' => $this->faker->randomElement([
                    'payment_overdue',
                    'policy_violation',
                    'resource_abuse',
                    'manual_suspension',
                ]),
            ];
        });
    }

    /**
     * Configure the factory for overdue workspace.
     */
    public function overdue(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'suspended',
                'suspended_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
                'suspension_reason' => 'payment_overdue',
                'overdue_amount' => $this->faker->randomFloat(2, 10, 1000),
                'last_overdue_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            ];
        });
    }

    /**
     * Configure the factory for pending workspace.
     */
    public function pending(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'suspended_at' => null,
                'suspension_reason' => null,
            ];
        });
    }
}
