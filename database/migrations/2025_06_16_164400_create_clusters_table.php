<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clusters', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('server_url');
            $table->text('certificate_authority_data')->nullable();
            $table->string('auth_type'); // token, certificate, username_password
            $table->text('client_certificate_data')->nullable();
            $table->text('client_key_data')->nullable();
            $table->text('token')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->boolean('insecure_skip_tls_verify')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clusters');
    }
};
