<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workspaces', function (Blueprint $table) {
            $table->timestamp('suspended_at')->nullable()->after('namespace');
            $table->string('suspension_reason')->nullable()->after('suspended_at');
            $table->decimal('overdue_amount', 10, 4)->nullable()->after('suspension_reason');
            $table->timestamp('last_overdue_at')->nullable()->after('overdue_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workspaces', function (Blueprint $table) {
            $table->dropColumn([
                'suspended_at',
                'suspension_reason',
                'overdue_amount',
                'last_overdue_at',
            ]);
        });
    }
};
