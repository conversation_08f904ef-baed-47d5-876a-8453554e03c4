<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ip_pools', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cluster_id')->constrained()->onDelete('cascade');
            $table->string('name')->comment('IP 池名称，也是 PureLB 的 service group');
            $table->text('description')->nullable()->comment('IP 池描述');
            $table->enum('allocation_strategy', ['least_used', 'round_robin', 'random'])
                ->default('least_used')
                ->comment('IP 分配策略：最少使用、轮询、随机');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();

            // 在同一集群中，IP 池名称必须唯一
            $table->unique(['cluster_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ip_pools');
    }
};
