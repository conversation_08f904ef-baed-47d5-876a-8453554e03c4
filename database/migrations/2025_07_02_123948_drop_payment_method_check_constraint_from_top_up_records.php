<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 直接删除 payment_method 的 check 约束
        DB::statement('ALTER TABLE top_up_records DROP CONSTRAINT IF EXISTS top_up_records_payment_method_check');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原有的 enum 约束
        DB::statement("ALTER TABLE top_up_records ADD CONSTRAINT top_up_records_payment_method_check CHECK (payment_method IN ('alipay', 'wechat', 'bank_card', 'manual', 'redeem_code'))");
    }
};
