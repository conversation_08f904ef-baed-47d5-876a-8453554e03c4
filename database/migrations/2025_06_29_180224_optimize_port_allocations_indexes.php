<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('port_allocations', function (Blueprint $table) {
            // 为获取已释放端口添加复合索引（按释放时间倒序）
            $table->index(['pool_ip_id', 'status', 'is_disabled', 'released_at'], 'idx_port_alloc_released');

            // 为获取最大端口号添加索引
            $table->index(['pool_ip_id', 'status', 'port'], 'idx_port_alloc_max_port');

            // 为端口范围查询添加索引
            $table->index(['pool_ip_id', 'port', 'status', 'is_disabled'], 'idx_port_alloc_range');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('port_allocations', function (Blueprint $table) {
            $table->dropIndex('idx_port_alloc_released');
            $table->dropIndex('idx_port_alloc_max_port');
            $table->dropIndex('idx_port_alloc_range');
        });
    }
};
