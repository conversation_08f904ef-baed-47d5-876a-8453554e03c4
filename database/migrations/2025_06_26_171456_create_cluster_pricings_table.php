<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cluster_pricings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cluster_id')->constrained()->onDelete('cascade');

            // 资源定价（按月）
            $table->decimal('memory_price_per_gb', 12, 4)->comment('内存价格/GB/月');
            $table->decimal('cpu_price_per_core', 12, 4)->comment('CPU价格/核/月');
            $table->decimal('storage_price_per_gb', 12, 4)->comment('存储价格/GB/月');
            $table->decimal('loadbalancer_price_per_service', 12, 4)->comment('LoadBalancer服务价格/个/月');

            // 计费开关
            $table->boolean('billing_enabled')->default(false)->comment('是否开启计费');

            // 备注
            $table->text('description')->nullable()->comment('定价说明');

            $table->timestamps();

            // 确保每个集群只有一个定价配置
            $table->unique('cluster_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cluster_pricings');
    }
};
