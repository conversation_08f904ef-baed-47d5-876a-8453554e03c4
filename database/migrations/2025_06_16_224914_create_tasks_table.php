<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();

            // Polymorphic 关系
            $table->morphs('taskable');

            // 任务类型
            $table->string('type');

            // 任务状态：pending, running, completed, failed
            $table->string('status')->default('pending')->index();

            // 任务数据（JSON）
            $table->json('data')->nullable();

            // 执行结果
            $table->json('result')->nullable();

            // 错误信息
            $table->text('error_message')->nullable();

            // 重试次数
            $table->unsignedInteger('attempts')->default(0);

            // 最大重试次数
            $table->unsignedInteger('max_attempts')->default(3);

            // 开始时间
            $table->timestamp('started_at')->nullable();

            // 完成时间
            $table->timestamp('completed_at')->nullable();

            $table->timestamps();

            // Workspace 的 ID
            $table->foreignId('workspace_id')->index()->nullable()->constrained()->nullOnDelete();

            // 索引
            $table->index(['taskable_type', 'taskable_id', 'status']);
            $table->index(['type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
