<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->string('driver')->default('metallb')->after('cluster_id')->comment('负载均衡器驱动');
            if (Schema::hasColumn('ip_pools', 'service_group_name')) {
                $table->dropColumn('service_group_name');
            }
            if (Schema::hasColumn('ip_pools', 'service_group_namespace')) {
                $table->dropColumn('service_group_namespace');
            }
            if (Schema::hasColumn('ip_pools', 'aggregation')) {
                $table->dropColumn('aggregation');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->dropColumn('driver');
            $table->string('service_group_name')->nullable()->comment('PureLB ServiceGroup 名称，默认使用 IP 池名称');
            $table->string('service_group_namespace')->default('purelb')->comment('PureLB ServiceGroup 命名空间');
            $table->string('aggregation')->default('default')->comment('PureLB 聚合策略');
        });
    }
};
