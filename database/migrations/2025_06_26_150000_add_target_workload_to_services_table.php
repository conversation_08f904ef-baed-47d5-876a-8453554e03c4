<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->string('target_workload_type')->nullable()->after('selector')->comment('目标工作负载类型');
            $table->string('target_workload_name')->nullable()->after('target_workload_type')->comment('目标工作负载名称');

            // 添加索引以便于查询
            $table->index(['workspace_id', 'target_workload_type', 'target_workload_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex(['workspace_id', 'target_workload_type', 'target_workload_name']);
            $table->dropColumn(['target_workload_type', 'target_workload_name']);
        });
    }
};
