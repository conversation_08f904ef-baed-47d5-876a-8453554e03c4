<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('top_up_records', function (Blueprint $table) {
            $table->string('payment_method')->nullable()->default('manual')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('top_up_records', function (Blueprint $table) {
            $table->enum('payment_method', ['alipay', 'wechat', 'bank_card', 'manual', 'redeem_code'])->change();
        });
    }
};
