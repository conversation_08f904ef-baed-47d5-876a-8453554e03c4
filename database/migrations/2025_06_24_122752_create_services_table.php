<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->string('name')->comment('Service 名称');
            $table->enum('type', ['ClusterIP', 'LoadBalancer'])->comment('Service 类型');
            $table->json('ports')->comment('端口配置');
            $table->json('selector')->comment('选择器');
            $table->string('session_affinity')->default('None')->comment('会话亲和性');

            // LoadBalancer 相关字段
            $table->foreignId('pool_ip_id')->nullable()->constrained()->onDelete('set null')->comment('分配的 IP');
            $table->string('allocated_ip')->nullable()->comment('分配的 IP 地址');
            $table->integer('allocated_port')->nullable()->comment('分配的端口');
            $table->string('external_traffic_policy')->default('Cluster')->comment('外部流量策略');
            $table->boolean('allow_shared_ip')->default(false)->comment('是否允许共享 IP');

            $table->enum('status', ['pending', 'active', 'failed'])->default('pending')->comment('Service 状态');
            $table->text('status_message')->nullable()->comment('状态消息');
            $table->timestamps();

            // 在同一工作空间中，Service 名称必须唯一
            $table->unique(['workspace_id', 'name']);

            // 为状态查询添加索引
            $table->index(['workspace_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
