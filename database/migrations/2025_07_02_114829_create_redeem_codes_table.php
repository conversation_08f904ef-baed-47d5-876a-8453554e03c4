<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('redeem_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 32)->unique()->comment('兑换码');
            $table->decimal('amount', 15, 8)->comment('兑换金额');
            $table->unsignedInteger('max_uses')->default(1)->comment('最大使用次数');
            $table->unsignedInteger('used_count')->default(0)->comment('已使用次数');
            $table->datetime('expires_at')->nullable()->comment('过期时间');
            $table->string('description')->nullable()->comment('描述');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();

            $table->index(['code', 'is_active']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('redeem_codes');
    }
};
