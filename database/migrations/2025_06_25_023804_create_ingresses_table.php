<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ingresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->string('name', 63)->comment('Ingress 名称');
            $table->string('ingress_class', 20)->default('traefik')->comment('Ingress 类型（nginx/traefik）');
            $table->json('domains')->comment('域名列表，包含域名和路径信息');
            $table->json('tls_config')->nullable()->comment('TLS 配置');
            $table->json('annotations')->nullable()->comment('自定义注解');
            $table->json('labels')->nullable()->comment('自定义标签');
            $table->enum('status', ['pending', 'active', 'failed'])->default('pending')->comment('状态');
            $table->text('status_message')->nullable()->comment('状态消息');
            $table->timestamps();

            // 索引
            $table->unique(['workspace_id', 'name'], 'ingresses_workspace_name_unique');
            $table->index('status');
            $table->index('ingress_class');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ingresses');
    }
};
