<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->morphs('settingable'); // 多态关联，可以关联到 User 或 Team
            $table->string('key')->index(); // 设置的键
            $table->text('value')->nullable(); // 设置的值
            $table->timestamps();

            // 确保每个模型的每个设置键只有一个值
            $table->unique(['settingable_type', 'settingable_id', 'key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
