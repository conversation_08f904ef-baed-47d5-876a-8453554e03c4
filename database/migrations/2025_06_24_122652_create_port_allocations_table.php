<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('port_allocations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pool_ip_id')->constrained()->onDelete('cascade');
            $table->integer('port')->comment('分配的端口号');
            $table->string('service_name')->comment('使用此端口的 Service 名称');
            $table->string('namespace')->comment('Service 所在的命名空间');
            $table->enum('status', ['allocated', 'released'])->default('allocated')->comment('分配状态');
            $table->timestamp('allocated_at')->useCurrent()->comment('分配时间');
            $table->timestamp('released_at')->nullable()->comment('释放时间');
            $table->timestamps();

            // 同一个 IP 上的端口必须唯一（只考虑已分配的）
            $table->unique(['pool_ip_id', 'port']);

            // 为 Service 查找添加索引
            $table->index(['service_name', 'namespace']);

            // 为状态查询添加索引
            $table->index(['pool_ip_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('port_allocations');
    }
};
