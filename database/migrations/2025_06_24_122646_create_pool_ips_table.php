<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pool_ips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ip_pool_id')->constrained()->onDelete('cascade');
            $table->string('ip_address')->comment('IP 地址');
            $table->integer('port_range_start')->default(10000)->comment('端口范围开始');
            $table->integer('port_range_end')->default(30000)->comment('端口范围结束');
            $table->integer('usage_count')->default(0)->comment('使用次数，用于最少使用策略');
            $table->boolean('is_active')->default(true)->comment('是否可用');
            $table->timestamps();

            // 同一个 IP 池中，IP 地址必须唯一
            $table->unique(['ip_pool_id', 'ip_address']);

            // 为使用次数添加索引，优化查询
            $table->index(['ip_pool_id', 'usage_count', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pool_ips');
    }
};
