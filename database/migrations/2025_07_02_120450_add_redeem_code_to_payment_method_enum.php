<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 修改 payment_method 列，添加 redeem_code 选项
        Schema::table('top_up_records', function (Blueprint $table) {
            $table->dropColumn('payment_method');
        });

        Schema::table('top_up_records', function (Blueprint $table) {
            $table->enum('payment_method', ['alipay', 'wechat', 'bank_card', 'manual', 'redeem_code'])
                ->nullable()->comment('支付方式')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('top_up_records', function (Blueprint $table) {
            $table->dropColumn('payment_method');
        });

        Schema::table('top_up_records', function (Blueprint $table) {
            $table->enum('payment_method', ['alipay', 'wechat', 'bank_card', 'manual'])
                ->nullable()->comment('支付方式')->after('status');
        });
    }
};
