<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 对于 PostgreSQL，需要使用原生 SQL 来修改枚举
        DB::statement('ALTER TABLE services DROP CONSTRAINT services_type_check');
        DB::statement("ALTER TABLE services ADD CONSTRAINT services_type_check CHECK (type IN ('ClusterIP', 'NodePort', 'LoadBalancer'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复为原来的枚举值
        DB::statement('ALTER TABLE services DROP CONSTRAINT services_type_check');
        DB::statement("ALTER TABLE services ADD CONSTRAINT services_type_check CHECK (type IN ('ClusterIP', 'LoadBalancer'))");
    }
};
