<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workspaces', function (Blueprint $table) {
            $table->id();

            // user_id
            $table->foreignId('user_id')->index()->constrained('users');
            // 集群 ID
            $table->foreignId('cluster_id')->index()->constrained('clusters');

            // name
            $table->string('name');

            // 工作区的状态，有 pending, deleting, active
            $table->string('status')->default('pending')->index();

            // 实际的 namespace
            $table->string('namespace')->nullable()->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workspaces');
    }
};
