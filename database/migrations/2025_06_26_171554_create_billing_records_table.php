<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('cluster_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // 计费时间段
            $table->timestamp('billing_start_at')->comment('计费开始时间');
            $table->timestamp('billing_end_at')->comment('计费结束时间');

            // 资源使用情况
            $table->json('resource_usage')->comment('资源使用详情JSON');

            // 计费详情
            $table->decimal('memory_cost', 10, 8)->default(0)->comment('内存费用');
            $table->decimal('cpu_cost', 10, 8)->default(0)->comment('CPU费用');
            $table->decimal('storage_cost', 10, 8)->default(0)->comment('存储费用');
            $table->decimal('loadbalancer_cost', 10, 8)->default(0)->comment('LoadBalancer费用');
            $table->decimal('total_cost', 10, 8)->comment('总费用');

            // 状态
            $table->enum('status', ['pending', 'charged', 'failed'])->default('pending')->comment('计费状态');
            $table->timestamp('charged_at')->nullable()->comment('扣费时间');

            $table->timestamps();

            // 索引
            $table->index(['workspace_id', 'billing_start_at']);
            $table->index(['user_id', 'status']);
            $table->index(['billing_start_at', 'billing_end_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_records');
    }
};
