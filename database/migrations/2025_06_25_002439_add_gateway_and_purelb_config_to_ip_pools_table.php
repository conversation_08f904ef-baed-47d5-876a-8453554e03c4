<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            // 网关配置
            $table->string('gateway_v4')->nullable()->comment('IPv4 网关地址');
            $table->string('gateway_v6')->nullable()->comment('IPv6 网关地址');

            // PureLB ServiceGroup 配置
            $table->string('service_group_name')->nullable()->comment('PureLB ServiceGroup 名称，默认使用 IP 池名称');
            $table->string('service_group_namespace')->default('purelb')->comment('PureLB ServiceGroup 命名空间');
            $table->string('aggregation')->default('default')->comment('PureLB 聚合策略');

            // IP 版本和子网信息
            $table->enum('ip_version', ['ipv4', 'ipv6', 'dual'])->default('ipv4')->comment('IP 版本：IPv4、IPv6 或双栈');
            $table->string('subnet_v4')->nullable()->comment('IPv4 子网 CIDR');
            $table->string('subnet_v6')->nullable()->comment('IPv6 子网 CIDR');

            // PureLB 同步状态
            $table->boolean('synced_to_k8s')->default(false)->comment('是否已同步到 K8s PureLB');
            $table->timestamp('last_sync_at')->nullable()->comment('最后同步时间');
            $table->text('sync_error')->nullable()->comment('同步错误信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->dropColumn([
                'gateway_v4',
                'gateway_v6',
                'service_group_name',
                'service_group_namespace',
                'aggregation',
                'ip_version',
                'subnet_v4',
                'subnet_v6',
                'synced_to_k8s',
                'last_sync_at',
                'sync_error',
            ]);
        });
    }
};
