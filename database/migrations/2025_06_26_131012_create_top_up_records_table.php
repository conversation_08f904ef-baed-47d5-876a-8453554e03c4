<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('top_up_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('transaction_number')->unique()->comment('流水号');
            $table->decimal('amount', 16, 8)->comment('充值金额');
            $table->decimal('remaining_amount', 16, 8)->default(0)->comment('剩余金额');
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded', 'partial_refunded'])
                ->default('pending')->comment('状态');
            $table->enum('payment_method', ['alipay', 'wechat', 'bank_card', 'manual'])
                ->nullable()->comment('支付方式');
            $table->json('payment_gateway_response')->nullable()->comment('支付网关响应');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamp('refunded_at')->nullable()->comment('退款时间');
            $table->decimal('refund_amount', 16, 8)->nullable()->comment('退款金额');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'remaining_amount']);
            $table->index('transaction_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('top_up_records');
    }
};
