<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('redeem_code_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('用户ID');
            $table->foreignId('redeem_code_id')->constrained()->onDelete('cascade')->comment('兑换码ID');
            $table->decimal('amount', 15, 8)->comment('兑换金额');
            $table->string('ip_address')->nullable()->comment('使用者IP地址');
            $table->string('user_agent')->nullable()->comment('用户代理');
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['redeem_code_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('redeem_code_usages');
    }
};
