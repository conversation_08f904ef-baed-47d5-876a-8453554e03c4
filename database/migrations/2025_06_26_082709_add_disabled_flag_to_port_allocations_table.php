<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('port_allocations', function (Blueprint $table) {
            $table->string('service_name')->nullable()->change();
            $table->string('namespace')->nullable()->change();
            $table->boolean('is_disabled')->default(false)->after('status');
            $table->text('reason')->nullable()->after('is_disabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('port_allocations', function (Blueprint $table) {
            $table->string('service_name')->nullable(false)->change();
            $table->string('namespace')->nullable(false)->change();
            $table->dropColumn(['is_disabled', 'reason']);
        });
    }
};
