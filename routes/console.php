<?php

use Illuminate\Support\Facades\Schedule;

// 计费流水线任务 - 包含资源计费、扣费、欠费恢复等所有计费功能
Schedule::command('billing:pipeline --force')
    ->everyMinute()
    ->name('billing-pipeline')
    ->description('每分钟执行完整计费流水线：资源收集、成本计算、计费记录、余额扣费、欠费处理')
    ->onOneServer()
    ->withoutOverlapping()
    ->runInBackground();

// 清理过期工作空间任务 - 删除长期欠费的工作空间
// Schedule::command('billing:cleanup-overdue-workspaces')
//     ->daily()
//     ->at('02:00')
//     ->name('billing-cleanup-overdue-workspaces')
//     ->description('每天凌晨2点清理长期欠费的过期工作空间')
//     ->onOneServer()
//     ->withoutOverlapping()
//     ->runInBackground();

// 证书缓存清理任务
Schedule::call(function () {
    \App\Service\CertificateCacheService::cleanupExpiredCertificates();
})->weekly()->mondays()->at('03:00')
    ->name('cleanup-certificate-cache')
    ->description('每周一凌晨3点清理过期的证书缓存文件')
    ->onOneServer();
