<?php

use App\Http\Controllers\ApplyController;
use App\Http\Controllers\BalanceController;
use App\Http\Controllers\ConfigMapController;
use App\Http\Controllers\DeploymentController;
use App\Http\Controllers\HorizontalPodAutoscalerController;
use App\Http\Controllers\IngressController;
use App\Http\Controllers\OverviewController;
use App\Http\Controllers\PaymentCallbackController;
use App\Http\Controllers\PodController;
use App\Http\Controllers\SecretController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\Settings\ProfileController;
use App\Http\Controllers\StatefulSetController;
use App\Http\Controllers\StorageController;
use App\Http\Controllers\WorkspaceController;
use App\Http\Middleware\MustInWorkspace;
use App\Http\Middleware\WorkspaceMustActive;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('workspaces', WorkspaceController::class);
    Route::post('/workspaces/{workspace}/set-current', [WorkspaceController::class, 'setCurrent'])->name('workspaces.set-current');

    // 需要在工作空间中的路由
    Route::middleware([MustInWorkspace::class, WorkspaceMustActive::class])->group(function () {
        Route::get('/dashboard', OverviewController::class)->name('dashboard');

        // Deployment 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('deployments', DeploymentController::class)->only(['index', 'create', 'show', 'edit']);

        // StatefulSet 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('statefulsets', StatefulSetController::class)->only(['index', 'create', 'show', 'edit']);

        // Secret 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('secrets', SecretController::class)->only(['index', 'create', 'show', 'edit']);

        // ConfigMap 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('configmaps', ConfigMapController::class)->only(['index', 'create', 'show', 'edit']);

        // Storage 管理路由 - 只有 index, create 方法
        Route::resource('storages', StorageController::class)->only(['index', 'create']);

        // Service 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('services', ServiceController::class)->only(['index', 'create', 'show', 'edit']);

        // Ingress 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('ingresses', IngressController::class)->only(['index', 'create', 'show', 'edit']);

        // Pod 管理路由 - 只有 index, show 方法
        Route::resource('pods', PodController::class)->only(['index', 'show']);
        Route::get('pods/{name}/logs', [PodController::class, 'logs'])->name('pods.logs');
        Route::get('pods/{name}/terminal', [PodController::class, 'terminal'])->name('pods.terminal');

        // HorizontalPodAutoscaler 管理路由 - 只有 index, create, show, edit 方法
        Route::resource('hpas', HorizontalPodAutoscalerController::class)->only(['index', 'create', 'show', 'edit']);

        // Apply YAML 资源应用
        Route::get('/apply', [ApplyController::class, 'index'])->name('apply.index');

        // Settings
        Route::get('/settings/profile', [ProfileController::class, 'edit'])->name('settings.profile.edit');
        Route::patch('/settings/profile', [ProfileController::class, 'update'])->name('settings.profile.update');
    });

    // Balance
    Route::get('/balance', [BalanceController::class, 'index'])->name('balance.index');
    Route::post('/balance/top-up', [BalanceController::class, 'topUp'])->name('balance.top-up');
    Route::post('/balance/redeem', [BalanceController::class, 'redeem'])->name('balance.redeem');

});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// 支付回调路由（不需要认证）
Route::any('/payment/callback/{gateway}', [PaymentCallbackController::class, 'handleCallback'])
    ->name('payment.callback');
