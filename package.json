{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.5", "@vue/eslint-config-typescript": "^14.3.0", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "laravel-echo": "^2.1.6", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "typescript-eslint": "^8.23.0", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/vite": "^4.1.1", "@tanstack/vue-table": "^8.21.3", "@types/js-yaml": "^4.0.9", "@vee-validate/zod": "^4.15.1", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^12.8.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "echarts": "^5.6.0", "js-yaml": "^4.1.0", "laravel-vite-plugin": "^1.0", "lucide-vue-next": "^0.468.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "reka-ui": "^2.3.2", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.2.2", "vee-validate": "^4.15.1", "vite": "^6.2.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-sonner": "^2.0.1", "ziggy-js": "^2.4.2", "zod": "^3.25.67"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}